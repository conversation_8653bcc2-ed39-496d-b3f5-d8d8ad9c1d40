// server/game/GameManager.js
import { Player } from './Player.js';
import { WorldManager } from './WorldManager.js';
import { MESSAGE_TYPES, GAME_CONFIG } from '../../shared/constants.js';

export class GameManager {
    constructor() {
        this.players = new Map(); // Map de playerId -> Player instance
        this.worldManager = new WorldManager();
        this.webSocketServer = null; // Sera défini par le WebSocketServer
        
        this.isRunning = false;
        this.tickInterval = null;
        this.lastTickTime = Date.now();
        this.tickCount = 0;
        
        // Statistiques de performance
        this.stats = {
            playersConnected: 0,
            ticksPerSecond: 0,
            averageTickTime: 0,
            lastStatsUpdate: Date.now()
        };
        
        console.log('🎮 GameManager initialisé');
    }
    
    start() {
        if (this.isRunning) {
            console.warn('⚠️ GameManager déjà en cours d\'exécution');
            return;
        }
        
        this.isRunning = true;
        this.lastTickTime = Date.now();
        
        // Démarrer la boucle de jeu principale
        this.tickInterval = setInterval(() => {
            this.update();
        }, 1000 / GAME_CONFIG.TICK_RATE);
        
        console.log(`🎮 Boucle de jeu serveur démarrée (${GAME_CONFIG.TICK_RATE} ticks/s)`);
        
        // Démarrer les statistiques
        this.startStatsReporting();
    }
    
    stop() {
        if (!this.isRunning) {
            return;
        }
        
        this.isRunning = false;
        
        if (this.tickInterval) {
            clearInterval(this.tickInterval);
            this.tickInterval = null;
        }
        
        console.log('🛑 GameManager arrêté');
    }
    
    // Boucle de mise à jour principale du serveur
    update() {
        const now = Date.now();
        const deltaTime = (now - this.lastTickTime) / 1000; // en secondes
        this.lastTickTime = now;
        this.tickCount++;
        
        // 1. Mettre à jour la physique de chaque joueur
        this.players.forEach(player => {
            player.update(deltaTime, this.worldManager);
        });
        
        // 2. Mettre à jour le monde (génération de chunks, etc.)
        this.worldManager.update(this.getPlayerPositions());
        
        // 3. Diffuser l'état mis à jour à tous les clients (moins fréquent que les ticks)
        if (this.tickCount % Math.floor(GAME_CONFIG.TICK_RATE / GAME_CONFIG.NETWORK_UPDATE_RATE) === 0) {
            this.broadcastWorldState();
        }
        
        // 4. Mettre à jour les statistiques
        this.updateStats(deltaTime);
    }
    
    broadcastWorldState() {
        if (!this.webSocketServer || this.players.size === 0) {
            return;
        }
        
        const worldState = {
            players: Array.from(this.players.values()).map(p => p.getNetworkState()),
            serverTime: Date.now(),
            tick: this.tickCount
        };
        
        this.webSocketServer.broadcast({
            type: MESSAGE_TYPES.SERVER_WORLD_UPDATE,
            payload: worldState
        });
    }
    
    // Gestion des événements de joueurs
    handlePlayerJoin(playerId, webSocketServer) {
        console.log(`👤 Player ${playerId} a rejoint le jeu`);
        
        // Stocker la référence au WebSocketServer si pas encore fait
        if (!this.webSocketServer) {
            this.webSocketServer = webSocketServer;
        }
        
        // Créer le joueur
        const player = new Player(playerId);
        this.players.set(playerId, player);
        
        // Trouver une position de spawn sûre
        const spawnPosition = this.worldManager.findSafeSpawnPosition();
        player.setPosition(spawnPosition.x, spawnPosition.y, spawnPosition.z);
        
        // Envoyer l'état initial du monde au nouveau joueur
        this.sendInitialWorldState(playerId);
        
        // Notifier les autres joueurs
        this.webSocketServer.broadcast({
            type: MESSAGE_TYPES.SERVER_PLAYER_JOIN,
            payload: {
                playerId,
                playerState: player.getNetworkState()
            }
        }, playerId);
        
        this.stats.playersConnected = this.players.size;
    }
    
    handlePlayerLeave(playerId) {
        console.log(`👋 Player ${playerId} a quitté le jeu`);
        
        if (this.players.has(playerId)) {
            this.players.delete(playerId);
            
            // Notifier les autres joueurs
            if (this.webSocketServer) {
                this.webSocketServer.broadcast({
                    type: MESSAGE_TYPES.SERVER_PLAYER_LEAVE,
                    payload: { playerId }
                });
            }
        }
        
        this.stats.playersConnected = this.players.size;
    }
    
    handlePlayerInput(playerId, input) {
        const player = this.players.get(playerId);
        if (player) {
            player.applyInput(input);
        }
    }
    
    handleMineStart(playerId, data) {
        const player = this.players.get(playerId);
        if (player) {
            const result = player.startMining(data.position, this.worldManager);
            if (result.success) {
                // Notifier le client que le minage a commencé
                this.webSocketServer.sendToClient(playerId, {
                    type: 'server:mineStart',
                    payload: result
                });
            }
        }
    }
    
    handleMineStop(playerId, data) {
        const player = this.players.get(playerId);
        if (player) {
            player.stopMining();
        }
    }
    
    handlePlaceBlock(playerId, data) {
        const player = this.players.get(playerId);
        if (player) {
            const result = this.worldManager.placeBlock(
                data.position.x, 
                data.position.y, 
                data.position.z, 
                data.blockType,
                player
            );
            
            if (result.success) {
                // Diffuser le changement à tous les joueurs dans la zone
                this.webSocketServer.broadcastToArea({
                    type: MESSAGE_TYPES.SERVER_BLOCK_UPDATE,
                    payload: {
                        position: data.position,
                        blockType: data.blockType,
                        action: 'place'
                    }
                }, data.position.x, data.position.z, GAME_CONFIG.DEFAULT_RENDER_DISTANCE * 16);
            }
        }
    }
    
    handleChat(playerId, data) {
        const player = this.players.get(playerId);
        if (player && this.webSocketServer) {
            // Diffuser le message de chat à tous les joueurs
            this.webSocketServer.broadcast({
                type: MESSAGE_TYPES.SERVER_CHAT,
                payload: {
                    playerId,
                    message: data.message,
                    timestamp: Date.now()
                }
            });
        }
    }
    
    // Utilitaires
    sendInitialWorldState(playerId) {
        const player = this.players.get(playerId);
        if (!player || !this.webSocketServer) return;
        
        // Envoyer les chunks autour du joueur
        const playerChunk = this.worldManager.getChunkCoordinates(
            player.position.x, 
            player.position.z
        );
        
        const chunksToSend = this.worldManager.getChunksInRadius(
            playerChunk.x, 
            playerChunk.z, 
            GAME_CONFIG.DEFAULT_LOAD_DISTANCE
        );
        
        chunksToSend.forEach(chunkData => {
            this.webSocketServer.sendToClient(playerId, {
                type: MESSAGE_TYPES.SERVER_CHUNK_DATA,
                payload: chunkData
            });
        });
    }
    
    getPlayerPositions() {
        return Array.from(this.players.values()).map(player => ({
            x: player.position.x,
            z: player.position.z
        }));
    }
    
    getPlayer(playerId) {
        return this.players.get(playerId);
    }
    
    updateStats(deltaTime) {
        // Calculer les TPS (Ticks Per Second)
        const now = Date.now();
        if (now - this.stats.lastStatsUpdate >= 1000) {
            this.stats.ticksPerSecond = this.tickCount;
            this.tickCount = 0;
            this.stats.lastStatsUpdate = now;
        }
        
        this.stats.averageTickTime = deltaTime * 1000; // en ms
    }
    
    startStatsReporting() {
        setInterval(() => {
            if (this.isRunning) {
                console.log(`📊 Stats: ${this.stats.playersConnected} joueurs, ${this.stats.ticksPerSecond} TPS, ${this.stats.averageTickTime.toFixed(2)}ms/tick`);
            }
        }, 10000); // Toutes les 10 secondes
    }
}
