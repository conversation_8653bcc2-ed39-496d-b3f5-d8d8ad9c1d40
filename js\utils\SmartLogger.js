// Système de logs intelligent et compacté pour l'IA
export class SmartLogger {
    constructor() {
        this.logs = [];
        this.criticalErrors = [];
        this.patterns = new Map();
        this.summary = {
            totalLogs: 0,
            criticalCount: 0,
            warningCount: 0,
            infoCount: 0,
            debugCount: 0,
            physicsIssues: 0,
            chunkIssues: 0,
            lastUpdate: Date.now()
        };
        
        // Configuration de compactage
        this.maxLogs = 1000; // Limite de logs stockés
        this.compactInterval = 5000; // Compactage toutes les 5 secondes
        this.patternThreshold = 5; // Seuil pour détecter les patterns répétitifs
        
        // Démarrer le compactage automatique
        this.startAutoCompact();
    }

    // Méthode principale de logging avec intelligence
    log(level, category, message, data = {}) {
        const timestamp = Date.now();
        const logEntry = {
            id: this.summary.totalLogs++,
            timestamp,
            level,
            category,
            message,
            data,
            hash: this.generateHash(category, message)
        };

        // Détecter les patterns répétitifs
        this.detectPattern(logEntry);

        // Traitement selon le niveau
        switch (level.toLowerCase()) {
            case 'error':
            case 'critical':
                this.criticalErrors.push(logEntry);
                this.summary.criticalCount++;
                break;
            case 'warn':
            case 'warning':
                this.summary.warningCount++;
                break;
            case 'info':
                this.summary.infoCount++;
                break;
            case 'debug':
                this.summary.debugCount++;
                break;
        }

        // Analyse spécialisée
        this.analyzeSpecialCases(logEntry);

        // Ajouter aux logs si pas répétitif
        if (!this.isRepetitive(logEntry)) {
            this.logs.push(logEntry);
        }

        // Maintenir la limite de logs
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }

        // Log vers la console si critique
        if (level.toLowerCase() === 'error' || level.toLowerCase() === 'critical') {
            console.error(`[${category}] ${message}`, data);
        }
    }

    // Générer un hash pour détecter les doublons
    generateHash(category, message) {
        return `${category}:${message}`.replace(/\d+/g, 'X'); // Remplacer les nombres par X
    }

    // Détecter les patterns répétitifs
    detectPattern(logEntry) {
        const hash = logEntry.hash;
        if (!this.patterns.has(hash)) {
            this.patterns.set(hash, { count: 1, first: logEntry.timestamp, last: logEntry.timestamp });
        } else {
            const pattern = this.patterns.get(hash);
            pattern.count++;
            pattern.last = logEntry.timestamp;
        }
    }

    // Vérifier si un log est répétitif
    isRepetitive(logEntry) {
        const pattern = this.patterns.get(logEntry.hash);
        return pattern && pattern.count > this.patternThreshold;
    }

    // Analyser les cas spéciaux (physique, chunks, etc.)
    analyzeSpecialCases(logEntry) {
        const { category, message, data } = logEntry;

        // Problèmes de physique
        if (category === 'PHYSICS' && data.onGround === false && data.velocity?.y < -1) {
            this.summary.physicsIssues++;
        }

        // Problèmes de chunks
        if (category === 'CHUNK' && message.includes('pas encore généré')) {
            this.summary.chunkIssues++;
        }
    }

    // Compactage automatique
    startAutoCompact() {
        setInterval(() => {
            this.compactLogs();
        }, this.compactInterval);
    }

    // Compacter les logs en supprimant les doublons et en résumant
    compactLogs() {
        const now = Date.now();
        const compactedPatterns = [];

        // Résumer les patterns répétitifs
        for (const [hash, pattern] of this.patterns.entries()) {
            if (pattern.count > this.patternThreshold) {
                compactedPatterns.push({
                    pattern: hash,
                    count: pattern.count,
                    duration: pattern.last - pattern.first,
                    frequency: pattern.count / ((pattern.last - pattern.first) / 1000)
                });
            }
        }

        // Nettoyer les anciens patterns
        for (const [hash, pattern] of this.patterns.entries()) {
            if (now - pattern.last > 30000) { // 30 secondes
                this.patterns.delete(hash);
            }
        }

        this.summary.lastUpdate = now;
        this.summary.compactedPatterns = compactedPatterns;
    }

    // Générer un rapport complet pour l'IA
    generateAIReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: { ...this.summary },
            criticalErrors: this.criticalErrors.slice(-10), // 10 dernières erreurs critiques
            recentLogs: this.logs.slice(-50), // 50 derniers logs
            patterns: Array.from(this.patterns.entries()).map(([hash, data]) => ({
                pattern: hash,
                ...data
            })),
            analysis: this.generateAnalysis(),
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    // Générer une analyse automatique
    generateAnalysis() {
        const analysis = {
            status: 'unknown',
            mainIssues: [],
            performance: 'unknown'
        };

        // Analyser les problèmes de physique
        if (this.summary.physicsIssues > 10) {
            analysis.mainIssues.push({
                type: 'PHYSICS_FALLING',
                severity: 'HIGH',
                description: 'Joueur en chute continue - système de détection du sol défaillant',
                count: this.summary.physicsIssues
            });
            analysis.status = 'CRITICAL';
        }

        // Analyser les problèmes de chunks
        if (this.summary.chunkIssues > 5) {
            analysis.mainIssues.push({
                type: 'CHUNK_GENERATION',
                severity: 'MEDIUM',
                description: 'Problèmes de génération de chunks',
                count: this.summary.chunkIssues
            });
        }

        // Analyser les patterns répétitifs
        const repetitivePatterns = Array.from(this.patterns.values()).filter(p => p.count > 20);
        if (repetitivePatterns.length > 0) {
            analysis.mainIssues.push({
                type: 'REPETITIVE_LOGS',
                severity: 'LOW',
                description: 'Logs répétitifs détectés - possible boucle infinie',
                patterns: repetitivePatterns.length
            });
        }

        // Déterminer le statut global
        if (analysis.status === 'unknown') {
            analysis.status = this.summary.criticalCount > 0 ? 'WARNING' : 'OK';
        }

        return analysis;
    }

    // Générer des recommandations pour l'IA
    generateRecommendations() {
        const recommendations = [];
        const analysis = this.generateAnalysis();

        for (const issue of analysis.mainIssues) {
            switch (issue.type) {
                case 'PHYSICS_FALLING':
                    recommendations.push({
                        priority: 'HIGH',
                        action: 'FIX_GROUND_DETECTION',
                        description: 'Corriger la fonction getGroundHeightAt() et le système de collision',
                        code: 'Player.js - fonction update() et World.js - fonction getGroundHeightAt()'
                    });
                    break;
                case 'CHUNK_GENERATION':
                    recommendations.push({
                        priority: 'MEDIUM',
                        action: 'OPTIMIZE_CHUNK_LOADING',
                        description: 'Optimiser le système de génération de chunks',
                        code: 'World.js - système de génération asynchrone'
                    });
                    break;
                case 'REPETITIVE_LOGS':
                    recommendations.push({
                        priority: 'LOW',
                        action: 'REDUCE_LOG_SPAM',
                        description: 'Réduire les logs répétitifs avec des conditions',
                        code: 'Ajouter des conditions de throttling dans les loggers'
                    });
                    break;
            }
        }

        return recommendations;
    }

    // Méthodes de convenance pour les différents niveaux
    error(category, message, data) { this.log('ERROR', category, message, data); }
    warn(category, message, data) { this.log('WARN', category, message, data); }
    info(category, message, data) { this.log('INFO', category, message, data); }
    debug(category, message, data) { this.log('DEBUG', category, message, data); }
    critical(category, message, data) { this.log('CRITICAL', category, message, data); }

    // Méthode spécialisée pour le minage
    mining(message, data = {}) {
        this.log('INFO', 'MINING', message, data);
        // Aussi afficher dans la console pour debug immédiat
        console.log(`[MINING] ${message}`, data);
    }

    // Exporter le rapport pour l'IA
    exportForAI() {
        const report = this.generateAIReport();
        
        // Sauvegarder dans un fichier pour l'IA
        const reportText = `
# RAPPORT D'ANALYSE AUTOMATIQUE POUR IA
Généré le: ${report.timestamp}

## RÉSUMÉ EXÉCUTIF
- Statut: ${report.analysis.status}
- Total logs: ${report.summary.totalLogs}
- Erreurs critiques: ${report.summary.criticalCount}
- Problèmes physique: ${report.summary.physicsIssues}
- Problèmes chunks: ${report.summary.chunkIssues}

## PROBLÈMES IDENTIFIÉS
${report.analysis.mainIssues.map(issue => 
    `- ${issue.type}: ${issue.description} (Sévérité: ${issue.severity})`
).join('\n')}

## RECOMMANDATIONS PRIORITAIRES
${report.recommendations.map(rec => 
    `- [${rec.priority}] ${rec.action}: ${rec.description}`
).join('\n')}

## PATTERNS RÉPÉTITIFS DÉTECTÉS
${report.patterns.filter(p => p.count > 10).map(p => 
    `- ${p.pattern}: ${p.count} occurrences`
).join('\n')}

## LOGS CRITIQUES RÉCENTS
${report.criticalErrors.slice(-5).map(log => 
    `[${new Date(log.timestamp).toISOString()}] ${log.category}: ${log.message}`
).join('\n')}
        `;

        return { report, reportText };
    }
}

// Instance globale
window.SmartLogger = new SmartLogger();
export default window.SmartLogger;