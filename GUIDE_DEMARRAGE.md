# 🚀 Guide de Démarrage Rapide - JScraft Multijoueur

## ✅ Problème Résolu !

Le problème avec `SimplexNoise.js` a été corrigé. Votre jeu JScraft multijoueur fonctionne maintenant parfaitement !

## 🎮 Comment Jouer

### **1. <PERSON><PERSON><PERSON><PERSON> le Serveur**
```bash
cd "C:\Users\<USER>\Desktop\interface\JScraft - Copie"
npm start
```

### **2. Ouvrir le Jeu**
- Ouvrir votre navigateur
- Aller à : `http://localhost:3000`
- Cliquer pour commencer à jouer

### **3. Contrôles**
| Touche | Action |
|--------|--------|
| **ZQSD** | Se déplacer |
| **Espace** | Sauter |
| **Maj Gauche** | Courir |
| **Clic Gauche** | Miner un bloc |
| **Clic Droit** | Placer un bloc |
| **1-9** | Sélectionner l'inventaire |
| **T** | Ouvrir le chat |
| **F1** | Menu d'options |
| **F** | Mode vol |
| **F3** | Informations de debug |
| **Échap** | Libérer la souris |

## 🌍 Fonctionnalités Disponibles

### ✅ **Multijoueur Temps Réel**
- Plusieurs joueurs peuvent se connecter simultanément
- Mouvements synchronisés en temps réel
- Chat multijoueur fonctionnel

### ✅ **Monde Procédural**
- Génération automatique de terrain
- Biomes variés (Océan, Plaines, Forêt, Montagnes)
- Grottes et minerais générés automatiquement
- Végétation (arbres, herbe, fleurs)

### ✅ **Système de Jeu Complet**
- Minage de blocs
- Placement de blocs
- Inventaire avec hotbar
- Physique réaliste (gravité, collisions)
- Mode vol disponible

### ✅ **Interface Moderne**
- HUD avec informations en temps réel
- Chat avec historique
- Menu d'options configurables
- Notifications système

## 🔧 Configuration

### **Options Disponibles (F1)**
- **Distance de rendu** : 2-16 chunks
- **Champ de vision** : 60-120°
- **Sensibilité souris** : 0.1-2.0
- **Qualité graphique** : Low/Medium/High
- **Adresse serveur** : Configurable

### **Commandes Chat (T)**
```
/help              # Aide
/players           # Liste des joueurs
/time              # Heure actuelle
/clear             # Effacer le chat
```

## 🐛 Dépannage

### **Si le serveur ne démarre pas :**
```bash
# Vérifier que Node.js est installé
node --version

# Réinstaller les dépendances
npm install

# Redémarrer
npm start
```

### **Si le navigateur ne se connecte pas :**
- Vérifier que le serveur est démarré
- Aller à `http://localhost:3000`
- Vérifier la console (F12) pour les erreurs
- Essayer un autre navigateur

### **Performance lente :**
- Réduire la distance de rendu (F1)
- Baisser la qualité graphique
- Fermer d'autres applications

## 📊 Informations Techniques

### **Architecture**
- **Serveur** : Node.js + WebSocket (20 TPS)
- **Client** : Three.js + WebSocket (60 FPS)
- **Communication** : Temps réel bidirectionnelle

### **Performance**
- **TPS Serveur** : ~20 (affiché dans les logs)
- **FPS Client** : ~60 (affiché avec F3)
- **Latence** : <100ms en local

### **Statistiques (F3)**
- Position du joueur
- FPS client
- Chunks chargés
- Statut de connexion
- Nombre de joueurs connectés

## 🎯 Prochaines Étapes

### **Fonctionnalités à Ajouter**
- [ ] Sauvegarde du monde
- [ ] Plus de types de blocs
- [ ] Système de craft
- [ ] Monstres et PvP
- [ ] Plugins et mods

### **Optimisations**
- [ ] Compression des chunks
- [ ] Cache persistant
- [ ] Support mobile
- [ ] Interface d'administration

## 🤝 Multijoueur

### **Jouer avec des Amis**
1. **Partager votre IP** : Remplacer `localhost` par votre IP locale
2. **Configurer le port** : S'assurer que le port 3000 est ouvert
3. **Donner l'adresse** : `http://VOTRE_IP:3000`

### **Serveur Dédié**
Pour un serveur permanent :
```bash
# Installer PM2 pour la gestion de processus
npm install -g pm2

# Démarrer en mode daemon
pm2 start server/server.js --name jscraft

# Voir les logs
pm2 logs jscraft

# Redémarrer
pm2 restart jscraft
```

## 📞 Support

### **Logs Serveur**
Les logs du serveur montrent :
- Connexions/déconnexions des joueurs
- Génération des chunks
- Statistiques de performance
- Erreurs éventuelles

### **Debug Client**
- **F3** : Informations de debug
- **F12** : Console développeur
- **window.game** : Accès aux objets du jeu

---

## 🎉 Félicitations !

Votre jeu JScraft multijoueur est maintenant **100% fonctionnel** avec :

✅ **Architecture client-serveur complète**  
✅ **Support multijoueur temps réel**  
✅ **Génération de monde procédurale**  
✅ **Interface utilisateur moderne**  
✅ **Physique et gameplay complets**  

**Amusez-vous bien dans votre monde Minecraft-like !** 🎮
