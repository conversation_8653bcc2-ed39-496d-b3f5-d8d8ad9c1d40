// client/js/player/PlayerController.js
// Contrôleur de joueur côté client avec prédiction
import { PlayerCamera } from './PlayerCamera.js';
import { Inventory } from './Inventory.js';
import { GAME_CONFIG } from '../../../shared/constants.js';

export class PlayerController {
    constructor() {
        this.playerId = null;
        this.camera = new PlayerCamera();
        this.inventory = new Inventory();
        this.keys = {};
        this.mouseMovement = { x: 0, y: 0 };
        
        // Position prédictive locale (pour la fluidité)
        this.predictedPosition = { x: 0, y: 100, z: 0 };
        this.predictedVelocity = { x: 0, y: 0, z: 0 };
        this.predictedRotation = { x: 0, y: 0 };
        
        // Position authoritative du serveur
        this.serverPosition = { x: 0, y: 100, z: 0 };
        this.serverVelocity = { x: 0, y: 0, z: 0 };
        this.serverRotation = { x: 0, y: 0 };
        
        // État de prédiction
        this.predictionEnabled = true;
        this.correctionStrength = 0.15; // Force de correction des erreurs de prédiction
        
        // Contrôles
        this.mouseSensitivity = 1.0;
        this.isPointerLocked = false;
        this.isFlying = false;
        
        // Minage
        this.isMining = false;
        this.miningTarget = null;
        
        // Timestamps
        this.lastInputSent = 0;
        this.inputSendRate = 1000 / 60; // 60 Hz
        
        this.setupEventListeners();
        console.log('🎮 PlayerController initialisé');
    }
    
    setupEventListeners() {
        // Événements clavier
        document.addEventListener('keydown', (e) => this.onKeyDown(e));
        document.addEventListener('keyup', (e) => this.onKeyUp(e));
        
        // Événements souris
        document.addEventListener('mousedown', (e) => this.onMouseDown(e));
        document.addEventListener('mouseup', (e) => this.onMouseUp(e));
        document.addEventListener('mousemove', (e) => this.onMouseMove(e));
        
        // Pointer Lock
        document.addEventListener('click', () => this.requestPointerLock());
        document.addEventListener('pointerlockchange', () => this.onPointerLockChange());
        
        // Molette de la souris (pour l'inventaire)
        document.addEventListener('wheel', (e) => this.onWheel(e));
    }
    
    onKeyDown(event) {
        this.keys[event.code] = true;
        
        // Touches spéciales
        switch (event.code) {
            case 'Escape':
                this.exitPointerLock();
                break;
            case 'KeyF':
                this.toggleFlyMode();
                break;
            case 'F1':
                event.preventDefault();
                if (window.game) {
                    window.game.toggleOptions();
                }
                break;
            case 'KeyT':
                if (window.game) {
                    window.game.toggleChat();
                }
                break;
        }
    }
    
    onKeyUp(event) {
        this.keys[event.code] = false;
    }
    
    onMouseDown(event) {
        if (!this.isPointerLocked) return;
        
        switch (event.button) {
            case 0: // Clic gauche - Miner
                this.startMining();
                break;
            case 2: // Clic droit - Placer un bloc
                this.placeBlock();
                break;
        }
    }
    
    onMouseUp(event) {
        if (!this.isPointerLocked) return;
        
        switch (event.button) {
            case 0: // Clic gauche - Arrêter de miner
                this.stopMining();
                break;
        }
    }
    
    onMouseMove(event) {
        if (!this.isPointerLocked) return;
        
        this.mouseMovement.x += event.movementX * this.mouseSensitivity;
        this.mouseMovement.y += event.movementY * this.mouseSensitivity;
    }
    
    onWheel(event) {
        if (!this.isPointerLocked) return;
        
        // Changer l'emplacement sélectionné dans l'inventaire
        // TODO: Implémenter la gestion de l'inventaire
    }
    
    requestPointerLock() {
        if (!this.isPointerLocked) {
            document.body.requestPointerLock();
        }
    }
    
    exitPointerLock() {
        if (this.isPointerLocked) {
            document.exitPointerLock();
        }
    }
    
    onPointerLockChange() {
        this.isPointerLocked = document.pointerLockElement === document.body;
        
        if (this.isPointerLocked) {
            console.log('🔒 Pointer lock activé');
            // Cacher les instructions
            const instructions = document.getElementById('instructions');
            if (instructions) {
                instructions.classList.add('hidden');
            }
        } else {
            console.log('🔓 Pointer lock désactivé');
        }
    }
    
    update(deltaTime) {
        // Mettre à jour la rotation de la caméra
        this.updateCameraRotation();
        
        // Prédiction côté client
        if (this.predictionEnabled) {
            this.updatePrediction(deltaTime);
        }
        
        // Envoyer les entrées au serveur si nécessaire
        const now = Date.now();
        if (now - this.lastInputSent >= this.inputSendRate) {
            const inputPayload = this.generateInputPayload();
            if (inputPayload) {
                this.lastInputSent = now;
                return inputPayload;
            }
        }
        
        return null;
    }
    
    updateCameraRotation() {
        if (this.mouseMovement.x !== 0 || this.mouseMovement.y !== 0) {
            // Appliquer la rotation
            this.predictedRotation.y -= this.mouseMovement.x * 0.002;
            this.predictedRotation.x -= this.mouseMovement.y * 0.002;
            
            // Limiter la rotation verticale
            this.predictedRotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.predictedRotation.x));
            
            // Mettre à jour la caméra
            this.camera.setRotation(this.predictedRotation.x, this.predictedRotation.y);
            
            // Réinitialiser le mouvement de la souris
            this.mouseMovement.x = 0;
            this.mouseMovement.y = 0;
        }
    }
    
    updatePrediction(deltaTime) {
        // Prédiction de mouvement côté client pour la fluidité
        const speed = GAME_CONFIG.MOVEMENT_SPEED;
        const sprintMultiplier = this.keys['ShiftLeft'] ? GAME_CONFIG.SPRINT_MULTIPLIER : 1.0;
        
        // Calculer le mouvement
        let moveX = 0;
        let moveZ = 0;
        
        if (this.keys['KeyW'] || this.keys['ArrowUp']) moveZ -= 1;
        if (this.keys['KeyS'] || this.keys['ArrowDown']) moveZ += 1;
        if (this.keys['KeyA'] || this.keys['ArrowLeft']) moveX -= 1;
        if (this.keys['KeyD'] || this.keys['ArrowRight']) moveX += 1;
        
        // Normaliser le vecteur de mouvement
        if (moveX !== 0 || moveZ !== 0) {
            const length = Math.sqrt(moveX * moveX + moveZ * moveZ);
            moveX /= length;
            moveZ /= length;
            
            // Appliquer la rotation de la caméra
            const cos = Math.cos(this.predictedRotation.y);
            const sin = Math.sin(this.predictedRotation.y);
            
            this.predictedVelocity.x = (moveX * cos - moveZ * sin) * speed * sprintMultiplier;
            this.predictedVelocity.z = (moveX * sin + moveZ * cos) * speed * sprintMultiplier;
        } else {
            this.predictedVelocity.x = 0;
            this.predictedVelocity.z = 0;
        }
        
        // Gestion du saut/vol
        if (this.keys['Space']) {
            if (this.isFlying) {
                this.predictedVelocity.y = speed * sprintMultiplier;
            } else {
                // Le saut sera géré par le serveur
            }
        } else if (this.isFlying && this.keys['ShiftLeft']) {
            this.predictedVelocity.y = -speed * sprintMultiplier;
        } else if (this.isFlying) {
            this.predictedVelocity.y *= 0.9; // Décélération en vol
        }
        
        // Appliquer le mouvement prédit
        this.predictedPosition.x += this.predictedVelocity.x * deltaTime;
        this.predictedPosition.y += this.predictedVelocity.y * deltaTime;
        this.predictedPosition.z += this.predictedVelocity.z * deltaTime;
        
        // Mettre à jour la position de la caméra
        this.camera.setPosition(
            this.predictedPosition.x,
            this.predictedPosition.y,
            this.predictedPosition.z
        );
    }
    
    generateInputPayload() {
        const activeKeys = Object.keys(this.keys).filter(k => this.keys[k]);
        
        // Ne pas envoyer si aucune entrée
        if (activeKeys.length === 0 && this.mouseMovement.x === 0 && this.mouseMovement.y === 0) {
            return null;
        }
        
        return {
            keys: activeKeys,
            rotation: {
                x: this.predictedRotation.x,
                y: this.predictedRotation.y
            },
            sprint: this.keys['ShiftLeft'] || false,
            timestamp: Date.now()
        };
    }
    
    // Gestion des mises à jour du serveur
    handleServerUpdate(worldState) {
        if (!worldState.players) return;

        // Trouver notre joueur dans l'état du monde
        const myState = worldState.players.find(p => p.id === this.playerId);
        if (myState) {
            // Debug: Log de la première position reçue
            if (!this.firstPositionReceived) {
                console.log('🎯 Première position reçue du serveur:', myState.position);
                this.firstPositionReceived = true;
            }

            // Mettre à jour la position authoritative
            this.serverPosition = { ...myState.position };
            this.serverVelocity = { ...myState.velocity };
            this.serverRotation = { ...myState.rotation };

            // Correction de la prédiction (interpolation douce)
            if (this.predictionEnabled) {
                this.correctPrediction();
            } else {
                // Utiliser directement la position du serveur
                this.predictedPosition = { ...this.serverPosition };
                this.predictedVelocity = { ...this.serverVelocity };
                this.predictedRotation = { ...this.serverRotation };

                this.camera.setPosition(
                    this.predictedPosition.x,
                    this.predictedPosition.y,
                    this.predictedPosition.z
                );
                this.camera.setRotation(
                    this.predictedRotation.x,
                    this.predictedRotation.y
                );
            }

            // Mettre à jour la position du joueur dans le monde client
            if (window.game && window.game.clientWorld) {
                window.game.clientWorld.updatePlayerPosition(
                    this.predictedPosition.x,
                    this.predictedPosition.z
                );
            }
        }
    }
    
    correctPrediction() {
        // Calculer l'erreur de prédiction
        const errorX = this.serverPosition.x - this.predictedPosition.x;
        const errorY = this.serverPosition.y - this.predictedPosition.y;
        const errorZ = this.serverPosition.z - this.predictedPosition.z;
        
        // Appliquer la correction graduellement
        this.predictedPosition.x += errorX * this.correctionStrength;
        this.predictedPosition.y += errorY * this.correctionStrength;
        this.predictedPosition.z += errorZ * this.correctionStrength;
        
        // Mettre à jour la caméra
        this.camera.setPosition(
            this.predictedPosition.x,
            this.predictedPosition.y,
            this.predictedPosition.z
        );
    }
    
    // Actions de jeu
    startMining() {
        if (this.isMining) return;
        
        // Lancer un rayon pour trouver le bloc cible
        const target = this.camera.getTargetBlock(GAME_CONFIG.MINING_REACH);
        if (target && window.game && window.game.socketClient) {
            this.isMining = true;
            this.miningTarget = target;
            window.game.socketClient.sendMineStart(target);
        }
    }
    
    stopMining() {
        if (!this.isMining) return;
        
        this.isMining = false;
        this.miningTarget = null;
        
        if (window.game && window.game.socketClient) {
            window.game.socketClient.sendMineStop();
        }
    }
    
    placeBlock() {
        // TODO: Implémenter le placement de blocs
        console.log('🧱 Placement de bloc (à implémenter)');
    }
    
    toggleFlyMode() {
        this.isFlying = !this.isFlying;
        console.log(`✈️ Mode vol: ${this.isFlying ? 'activé' : 'désactivé'}`);
    }
    
    // Méthodes publiques
    setPlayerId(playerId) {
        this.playerId = playerId;
        console.log(`👤 ID joueur défini: ${playerId}`);
    }
    
    setMouseSensitivity(sensitivity) {
        this.mouseSensitivity = sensitivity;
    }
    
    getPosition() {
        return { ...this.predictedPosition };
    }
    
    getCamera() {
        return this.camera.getCamera();
    }
    
    handleResize() {
        this.camera.handleResize();
    }
}
