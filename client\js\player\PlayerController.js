// client/js/player/PlayerController.js
// Contrôleur de joueur côté client avec prédiction
import { PlayerCamera } from './PlayerCamera.js';
import { Inventory } from './Inventory.js';
import { GAME_CONFIG } from '../../../shared/constants.js';

export class PlayerController {
    constructor() {
        this.playerId = null;
        this.camera = new PlayerCamera();
        this.inventory = new Inventory();
        this.keys = {};
        this.mouseMovement = { x: 0, y: 0 };
        
        // Position prédictive locale (pour la fluidité)
        this.predictedPosition = { x: 0, y: 100, z: 0 };
        this.predictedVelocity = { x: 0, y: 0, z: 0 };
        this.predictedRotation = { x: 0, y: 0 };
        
        // Position authoritative du serveur
        this.serverPosition = { x: 0, y: 100, z: 0 };
        this.serverVelocity = { x: 0, y: 0, z: 0 };
        this.serverRotation = { x: 0, y: 0 };
        
        // État de prédiction
        this.predictionEnabled = true;
        this.correctionStrength = 0.15; // Force de correction des erreurs de prédiction
        
        // Contrôles
        this.mouseSensitivity = 1.0;
        this.isPointerLocked = false;
        this.isFlying = false;
        
        // Minage
        this.isMining = false;
        this.miningTarget = null;
        
        // Timestamps
        this.lastInputSent = 0;
        this.inputSendRate = 1000 / 60; // 60 Hz
        
        this.setupEventListeners();
        console.log('🎮 PlayerController initialisé');
    }
    
    setupEventListeners() {
        // Événements clavier
        document.addEventListener('keydown', (e) => this.onKeyDown(e));
        document.addEventListener('keyup', (e) => this.onKeyUp(e));
        
        // Événements souris
        document.addEventListener('mousedown', (e) => this.onMouseDown(e));
        document.addEventListener('mouseup', (e) => this.onMouseUp(e));
        document.addEventListener('mousemove', (e) => this.onMouseMove(e));
        
        // Pointer Lock
        document.addEventListener('click', () => this.requestPointerLock());
        document.addEventListener('pointerlockchange', () => this.onPointerLockChange());
        
        // Molette de la souris (pour l'inventaire)
        document.addEventListener('wheel', (e) => this.onWheel(e));
    }
    
    onKeyDown(event) {
        this.keys[event.code] = true;
        
        // Touches spéciales
        switch (event.code) {
            case 'Escape':
                this.exitPointerLock();
                break;
            case 'KeyF':
                this.toggleFlyMode();
                break;
            case 'F1':
                event.preventDefault();
                if (window.game) {
                    window.game.toggleOptions();
                }
                break;
            case 'KeyT':
                if (window.game) {
                    window.game.toggleChat();
                }
                break;
        }
    }
    
    onKeyUp(event) {
        this.keys[event.code] = false;
    }
    
    onMouseDown(event) {
        if (!this.isPointerLocked) return;
        
        switch (event.button) {
            case 0: // Clic gauche - Miner
                this.startMining();
                break;
            case 2: // Clic droit - Placer un bloc
                this.placeBlock();
                break;
        }
    }
    
    onMouseUp(event) {
        if (!this.isPointerLocked) return;
        
        switch (event.button) {
            case 0: // Clic gauche - Arrêter de miner
                this.stopMining();
                break;
        }
    }
    
    onMouseMove(event) {
        if (!this.isPointerLocked) return;
        
        this.mouseMovement.x += event.movementX * this.mouseSensitivity;
        this.mouseMovement.y += event.movementY * this.mouseSensitivity;
    }
    
    onWheel(event) {
        if (!this.isPointerLocked) return;
        
        // Changer l'emplacement sélectionné dans l'inventaire
        // TODO: Implémenter la gestion de l'inventaire
    }
    
    requestPointerLock() {
        if (!this.isPointerLocked) {
            document.body.requestPointerLock();
        }
    }
    
    exitPointerLock() {
        if (this.isPointerLocked) {
            document.exitPointerLock();
        }
    }
    
    onPointerLockChange() {
        const wasLocked = this.isPointerLocked;
        this.isPointerLocked = document.pointerLockElement === document.body;

        if (this.isPointerLocked) {
            console.log('🔒 Pointer lock activé');
            // Cacher les instructions
            const instructions = document.getElementById('instructions');
            if (instructions) {
                instructions.classList.add('hidden');
            }
        } else {
            console.log('🔓 Pointer lock désactivé');
            // Debug: Pourquoi le pointer lock s'est désactivé ?
            if (wasLocked) {
                console.log('🔍 Pointer lock désactivé après avoir été actif - vérifier la cause');
                console.trace('Stack trace de la désactivation du pointer lock');
            }
        }
    }
    
    update(deltaTime) {
        // Mettre à jour la rotation de la caméra
        this.updateCameraRotation();
        
        // Prédiction côté client
        if (this.predictionEnabled) {
            this.updatePrediction(deltaTime);
        }
        
        // Envoyer les entrées au serveur si nécessaire
        const now = Date.now();
        if (now - this.lastInputSent >= this.inputSendRate) {
            const inputPayload = this.generateInputPayload();
            if (inputPayload) {
                this.lastInputSent = now;
                return inputPayload;
            }
        }
        
        return null;
    }
    
    updateCameraRotation() {
        if (this.mouseMovement.x !== 0 || this.mouseMovement.y !== 0) {
            // Appliquer la rotation
            this.predictedRotation.y -= this.mouseMovement.x * 0.002;
            this.predictedRotation.x -= this.mouseMovement.y * 0.002;
            
            // Limiter la rotation verticale
            this.predictedRotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.predictedRotation.x));
            
            // Mettre à jour la caméra
            this.camera.setRotation(this.predictedRotation.x, this.predictedRotation.y);
            
            // Réinitialiser le mouvement de la souris
            this.mouseMovement.x = 0;
            this.mouseMovement.y = 0;
        }
    }
    
    updatePrediction(deltaTime) {
        // Prédiction de mouvement côté client pour la fluidité
        const speed = GAME_CONFIG.MOVEMENT_SPEED;
        const sprintMultiplier = this.keys['ShiftLeft'] ? GAME_CONFIG.SPRINT_MULTIPLIER : 1.0;

        // Log seulement si il y a du mouvement ou si on vole
        const hasMovement = Object.keys(this.keys).some(k => this.keys[k]) || this.isFlying;
        if (hasMovement) {
            console.log('🔍 [PHYSICS] updatePrediction:', {
                deltaTime: deltaTime.toFixed(4),
                speed,
                sprintMultiplier,
                isFlying: this.isFlying,
                position: {
                    x: this.predictedPosition.x.toFixed(2),
                    y: this.predictedPosition.y.toFixed(2),
                    z: this.predictedPosition.z.toFixed(2)
                },
                velocity: {
                    x: this.predictedVelocity.x.toFixed(2),
                    y: this.predictedVelocity.y.toFixed(2),
                    z: this.predictedVelocity.z.toFixed(2)
                }
            });
        }
        
        // Calculer le mouvement
        let moveX = 0;
        let moveZ = 0;

        if (this.keys['KeyW'] || this.keys['ArrowUp']) moveZ -= 1;
        if (this.keys['KeyS'] || this.keys['ArrowDown']) moveZ += 1;
        if (this.keys['KeyA'] || this.keys['ArrowLeft']) moveX -= 1;
        if (this.keys['KeyD'] || this.keys['ArrowRight']) moveX += 1;

        const activeKeys = Object.keys(this.keys).filter(k => this.keys[k]);
        if (activeKeys.length > 0) {
            console.log('🎮 [MOVEMENT] Touches actives:', activeKeys, 'moveX:', moveX, 'moveZ:', moveZ);
        }
        
        // Normaliser le vecteur de mouvement
        if (moveX !== 0 || moveZ !== 0) {
            const length = Math.sqrt(moveX * moveX + moveZ * moveZ);
            moveX /= length;
            moveZ /= length;
            
            // Appliquer la rotation de la caméra
            const cos = Math.cos(this.predictedRotation.y);
            const sin = Math.sin(this.predictedRotation.y);
            
            this.predictedVelocity.x = (moveX * cos - moveZ * sin) * speed * sprintMultiplier;
            this.predictedVelocity.z = (moveX * sin + moveZ * cos) * speed * sprintMultiplier;
        } else {
            this.predictedVelocity.x = 0;
            this.predictedVelocity.z = 0;
        }
        
        // GRAVITÉ DÉSACTIVÉE CÔTÉ CLIENT - Gérée uniquement par le serveur
        // La prédiction côté client ne doit PAS appliquer la gravité pour éviter les conflits
        // Le serveur est la source de vérité pour la physique verticale
        if (!this.isFlying) {
            console.log('🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement');
        }

        // Gestion du saut/vol
        if (this.keys['Space']) {
            if (this.isFlying) {
                this.predictedVelocity.y = speed * sprintMultiplier;
                console.log('🚁 [FLY] Vol vers le haut:', this.predictedVelocity.y.toFixed(2));
            } else {
                console.log('🦘 [JUMP] Tentative de saut (géré par serveur)');
            }
        } else if (this.isFlying && this.keys['ShiftLeft']) {
            this.predictedVelocity.y = -speed * sprintMultiplier;
            console.log('🚁 [FLY] Vol vers le bas:', this.predictedVelocity.y.toFixed(2));
        } else if (this.isFlying) {
            this.predictedVelocity.y *= 0.9; // Décélération en vol
            console.log('🚁 [FLY] Décélération vol:', this.predictedVelocity.y.toFixed(2));
        }
        
        // Sauvegarder l'ancienne position pour les logs
        const oldPosition = { ...this.predictedPosition };

        // Appliquer le mouvement prédit (SEULEMENT horizontal)
        this.predictedPosition.x += this.predictedVelocity.x * deltaTime;
        // Y désactivé - géré uniquement par le serveur pour éviter les conflits
        // this.predictedPosition.y += this.predictedVelocity.y * deltaTime;
        this.predictedPosition.z += this.predictedVelocity.z * deltaTime;

        // Log du mouvement si il y a eu un changement significatif
        const positionChanged = Math.abs(this.predictedPosition.x - oldPosition.x) > 0.01 ||
                               Math.abs(this.predictedPosition.y - oldPosition.y) > 0.01 ||
                               Math.abs(this.predictedPosition.z - oldPosition.z) > 0.01;

        if (positionChanged) {
            console.log('📍 [POSITION] Mouvement appliqué:', {
                from: {
                    x: oldPosition.x.toFixed(2),
                    y: oldPosition.y.toFixed(2),
                    z: oldPosition.z.toFixed(2)
                },
                to: {
                    x: this.predictedPosition.x.toFixed(2),
                    y: this.predictedPosition.y.toFixed(2),
                    z: this.predictedPosition.z.toFixed(2)
                },
                delta: {
                    x: (this.predictedPosition.x - oldPosition.x).toFixed(3),
                    y: (this.predictedPosition.y - oldPosition.y).toFixed(3),
                    z: (this.predictedPosition.z - oldPosition.z).toFixed(3)
                }
            });
        }

        // Mettre à jour la position de la caméra
        this.camera.setPosition(
            this.predictedPosition.x,
            this.predictedPosition.y,
            this.predictedPosition.z
        );
    }
    
    generateInputPayload() {
        const activeKeys = Object.keys(this.keys).filter(k => this.keys[k]);
        
        // Ne pas envoyer si aucune entrée
        if (activeKeys.length === 0 && this.mouseMovement.x === 0 && this.mouseMovement.y === 0) {
            return null;
        }
        
        return {
            keys: activeKeys,
            rotation: {
                x: this.predictedRotation.x,
                y: this.predictedRotation.y
            },
            sprint: this.keys['ShiftLeft'] || false,
            timestamp: Date.now()
        };
    }
    
    // Gestion des mises à jour du serveur
    handleServerUpdate(worldState) {
        if (!worldState.players) return;

        // Trouver notre joueur dans l'état du monde
        const myState = worldState.players.find(p => p.id === this.playerId);
        if (myState) {
            // Debug: Log de la première position reçue
            if (!this.firstPositionReceived) {
                console.log('🎯 Première position reçue du serveur:', myState.position);
                this.firstPositionReceived = true;
            }

            // Log détaillé des mises à jour du serveur
            console.log('📡 [SERVER] Mise à jour reçue:', {
                position: {
                    x: myState.position.x.toFixed(2),
                    y: myState.position.y.toFixed(2),
                    z: myState.position.z.toFixed(2)
                },
                velocity: {
                    x: myState.velocity.x.toFixed(2),
                    y: myState.velocity.y.toFixed(2),
                    z: myState.velocity.z.toFixed(2)
                },
                predicted: {
                    x: this.predictedPosition.x.toFixed(2),
                    y: this.predictedPosition.y.toFixed(2),
                    z: this.predictedPosition.z.toFixed(2)
                },
                error: {
                    x: (myState.position.x - this.predictedPosition.x).toFixed(3),
                    y: (myState.position.y - this.predictedPosition.y).toFixed(3),
                    z: (myState.position.z - this.predictedPosition.z).toFixed(3)
                }
            });

            // Mettre à jour la position authoritative
            this.serverPosition = { ...myState.position };
            this.serverVelocity = { ...myState.velocity };
            this.serverRotation = { ...myState.rotation };

            // Correction de la prédiction (interpolation douce)
            if (this.predictionEnabled) {
                this.correctPrediction();
            } else {
                // Utiliser directement la position du serveur
                this.predictedPosition = { ...this.serverPosition };
                this.predictedVelocity = { ...this.serverVelocity };
                this.predictedRotation = { ...this.serverRotation };

                this.camera.setPosition(
                    this.predictedPosition.x,
                    this.predictedPosition.y,
                    this.predictedPosition.z
                );
                this.camera.setRotation(
                    this.predictedRotation.x,
                    this.predictedRotation.y
                );
            }

            // Mettre à jour la position du joueur dans le monde client
            if (window.game && window.game.clientWorld) {
                window.game.clientWorld.updatePlayerPosition(
                    this.predictedPosition.x,
                    this.predictedPosition.z
                );
            }
        }
    }
    
    correctPrediction() {
        // Calculer l'erreur de prédiction
        const errorX = this.serverPosition.x - this.predictedPosition.x;
        const errorY = this.serverPosition.y - this.predictedPosition.y;
        const errorZ = this.serverPosition.z - this.predictedPosition.z;

        const errorMagnitude = Math.sqrt(errorX * errorX + errorY * errorY + errorZ * errorZ);

        if (errorMagnitude > 0.1) {
            console.log('🔧 [CORRECTION] Erreur de prédiction détectée:', {
                error: {
                    x: errorX.toFixed(3),
                    y: errorY.toFixed(3),
                    z: errorZ.toFixed(3),
                    magnitude: errorMagnitude.toFixed(3)
                },
                correctionStrength: this.correctionStrength,
                beforeCorrection: {
                    x: this.predictedPosition.x.toFixed(2),
                    y: this.predictedPosition.y.toFixed(2),
                    z: this.predictedPosition.z.toFixed(2)
                }
            });
        }

        // Appliquer la correction graduellement
        this.predictedPosition.x += errorX * this.correctionStrength;
        // Position Y forcée du serveur (pas de prédiction)
        this.predictedPosition.y = this.serverPosition.y;
        this.predictedPosition.z += errorZ * this.correctionStrength;

        if (errorMagnitude > 0.1) {
            console.log('🔧 [CORRECTION] Après correction:', {
                x: this.predictedPosition.x.toFixed(2),
                y: this.predictedPosition.y.toFixed(2),
                z: this.predictedPosition.z.toFixed(2)
            });
        }
        
        // Mettre à jour la caméra
        this.camera.setPosition(
            this.predictedPosition.x,
            this.predictedPosition.y,
            this.predictedPosition.z
        );
    }
    
    // Actions de jeu
    startMining() {
        if (this.isMining) return;
        
        // Lancer un rayon pour trouver le bloc cible
        const target = this.camera.getTargetBlock(GAME_CONFIG.MINING_REACH);
        if (target && window.game && window.game.socketClient) {
            this.isMining = true;
            this.miningTarget = target;
            window.game.socketClient.sendMineStart(target);
        }
    }
    
    stopMining() {
        if (!this.isMining) return;
        
        this.isMining = false;
        this.miningTarget = null;
        
        if (window.game && window.game.socketClient) {
            window.game.socketClient.sendMineStop();
        }
    }
    
    placeBlock() {
        // TODO: Implémenter le placement de blocs
        console.log('🧱 Placement de bloc (à implémenter)');
    }
    
    toggleFlyMode() {
        this.isFlying = !this.isFlying;
        console.log(`✈️ Mode vol: ${this.isFlying ? 'activé' : 'désactivé'}`);
    }
    
    // Méthodes publiques
    setPlayerId(playerId) {
        this.playerId = playerId;
        console.log(`👤 ID joueur défini: ${playerId}`);
    }
    
    setMouseSensitivity(sensitivity) {
        this.mouseSensitivity = sensitivity;
    }
    
    getPosition() {
        return { ...this.predictedPosition };
    }
    
    getCamera() {
        return this.camera.getCamera();
    }
    
    handleResize() {
        this.camera.handleResize();
    }
}
