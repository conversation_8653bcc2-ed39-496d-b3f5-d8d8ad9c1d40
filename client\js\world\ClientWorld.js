// client/js/world/ClientWorld.js
// Gestionnaire du monde côté client - AFFICHAGE SEULEMENT
import { ClientChunk } from './ClientChunk.js';
import { RemotePlayer } from '../player/RemotePlayer.js';
import { GAME_CONFIG, Utils } from '../../../shared/constants.js';

export class ClientWorld {
    constructor(scene) {
        this.scene = scene;
        this.chunks = new Map(); // Map de "x,z" -> ClientChunk
        this.remotePlayers = new Map(); // Map de playerId -> RemotePlayer
        
        // Configuration
        this.renderDistance = GAME_CONFIG.DEFAULT_RENDER_DISTANCE;
        this.seed = 0;
        
        // Position du joueur pour le culling
        this.playerPosition = { x: 0, z: 0 };
        
        // Cache de visibilité
        this.visibilityCache = new Map();
        this.lastVisibilityUpdate = 0;
        this.visibilityUpdateInterval = 500; // ms
        
        // Statistiques
        this.stats = {
            chunksLoaded: 0,
            chunksVisible: 0,
            playersVisible: 0,
            lastUpdate: Date.now()
        };
        
        console.log('🌍 ClientWorld initialisé');
    }
    
    setSeed(seed) {
        this.seed = seed;
        console.log(`🌱 Seed du monde défini: ${seed}`);
    }
    
    update(deltaTime) {
        const now = Date.now();
        
        // Mettre à jour les joueurs distants
        this.remotePlayers.forEach(player => {
            player.update(deltaTime);
        });
        
        // Mettre à jour la visibilité des chunks
        if (now - this.lastVisibilityUpdate > this.visibilityUpdateInterval) {
            this.updateChunkVisibility();
            this.lastVisibilityUpdate = now;
        }
        
        // Mettre à jour les statistiques
        this.updateStats();
    }
    
    // Gestion des mises à jour du serveur
    handleServerUpdate(worldState) {
        if (!worldState.players) return;
        
        // Mettre à jour les joueurs distants
        worldState.players.forEach(playerState => {
            if (playerState.id === window.game?.playerId) {
                // Notre joueur - géré par PlayerController
                return;
            }
            
            // Joueur distant
            if (!this.remotePlayers.has(playerState.id)) {
                this.addPlayer(playerState.id, playerState);
            } else {
                this.remotePlayers.get(playerState.id).updateState(playerState);
            }
        });
        
        // Supprimer les joueurs qui ne sont plus dans l'état
        const activePlayerIds = new Set(worldState.players.map(p => p.id));
        this.remotePlayers.forEach((player, playerId) => {
            if (!activePlayerIds.has(playerId)) {
                this.removePlayer(playerId);
            }
        });
    }
    
    handleChunkData(chunkData) {
        const chunkKey = `${chunkData.x},${chunkData.z}`;
        
        console.log(`📦 Réception chunk: (${chunkData.x}, ${chunkData.z})`);
        
        // Créer le chunk client
        const clientChunk = new ClientChunk(chunkData.x, chunkData.z);
        clientChunk.setBlockData(chunkData.blocks);
        
        // Construire le mesh
        const mesh = clientChunk.buildMesh();
        if (mesh) {
            // Vérifier si le chunk est dans la distance de rendu
            const distance = this.getChunkDistance(chunkData.x, chunkData.z);
            if (distance <= this.renderDistance) {
                this.scene.add(mesh);
                clientChunk.setVisible(true);
            }
        }
        
        // Stocker le chunk
        this.chunks.set(chunkKey, clientChunk);
        this.stats.chunksLoaded++;
        
        console.log(`✅ Chunk ajouté: (${chunkData.x}, ${chunkData.z}), distance: ${distance.toFixed(2)}`);
    }
    
    handleBlockUpdate(updateData) {
        const { position, blockType, action } = updateData;
        const chunkX = Math.floor(position.x / GAME_CONFIG.CHUNK_WIDTH);
        const chunkZ = Math.floor(position.z / GAME_CONFIG.CHUNK_DEPTH);
        const chunkKey = `${chunkX},${chunkZ}`;
        
        const chunk = this.chunks.get(chunkKey);
        if (chunk) {
            // Calculer les coordonnées locales
            const localX = position.x - chunkX * GAME_CONFIG.CHUNK_WIDTH;
            const localZ = position.z - chunkZ * GAME_CONFIG.CHUNK_DEPTH;
            
            // Mettre à jour le bloc
            chunk.setBlock(localX, position.y, localZ, blockType);
            
            // Reconstruire le mesh
            chunk.rebuildMesh();
            
            console.log(`🧱 Bloc ${action}: (${position.x}, ${position.y}, ${position.z}) -> ${blockType}`);
        }
    }
    
    // Gestion des joueurs distants
    addPlayer(playerId, playerState) {
        if (this.remotePlayers.has(playerId)) {
            console.warn(`⚠️ Joueur ${playerId} déjà présent`);
            return;
        }
        
        const remotePlayer = new RemotePlayer(playerId, this.scene);
        remotePlayer.updateState(playerState);
        
        this.remotePlayers.set(playerId, remotePlayer);
        this.stats.playersVisible++;
        
        console.log(`👤 Joueur ajouté: ${playerId}`);
    }
    
    removePlayer(playerId) {
        const player = this.remotePlayers.get(playerId);
        if (player) {
            player.dispose();
            this.remotePlayers.delete(playerId);
            this.stats.playersVisible--;
            
            console.log(`👋 Joueur supprimé: ${playerId}`);
        }
    }
    
    // Gestion de la visibilité des chunks
    updateChunkVisibility() {
        let visibleCount = 0;
        
        this.chunks.forEach((chunk, chunkKey) => {
            const [chunkX, chunkZ] = chunkKey.split(',').map(Number);
            const distance = this.getChunkDistance(chunkX, chunkZ);
            const shouldBeVisible = distance <= this.renderDistance;
            
            if (shouldBeVisible !== chunk.isVisible()) {
                if (shouldBeVisible) {
                    // Rendre visible
                    const mesh = chunk.getMesh();
                    if (mesh && !this.scene.children.includes(mesh)) {
                        this.scene.add(mesh);
                    }
                    chunk.setVisible(true);
                } else {
                    // Cacher
                    const mesh = chunk.getMesh();
                    if (mesh && this.scene.children.includes(mesh)) {
                        this.scene.remove(mesh);
                    }
                    chunk.setVisible(false);
                }
            }
            
            if (chunk.isVisible()) {
                visibleCount++;
            }
        });
        
        this.stats.chunksVisible = visibleCount;
    }
    
    getChunkDistance(chunkX, chunkZ) {
        const playerChunkX = Math.floor(this.playerPosition.x / GAME_CONFIG.CHUNK_WIDTH);
        const playerChunkZ = Math.floor(this.playerPosition.z / GAME_CONFIG.CHUNK_DEPTH);
        
        return Utils.distance2D(chunkX, chunkZ, playerChunkX, playerChunkZ);
    }
    
    // Mise à jour de la position du joueur pour le culling
    updatePlayerPosition(x, z) {
        this.playerPosition.x = x;
        this.playerPosition.z = z;
    }
    
    // Configuration
    setRenderDistance(distance) {
        this.renderDistance = Math.max(2, Math.min(distance, GAME_CONFIG.MAX_RENDER_DISTANCE));
        console.log(`🔭 Distance de rendu: ${this.renderDistance}`);
        
        // Forcer la mise à jour de la visibilité
        this.updateChunkVisibility();
    }
    
    // Obtenir des informations sur un bloc
    getBlockAt(worldX, worldY, worldZ) {
        const chunkX = Math.floor(worldX / GAME_CONFIG.CHUNK_WIDTH);
        const chunkZ = Math.floor(worldZ / GAME_CONFIG.CHUNK_DEPTH);
        const chunkKey = `${chunkX},${chunkZ}`;
        
        const chunk = this.chunks.get(chunkKey);
        if (!chunk) return 0; // Air si chunk pas chargé
        
        const localX = worldX - chunkX * GAME_CONFIG.CHUNK_WIDTH;
        const localZ = worldZ - chunkZ * GAME_CONFIG.CHUNK_DEPTH;
        
        return chunk.getBlock(localX, worldY, localZ);
    }
    
    // Recherche de collision (version simplifiée côté client)
    hasCollisionAt(worldX, worldY, worldZ) {
        const blockType = this.getBlockAt(Math.floor(worldX), Math.floor(worldY), Math.floor(worldZ));
        return blockType !== 0; // Tout sauf l'air est solide
    }
    
    // Nettoyage des chunks distants
    cleanupDistantChunks() {
        const chunksToRemove = [];
        
        this.chunks.forEach((chunk, chunkKey) => {
            const [chunkX, chunkZ] = chunkKey.split(',').map(Number);
            const distance = this.getChunkDistance(chunkX, chunkZ);
            
            // Supprimer les chunks très éloignés
            if (distance > this.renderDistance + 5) {
                chunksToRemove.push(chunkKey);
            }
        });
        
        chunksToRemove.forEach(chunkKey => {
            const chunk = this.chunks.get(chunkKey);
            if (chunk) {
                chunk.dispose();
                this.chunks.delete(chunkKey);
                this.stats.chunksLoaded--;
            }
        });
        
        if (chunksToRemove.length > 0) {
            console.log(`🗑️ ${chunksToRemove.length} chunks distants supprimés`);
        }
    }
    
    // Statistiques et informations
    updateStats() {
        this.stats.lastUpdate = Date.now();
    }
    
    getChunkCount() {
        return this.chunks.size;
    }
    
    getPlayerCount() {
        return this.remotePlayers.size;
    }
    
    getScene() {
        return this.scene;
    }
    
    getStats() {
        return {
            ...this.stats,
            renderDistance: this.renderDistance,
            seed: this.seed
        };
    }
    
    // Debug et outils de développement
    highlightChunk(chunkX, chunkZ, color = 0xff0000) {
        const chunkKey = `${chunkX},${chunkZ}`;
        const chunk = this.chunks.get(chunkKey);
        
        if (chunk) {
            // Créer un contour pour le chunk
            const geometry = new THREE.EdgesGeometry(
                new THREE.BoxGeometry(
                    GAME_CONFIG.CHUNK_WIDTH,
                    GAME_CONFIG.CHUNK_HEIGHT,
                    GAME_CONFIG.CHUNK_DEPTH
                )
            );
            const material = new THREE.LineBasicMaterial({ color });
            const wireframe = new THREE.LineSegments(geometry, material);
            
            wireframe.position.set(
                chunkX * GAME_CONFIG.CHUNK_WIDTH + GAME_CONFIG.CHUNK_WIDTH / 2,
                GAME_CONFIG.CHUNK_HEIGHT / 2,
                chunkZ * GAME_CONFIG.CHUNK_DEPTH + GAME_CONFIG.CHUNK_DEPTH / 2
            );
            
            this.scene.add(wireframe);
            
            // Supprimer après 3 secondes
            setTimeout(() => {
                this.scene.remove(wireframe);
                geometry.dispose();
                material.dispose();
            }, 3000);
        }
    }
    
    // Nettoyage
    dispose() {
        console.log('🧹 Nettoyage du ClientWorld...');
        
        // Nettoyer tous les chunks
        this.chunks.forEach(chunk => {
            chunk.dispose();
        });
        this.chunks.clear();
        
        // Nettoyer tous les joueurs distants
        this.remotePlayers.forEach(player => {
            player.dispose();
        });
        this.remotePlayers.clear();
        
        // Nettoyer la scène
        if (this.scene) {
            this.scene.clear();
        }
    }
}
