<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JScraft - Minecraft-like en JavaScript</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/mining-ui.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
        
        <!-- Interface utilisateur -->
        <div id="ui-overlay">
            <!-- Informations de debug -->
            <div id="debug-info">
                <div id="position">Position: <span>0, 0, 0</span></div>
                <div id="fps">FPS: <span>0</span></div>
                <div id="chunks">Chunks: <span>0</span></div>
                <div id="biome">Biome: <span>Plains</span></div>
                <div id="connection-status">Connexion: <span class="disconnected">Déconnecté</span></div>
                <div id="players-count">Joueurs: <span>0</span></div>
            </div>
            
            <!-- Réticule -->
            <div id="crosshair">
                <div class="crosshair-line horizontal"></div>
                <div class="crosshair-line vertical"></div>
            </div>
            
            <!-- Barre d'inventaire -->
            <div id="hotbar">
                <div class="hotbar-slot selected" data-slot="0"></div>
                <div class="hotbar-slot" data-slot="1"></div>
                <div class="hotbar-slot" data-slot="2"></div>
                <div class="hotbar-slot" data-slot="3"></div>
                <div class="hotbar-slot" data-slot="4"></div>
                <div class="hotbar-slot" data-slot="5"></div>
                <div class="hotbar-slot" data-slot="6"></div>
                <div class="hotbar-slot" data-slot="7"></div>
                <div class="hotbar-slot" data-slot="8"></div>
            </div>
            
            <!-- Interface de minage -->
            <div id="mining-progress" class="hidden">
                <div class="mining-progress-bar">
                    <div class="mining-progress-fill"></div>
                </div>
                <div class="mining-info">
                    <span id="mining-block-type">Pierre</span>
                    <span id="mining-time-left">1.0s</span>
                </div>
            </div>
            
            <!-- Notifications -->
            <div id="notifications"></div>
            
            <!-- Chat -->
            <div id="chat-container" class="hidden">
                <div id="chat-messages"></div>
                <div id="chat-input-container">
                    <input type="text" id="chat-input" placeholder="Tapez votre message..." maxlength="100">
                </div>
            </div>
        </div>
        
        <!-- Menu des options -->
        <div id="options-menu" class="hidden">
            <div class="options-content">
                <h2>Options</h2>
                
                <div class="option-group">
                    <h3>Graphiques</h3>
                    <div class="option-item">
                        <label for="render-distance">Distance de rendu:</label>
                        <input type="range" id="render-distance" min="2" max="16" value="6">
                        <span id="render-distance-value">6</span>
                    </div>
                    <div class="option-item">
                        <label for="fov">Champ de vision:</label>
                        <input type="range" id="fov" min="60" max="120" value="75">
                        <span id="fov-value">75°</span>
                    </div>
                </div>
                
                <div class="option-group">
                    <h3>Contrôles</h3>
                    <div class="option-item">
                        <label for="mouse-sensitivity">Sensibilité souris:</label>
                        <input type="range" id="mouse-sensitivity" min="0.1" max="2" step="0.1" value="1">
                        <span id="mouse-sensitivity-value">1.0</span>
                    </div>
                    <div class="option-item">
                        <label>
                            <input type="checkbox" id="auto-climb" checked>
                            Escalade automatique
                        </label>
                    </div>
                </div>
                
                <div class="option-group">
                    <h3>Réseau</h3>
                    <div class="option-item">
                        <label for="server-address">Adresse serveur:</label>
                        <input type="text" id="server-address" value="localhost:3000" placeholder="localhost:3000">
                    </div>
                    <div class="option-item">
                        <button id="reconnect-button">Reconnecter</button>
                    </div>
                </div>
                
                <div class="options-buttons">
                    <button id="reset-options">Réinitialiser</button>
                    <button id="close-options">Fermer</button>
                </div>
            </div>
        </div>
        
        <!-- Écran de connexion -->
        <div id="connection-screen">
            <div class="connection-content">
                <h1>JScraft</h1>
                <p>Connexion au serveur...</p>
                <div class="loading-spinner"></div>
                <div id="connection-error" class="hidden">
                    <p class="error-message">Impossible de se connecter au serveur</p>
                    <button id="retry-connection">Réessayer</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div id="instructions" class="hidden">
        <h3>Contrôles</h3>
        <ul>
            <li><strong>ZQSD / Flèches</strong> - Se déplacer</li>
            <li><strong>Espace</strong> - Sauter</li>
            <li><strong>Maj</strong> - Courir</li>
            <li><strong>Clic gauche</strong> - Miner</li>
            <li><strong>Clic droit</strong> - Placer un bloc</li>
            <li><strong>F1</strong> - Options</li>
            <li><strong>F</strong> - Mode vol</li>
            <li><strong>T</strong> - Chat</li>
            <li><strong>Échap</strong> - Libérer la souris</li>
        </ul>
        <p>Cliquez pour commencer à jouer</p>
    </div>
    
    <!-- Scripts -->
    <script type="module" src="js/main.js"></script>
</body>
</html>
