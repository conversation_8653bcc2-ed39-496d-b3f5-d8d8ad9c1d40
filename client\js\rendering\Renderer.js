// client/js/rendering/Renderer.js
// Gestionnaire de rendu Three.js côté client

export class Renderer {
    constructor() {
        this.renderer = null;
        this.scene = null;
        this.canvas = null;
        
        // Configuration de rendu
        this.shadowsEnabled = true;
        this.antialias = true;
        this.pixelRatio = Math.min(window.devicePixelRatio, 2);
        
        // Statistiques de rendu
        this.stats = {
            drawCalls: 0,
            triangles: 0,
            geometries: 0,
            textures: 0,
            lastFrameTime: 0
        };
        
        console.log('🎨 Renderer initialisé');
    }
    
    async init() {
        try {
            // Créer la scène
            this.scene = new THREE.Scene();
            this.scene.background = new THREE.Color(0x87CEEB); // Couleur ciel
            
            // Obtenir le canvas
            this.canvas = document.getElementById('game-canvas');
            if (!this.canvas) {
                throw new Error('Canvas #game-canvas non trouvé');
            }
            
            // Créer le renderer WebGL
            this.renderer = new THREE.WebGLRenderer({
                canvas: this.canvas,
                antialias: this.antialias,
                powerPreference: 'high-performance'
            });
            
            // Configuration du renderer
            this.renderer.setSize(window.innerWidth, window.innerHeight);
            this.renderer.setPixelRatio(this.pixelRatio);
            this.renderer.shadowMap.enabled = this.shadowsEnabled;
            this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            this.renderer.outputEncoding = THREE.sRGBEncoding;
            this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
            this.renderer.toneMappingExposure = 1.0;
            
            // Configurer l'éclairage
            this.setupLighting();
            
            // Configurer le brouillard
            this.setupFog();
            
            console.log('✅ Renderer initialisé avec succès');
            console.log(`📊 WebGL: ${this.renderer.capabilities.isWebGL2 ? 'WebGL2' : 'WebGL1'}`);
            console.log(`📊 Max textures: ${this.renderer.capabilities.maxTextures}`);
            
        } catch (error) {
            console.error('❌ Erreur initialisation renderer:', error);
            throw error;
        }
    }
    
    setupLighting() {
        // Lumière directionnelle (soleil)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = this.shadowsEnabled;
        
        if (this.shadowsEnabled) {
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.5;
            directionalLight.shadow.camera.far = 500;
            directionalLight.shadow.camera.left = -100;
            directionalLight.shadow.camera.right = 100;
            directionalLight.shadow.camera.top = 100;
            directionalLight.shadow.camera.bottom = -100;
            directionalLight.shadow.bias = -0.0001;
        }
        
        this.scene.add(directionalLight);
        
        // Lumière ambiante
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // Stocker les références pour modification dynamique
        this.directionalLight = directionalLight;
        this.ambientLight = ambientLight;
        
        console.log('💡 Éclairage configuré');
    }
    
    setupFog() {
        // Brouillard pour masquer la limite de rendu
        const fogColor = 0x87CEEB;
        const fogNear = 100;
        const fogFar = 200;
        
        this.scene.fog = new THREE.Fog(fogColor, fogNear, fogFar);
        console.log('🌫️ Brouillard configuré');
    }
    
    render(scene, camera) {
        if (!this.renderer || !scene || !camera) {
            console.warn('⚠️ Renderer, scene ou camera manquant');
            return;
        }
        
        const startTime = performance.now();
        
        try {
            // Mettre à jour les statistiques avant le rendu
            this.updateStats();
            
            // Rendre la scène
            this.renderer.render(scene, camera);
            
            // Calculer le temps de rendu
            this.stats.lastFrameTime = performance.now() - startTime;
            
        } catch (error) {
            console.error('❌ Erreur lors du rendu:', error);
        }
    }
    
    updateStats() {
        const info = this.renderer.info;
        
        this.stats.drawCalls = info.render.calls;
        this.stats.triangles = info.render.triangles;
        this.stats.geometries = info.memory.geometries;
        this.stats.textures = info.memory.textures;
    }
    
    handleResize() {
        if (!this.renderer) return;
        
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        console.log(`📐 Renderer redimensionné: ${width}x${height}`);
    }
    
    // Gestion de la qualité graphique
    setQuality(level) {
        switch (level) {
            case 'low':
                this.renderer.setPixelRatio(1);
                this.renderer.shadowMap.enabled = false;
                this.shadowsEnabled = false;
                break;
                
            case 'medium':
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
                this.renderer.shadowMap.enabled = true;
                this.shadowsEnabled = true;
                break;
                
            case 'high':
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                this.renderer.shadowMap.enabled = true;
                this.shadowsEnabled = true;
                break;
        }
        
        console.log(`🎨 Qualité graphique: ${level}`);
    }
    
    // Gestion du cycle jour/nuit
    setTimeOfDay(time) {
        // time: 0 = minuit, 0.5 = midi, 1 = minuit suivant
        const sunAngle = time * Math.PI * 2;
        
        // Position du soleil
        const sunX = Math.cos(sunAngle) * 100;
        const sunY = Math.sin(sunAngle) * 100;
        const sunZ = 50;
        
        this.directionalLight.position.set(sunX, Math.max(sunY, 10), sunZ);
        
        // Intensité de la lumière selon l'heure
        const dayIntensity = Math.max(0.1, Math.sin(sunAngle));
        this.directionalLight.intensity = dayIntensity;
        
        // Couleur de la lumière
        if (sunY > 0) {
            // Jour
            this.directionalLight.color.setHex(0xffffff);
            this.ambientLight.intensity = 0.4;
        } else {
            // Nuit
            this.directionalLight.color.setHex(0x404080);
            this.ambientLight.intensity = 0.1;
        }
        
        // Couleur du ciel
        const skyColor = this.getSkyColor(time);
        this.scene.background.setHex(skyColor);
        if (this.scene.fog) {
            this.scene.fog.color.setHex(skyColor);
        }
    }
    
    getSkyColor(time) {
        // Couleurs du ciel selon l'heure
        const colors = {
            night: 0x000033,
            dawn: 0xff6b35,
            day: 0x87ceeb,
            dusk: 0xff4500
        };
        
        if (time < 0.2 || time > 0.8) {
            return colors.night;
        } else if (time < 0.3) {
            // Aube
            const t = (time - 0.2) / 0.1;
            return this.lerpColor(colors.night, colors.dawn, t);
        } else if (time < 0.4) {
            // Matin
            const t = (time - 0.3) / 0.1;
            return this.lerpColor(colors.dawn, colors.day, t);
        } else if (time < 0.6) {
            // Jour
            return colors.day;
        } else if (time < 0.7) {
            // Soir
            const t = (time - 0.6) / 0.1;
            return this.lerpColor(colors.day, colors.dusk, t);
        } else {
            // Crépuscule
            const t = (time - 0.7) / 0.1;
            return this.lerpColor(colors.dusk, colors.night, t);
        }
    }
    
    lerpColor(color1, color2, t) {
        const c1 = new THREE.Color(color1);
        const c2 = new THREE.Color(color2);
        return c1.lerp(c2, t).getHex();
    }
    
    // Effets visuels
    addParticleEffect(position, type, count = 10) {
        // TODO: Implémenter les effets de particules
        console.log(`✨ Effet de particules: ${type} à`, position);
    }
    
    // Capture d'écran
    takeScreenshot() {
        if (!this.renderer) return null;
        
        this.renderer.render(this.scene, this.camera);
        return this.renderer.domElement.toDataURL('image/png');
    }
    
    // Informations de debug
    getDebugInfo() {
        return {
            ...this.stats,
            capabilities: {
                webgl2: this.renderer.capabilities.isWebGL2,
                maxTextures: this.renderer.capabilities.maxTextures,
                maxTextureSize: this.renderer.capabilities.maxTextureSize,
                maxVertexTextures: this.renderer.capabilities.maxVertexTextures
            },
            settings: {
                shadowsEnabled: this.shadowsEnabled,
                antialias: this.antialias,
                pixelRatio: this.pixelRatio
            }
        };
    }
    
    // Nettoyage
    dispose() {
        console.log('🧹 Nettoyage du renderer...');
        
        if (this.renderer) {
            this.renderer.dispose();
            this.renderer = null;
        }
        
        if (this.scene) {
            // Nettoyer tous les objets de la scène
            this.scene.traverse((object) => {
                if (object.geometry) {
                    object.geometry.dispose();
                }
                if (object.material) {
                    if (Array.isArray(object.material)) {
                        object.material.forEach(material => material.dispose());
                    } else {
                        object.material.dispose();
                    }
                }
            });
            
            this.scene = null;
        }
    }

    getScene() {
        return this.scene;
    }
}
