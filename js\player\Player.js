// Three.js est charge globalement depuis le CDN
const THREE = window.THREE;

export class Player {
    constructor(scene, initialSettings = null) {
        // Recuperer le logger global
        this.logger = window.GameLogger;
        const PLAYER_VERSION = `Player-v${Date.now()}`;
        this.logger.info('Player initialise', {
            version: PLAYER_VERSION,
            features: {
                escaladeAutomatique: 'Desactivee apres stabilisation',
                gravite: 'Amelioree pour detection sol',
                limiteInferieure: 'Desactivee apres stabilisation',
                logsDetailles: 'Actives pour diagnostic'
            },
            initialSettings: initialSettings ? 'Fournis' : 'Par defaut'
        });
        // Stocker la version pour verification
        this.version = PLAYER_VERSION;
        // Enregistrer ce module aupres du systeme de versioning (si disponible)
        if (window.VERSION_CONFIG && window.VERSION_CONFIG.verifyModuleIntegrity) {
            const moduleRegistry = window.VERSION_CONFIG.verifyModuleIntegrity();
            moduleRegistry.registerModule('Player', PLAYER_VERSION);
        }
        // Configuration amelioree de la camera
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        // Position de depart du joueur au niveau du sol (sera ajustee automatiquement)
        this.camera.position.set(0, 100, 0); // Position temporaire haute, sera ajustee au premier update
        // Proprietes de mouvement et collision
        this.velocity = new THREE.Vector3();
        this.onGround = false;
        this.eyeHeight = 1.7; // Hauteur des yeux par rapport au sol (en blocs)
        this.playerHeight = 1.8; // Taille du joueur (1.8 blocs)
        this.stepHeight = 0.5; // Hauteur max d'escalade automatique (0.5 bloc)
        this.collisionRadius = 0.3; // Rayon de collision horizontal
        this.collisionSegments = 8; // Nombre de points de collision pour une detection precise
        this.needsGroundCheck = true; // Flag pour verifier le sol au premier update
        this.flyMode = false; // Mode vol active/desactive
        // Proprietes de saut robustes
        this.isJumping = false; // Flag pour indiquer qu'un saut est en cours
        this.jumpStartTime = 0; // Timestamp du debut du saut
        this.jumpDuration = 500; // Duree minimale d'un saut (500ms)
        // Options d'escalade - Appliquer les parametres initiaux directement pour eviter la race condition
        if (initialSettings) {
            this.autoClimb = initialSettings.autoClimb !== undefined ? initialSettings.autoClimb : false;
            this.autoClimbTrees = initialSettings.autoClimbTrees !== undefined ? initialSettings.autoClimbTrees : false;
            this.logger.info('PARAMETRES ESCALADE INITIALISES depuis les settings', {
                autoClimb: this.autoClimb,
                autoClimbTrees: this.autoClimbTrees,
                source: 'initialSettings'
            });
        } else {
            // Valeurs par defaut securisees (escalade desactivee par defaut)
            this.autoClimb = false; // Escalade automatique du terrain
            this.autoClimbTrees = false; // Escalade automatique des arbres
            this.logger.info('PARAMETRES ESCALADE INITIALISES par defaut', {
                autoClimb: this.autoClimb,
                autoClimbTrees: this.autoClimbTrees,
                source: 'default'
            });
        }
        // Cache pour eviter les appels repetes a getGroundHeightAt
        this.lastGroundCheck = { x: null, z: null, result: null, time: 0 };
        this.groundCheckCooldown = 100; // ms entre les verifications (reduit pour meilleure reactivite)
        // Systeme de stabilisation pour reduire les logs
        this.isStable = false;
        this.stableFrameCount = 0;
        this.requiredStableFrames = 60; // 1 seconde a 60fps
        // Protection contre l'escalade repetee
        this.lastStepTime = 0;
        this.stepCooldown = 100; // 100ms entre les escalades
        // Systeme de minage
        this.isMining = false;
        this.miningStartTime = 0;
        this.miningTarget = null;
        this.miningProgress = 0;
        this.miningDuration = 1000; // 1 seconde pour miner un bloc
        this.reach = 5; // Portee de minage en blocs
        // Animation de bras
        this.armSwingTime = 0;
        this.armSwingDuration = 300; // 300ms par swing
        this.isSwinging = false;
        // Inventaire
        this.inventory = new Map();
        this.inventoryOpen = false;
        this.maxInventorySlots = 36; // 9x4 comme Minecraft
        // Interface utilisateur
        this.createUI();

        // Systeme de verrouillage pour l'escalade automatique
        this._climbingLocked = false;
        this._lastClimbCheck = 0;
        this._climbCheckCooldown = 100; // ms entre les verifications
        // Systeme de stabilisation forcee pour eviter les chutes infinies
        this.lastStablePosition = { x: 0, y: 70, z: 0 };
        this.fallTime = 0;
        this.maxFallTime = 2000; // 2 secondes max de chute
        // Systeme de desactivation de la physique apres stabilisation
        this.physicsEnabled = true;
        this.lastLogTime = 0; // Pour throttling des logs
        this.spawnAttempts = 0; // Compteur de tentatives de spawn
        this.spawnPosition = null; // Position de spawn calculee

        // Cette methode peut etre appelee depuis main.js pour mettre a jour la camera
        this.updateCameraAspect = (width, height) => {
            this.camera.aspect = width / height;
            this.camera.updateProjectionMatrix();
            this.logger.debug('Aspect ratio de la camera mis a jour', {
                width: width,
                height: height,
                aspect: this.camera.aspect.toFixed(3),
                fov: this.camera.fov
            });
        };
    }

    update(delta, world) {
        // Verifier que delta est valide
        if (isNaN(delta) || delta <= 0) {
            delta = 0.016; // Valeur par defaut
        }
        // Logs intelligents avec le nouveau systeme
        if (window.SmartLogger) {
            if (this.needsGroundCheck || Math.abs(this.velocity.y) > 0.1) {
                window.SmartLogger.debug('PHYSICS', 'Mise a jour joueur', {
                    position: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    velocity: {
                        y: Math.round(this.velocity.y * 1000) / 1000
                    },
                    onGround: this.onGround,
                    needsGroundCheck: this.needsGroundCheck
                });
            }
        } else {
            // Fallback vers l'ancien systeme avec throttling
            if ((this.needsGroundCheck || Math.abs(this.velocity.y) > 0.1) && 
                (!this.lastLogTime || Date.now() - this.lastLogTime > 1000)) {
                this.logger.physics('Mise a jour joueur', {
                    position: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    velocity: {
                        y: Math.round(this.velocity.y * 1000) / 1000
                    },
                    onGround: this.onGround,
                    needsGroundCheck: this.needsGroundCheck
                });
                this.lastLogTime = Date.now();
            }
        }
        // Au premier update, positionner le joueur sur le sol
        if (this.needsGroundCheck && world && !this.flyMode) {
            this.logger.physics('Debut du positionnement initial');
            const positioningSuccess = this.findGroundPosition(world);
            if (positioningSuccess) {
                // Marquer le positionnement initial comme termine
                this.needsGroundCheck = false;
                this.onGround = true;
                this.velocity.y = 0;
                this.logger.physics('Positionnement initial reussi', {
                    finalPosition: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    physicsEnabled: true
                });
            } else {
                // Ajouter un compteur de tentatives pour eviter les boucles infinies
                if (!this.spawnAttempts) this.spawnAttempts = 0;
                this.spawnAttempts++;

                if (this.spawnAttempts >= 5) {
                    // Trop de tentatives - forcer une position de secours
                    this.logger.warn('Trop de tentatives de spawn - position de secours forcee', {
                        attempts: this.spawnAttempts
                    });
                    this.camera.position.set(0, 70, 0);
                    this.needsGroundCheck = false;
                    this.onGround = true;
                    this.velocity.y = 0;
                } else {
                    this.logger.warn('Positionnement initial echoue', {
                        reason: 'Chunk pas encore genere',
                        action: 'Attente de generation des chunks',
                        attempt: this.spawnAttempts
                    });
                }
            }
        }
        // Position avant mouvement (pour detection de collision)
        const previousPosition = this.camera.position.clone();
        
        // LOGS DETAILLES - Etat avant mouvement
        this.logger.debug('=== DEBUT UPDATE PLAYER ===', {
            delta: Math.round(delta * 1000) / 1000,
            position: {
                x: Math.round(this.camera.position.x * 100) / 100,
                y: Math.round(this.camera.position.y * 100) / 100,
                z: Math.round(this.camera.position.z * 100) / 100
            },
            velocity: {
                x: Math.round(this.velocity.x * 100) / 100,
                y: Math.round(this.velocity.y * 100) / 100,
                z: Math.round(this.velocity.z * 100) / 100
            },
            onGround: this.onGround,
            flyMode: this.flyMode
        });
        
        // Decomposition du mouvement en etapes separees (horizontal et vertical)
        const movement = this.velocity.clone().multiplyScalar(delta);
        
        // LOGS DETAILLES - Calcul du mouvement
        this.logger.debug('Calcul du mouvement', {
            velocityBeforeScale: {
                x: Math.round(this.velocity.x * 100) / 100,
                y: Math.round(this.velocity.y * 100) / 100,
                z: Math.round(this.velocity.z * 100) / 100
            },
            delta: Math.round(delta * 1000) / 1000,
            calculatedMovement: {
                x: Math.round(movement.x * 1000) / 1000,
                y: Math.round(movement.y * 1000) / 1000,
                z: Math.round(movement.z * 1000) / 1000
            }
        });
        // 1. Appliquer le mouvement horizontal (X et Z) d'abord
        const horizontalMovement = new THREE.Vector3(movement.x, 0, movement.z);
        const positionBeforeHorizontal = this.camera.position.clone();
        this.camera.position.add(horizontalMovement);
        
        // LOGS DETAILLES - Apres mouvement horizontal
        this.logger.debug('Apres mouvement horizontal', {
            horizontalMovement: {
                x: Math.round(horizontalMovement.x * 1000) / 1000,
                z: Math.round(horizontalMovement.z * 1000) / 1000
            },
            positionBefore: {
                x: Math.round(positionBeforeHorizontal.x * 100) / 100,
                z: Math.round(positionBeforeHorizontal.z * 100) / 100
            },
            positionAfter: {
                x: Math.round(this.camera.position.x * 100) / 100,
                z: Math.round(this.camera.position.z * 100) / 100
            },
            actualMovement: {
                x: Math.round((this.camera.position.x - positionBeforeHorizontal.x) * 1000) / 1000,
                z: Math.round((this.camera.position.z - positionBeforeHorizontal.z) * 1000) / 1000
            }
        });
        
        // 2. Appliquer le mouvement vertical AVANT la physique
        const yBeforeVertical = this.camera.position.y;
        this.camera.position.y += movement.y;
        
        // LOGS DETAILLES - Apres mouvement vertical
        this.logger.debug('Apres mouvement vertical', {
            verticalMovement: Math.round(movement.y * 1000) / 1000,
            yBefore: Math.round(yBeforeVertical * 100) / 100,
            yAfter: Math.round(this.camera.position.y * 100) / 100,
            actualYMovement: Math.round((this.camera.position.y - yBeforeVertical) * 1000) / 1000
        });
        
        // 3. Systeme de physique robuste APRES le mouvement
        if (!this.flyMode && world) {
            const playerPos = this.camera.position;
            const feetY = playerPos.y - this.eyeHeight;
            let groundHeight = this.getGroundHeightWithFallback(world, playerPos.x, playerPos.z);
            
            // LOGS DE DEBUG DETAILLES pour la detection du sol
            this.logger.debug('DETECTION DU SOL', {
                playerPos: {
                    x: Math.round(playerPos.x * 100) / 100,
                    y: Math.round(playerPos.y * 100) / 100,
                    z: Math.round(playerPos.z * 100) / 100
                },
                feetY: Math.round(feetY * 100) / 100,
                groundHeight: groundHeight,
                groundHeightFound: groundHeight !== null && groundHeight !== undefined
            });
            
            // Appliquer la physique basee sur le sol trouve
            if (groundHeight !== null && groundHeight !== undefined) {
                const distanceToGround = feetY - (groundHeight + 1);
                const targetGroundY = groundHeight + 1 + this.eyeHeight; // Position Y ideale du joueur (yeux) sur le sol
                
                // LOGS DE DEBUG DETAILLES pour les calculs de distance
                this.logger.debug('CALCULS DE DISTANCE AU SOL', {
                    feetY: Math.round(feetY * 100) / 100,
                    groundHeight: groundHeight,
                    groundPlusOne: groundHeight + 1,
                    distanceToGround: Math.round(distanceToGround * 1000) / 1000,
                    targetGroundY: Math.round(targetGroundY * 100) / 100,
                    velocityY: Math.round(this.velocity.y * 100) / 100,
                    currentOnGround: this.onGround
                });
                
                const currentTime = Date.now();

                // Logic for jumping (remains unchanged)
                if (this.isJumping && (currentTime - this.jumpStartTime) < this.jumpDuration) {
                    this.onGround = false;
                    if (this.velocity.y <= 0) {
                        this.velocity.y -= 8 * delta; // Gravite reduite pendant la descente du saut
                    }
                    this.logger.debug('PHYSIQUE - MODE SAUT', {
                        isJumping: this.isJumping,
                        jumpDuration: this.jumpDuration,
                        timeSinceJump: currentTime - this.jumpStartTime,
                        onGround: this.onGround
                    });
                } else {
                    this.isJumping = false; // S'assurer que isJumping est false apres la duree du saut

                    // Logique de stabilisation au sol (toujours active pour empecher de tomber a travers)
                    // Si le joueur tombe ou est tres proche du sol, le "snapper" a cette position.
                    // La tolerance ici est cruciale pour que le joueur ne "tremble" pas et se colle bien.
                    const groundSnapTolerance = 0.1; // Ajustement minimal pour le collage au sol

                    this.logger.debug('VERIFICATION STABILISATION', {
                        velocityY: Math.round(this.velocity.y * 100) / 100,
                        distanceToGround: Math.round(distanceToGround * 1000) / 1000,
                        absDistanceToGround: Math.round(Math.abs(distanceToGround) * 1000) / 1000,
                        groundSnapTolerance: groundSnapTolerance,
                        shouldSnap: this.velocity.y <= 0 && Math.abs(distanceToGround) <= groundSnapTolerance,
                        shouldFall: distanceToGround > groundSnapTolerance
                    });

                    if (this.velocity.y <= 0 && Math.abs(distanceToGround) <= groundSnapTolerance) {
                        // ATTERRISSAGE NORMAL ou STABILISATION sur le sol existant
                        this.camera.position.y = targetGroundY;
                        this.velocity.y = 0;
                        this.onGround = true;
                        this.fallTime = 0;
                        this.lastStablePosition = { x: playerPos.x, y: targetGroundY, z: playerPos.z };
                        this.logger.info('PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol', {
                            oldY: Math.round((targetGroundY - this.eyeHeight) * 100) / 100,
                            newY: Math.round(this.camera.position.y * 100) / 100,
                            targetGroundY: Math.round(targetGroundY * 100) / 100,
                            distanceToGround: Math.round(distanceToGround * 1000) / 1000,
                            onGround: this.onGround
                        });

                    } else if (distanceToGround < -groundSnapTolerance) { // Le joueur est EN DESSOUS du sol detecte
                        // CORRECTION CRITIQUE: Remonter progressivement le joueur
                        this.camera.position.y += Math.abs(distanceToGround) * 0.5; // Remonte de 50% de la distance
                        this.velocity.y = Math.max(0, this.velocity.y); // Empeche de tomber plus bas
                        this.onGround = false; // Pas sur le sol, en cours de correction
                        this.logger.warn('PHYSICS_UNDERGROUND_CORRECTION - Correction de la position du joueur sous le sol', {
                            oldY: Math.round(playerPos.y * 100) / 100,
                            newY: Math.round(this.camera.position.y * 100) / 100,
                            distanceToGround: Math.round(distanceToGround * 1000) / 1000
                        });
                    } else if (this.velocity.y <= 0 && feetY <= groundHeight + 1) {
                        // NOUVELLE LOGIQUE D'ATTERRISSAGE: Le joueur etait en chute et a touche le sol
                        this.camera.position.y = targetGroundY;
                        this.velocity.y = 0;
                        this.onGround = true;
                        this.fallTime = 0;
                        this.lastStablePosition = { x: playerPos.x, y: targetGroundY, z: playerPos.z };
                        this.logger.info('PHYSICS_LANDING - Joueur stabilise au sol apres une chute.', {
                            newY: Math.round(this.camera.position.y * 100) / 100,
                            targetGroundY: Math.round(targetGroundY * 100) / 100,
                            feetY: Math.round(feetY * 100) / 100,
                            groundHeight: groundHeight
                        });
                    } else if (distanceToGround > groundSnapTolerance) { // Le joueur est clairement au-dessus du sol
                        this.onGround = false;
                        this.velocity.y -= 12 * delta; // Gravite normale
                        if (this.velocity.y < -15) this.velocity.y = -15; // Vitesse de chute max
                        this.fallTime += delta * 1000;
                        this.logger.debug('PHYSIQUE - CHUTE LIBRE', {
                            distanceToGround: Math.round(distanceToGround * 1000) / 1000,
                            velocityY: Math.round(this.velocity.y * 100) / 100,
                            fallTime: Math.round(this.fallTime),
                            onGround: this.onGround
                        });
                        if (this.fallTime > 3000) { // Teleportation d'urgence si chute trop longue
                            this.camera.position.copy(this.lastStablePosition);
                            this.velocity.y = 0;
                            this.onGround = true;
                            this.fallTime = 0;
                            this.logger.warn('Repositionnement d\'urgence - chute infinie');
                        }
                    } else if (this.velocity.y > 0 && !this.isJumping) { // Le joueur se deplace vers le haut de maniere non naturelle (pas un saut)
                        this.onGround = false; // Ne peut pas etre au sol s'il monte sans sauter
                        this.velocity.y -= 15 * delta; // Appliquer une forte gravite pour le tirer vers le bas
                        this.logger.debug('PHYSICS_UNNATURAL_UPWARD_CORRECTION', 'Correction mouvement ascendant non naturel', {
                            currentY: Math.round(playerPos.y * 100) / 100,
                            velocityY: Math.round(this.velocity.y * 100) / 100
                        });
                    }
                }
            } else { // Pas de sol detecte du tout (joueur potentiellement tres haut ou dans le vide)
                this.onGround = false;
                this.velocity.y -= 18 * delta; // Gravite plus forte
                if (this.velocity.y < -20) this.velocity.y = -20; // Vitesse de chute max
                this.fallTime += delta * 1000;
                this.logger.warn('AUCUN SOL DETECTE', {
                    playerPos: {
                        x: Math.round(playerPos.x * 100) / 100,
                        y: Math.round(playerPos.y * 100) / 100,
                        z: Math.round(playerPos.z * 100) / 100
                    },
                    groundHeight: groundHeight,
                    fallTime: Math.round(this.fallTime),
                    velocityY: Math.round(this.velocity.y * 100) / 100
                });
                if (this.fallTime > 3000) { // Teleportation d'urgence si chute trop longue
                    if (this.lastStablePosition && this.lastStablePosition.y > 0) {
                        this.camera.position.copy(this.lastStablePosition);
                    } else {
                        this.camera.position.set(0, 70, 0); // Revenir a une position par defaut
                    }
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;
                    this.logger.warn('Repositionnement d\'urgence effectue (pas de sol)');
                }
            }
        } else if (!this.flyMode) { // Pas en mode vol, mais le monde n'est pas disponible (cas rare)
            this.velocity.y -= 18 * delta;
            if (this.velocity.y < -20) this.velocity.y = -20;
        }



        // 5. Collision avec le plafond amelioree
        if (!this.flyMode && world && world.hasCollisionAt) {
            const playerPos = this.camera.position;
            const headY = playerPos.y + 0.2; // Un peu plus haut pour etre sur
            const hasHeadCollision = world.hasCollisionAt(playerPos.x, headY, playerPos.z, this.collisionRadius);

            if (hasHeadCollision) {
                this.logger.warn('--- COLLISION TETE DETECTEE ---', {
                    headY: headY.toFixed(2),
                    playerY: playerPos.y.toFixed(2),
                    velocityY: this.velocity.y.toFixed(2)
                });

                if (this.velocity.y > 0) {
                    this.velocity.y = 0; // Arreter le mouvement ascendant
                }
                // Repousser le joueur vers le bas pour eviter de rester coince
                const newY = Math.floor(headY) - 0.2 - 0.01;
                this.logger.warn('Correction Plafond: Repositionnement vers le bas.', {
                    oldY: playerPos.y.toFixed(2),
                    newY: newY.toFixed(2)
                });
                this.camera.position.y = newY;
            }
        }

        // 6. Appliquer la friction au sol
        if (this.onGround && !this.flyMode) {
            this.velocity.x *= 0.8; // Friction horizontale
            this.velocity.z *= 0.8;
        }

        // Logs de fin d'update
        this.logger.debug('=== FIN UPDATE PLAYER ===', {
            finalPosition: {
                x: Math.round(this.camera.position.x * 100) / 100,
                y: Math.round(this.camera.position.y * 100) / 100,
                z: Math.round(this.camera.position.z * 100) / 100
            },
            finalVelocity: {
                x: Math.round(this.velocity.x * 100) / 100,
                y: Math.round(this.velocity.y * 100) / 100,
                z: Math.round(this.velocity.z * 100) / 100
            },
            onGround: this.onGround,
            flyMode: this.flyMode
        });

        // Mettre a jour le minage
        if (this.isMining) {
            this.updateMining(delta);
        }

        // Mettre a jour l'animation de bras
        if (this.isSwinging) {
            this.armSwingTime += delta * 1000;
            if (this.armSwingTime >= this.armSwingDuration) {
                this.armSwingTime = 0;
            }
        }
    }

    // Methode amelioree pour obtenir la hauteur du sol avec fallback
    getGroundHeightWithFallback(world, x, z) {
        const currentTime = Date.now();
        const cacheKey = `${Math.floor(x)},${Math.floor(z)}`;
        
        // Verifier le cache
        if (this.lastGroundCheck.x === Math.floor(x) && 
            this.lastGroundCheck.z === Math.floor(z) && 
            (currentTime - this.lastGroundCheck.time) < this.groundCheckCooldown) {
            this.logger.debug('CACHE HIT - Utilisation du cache pour la detection du sol', {
                cacheKey: cacheKey,
                cachedResult: this.lastGroundCheck.result,
                timeSinceLastCheck: currentTime - this.lastGroundCheck.time
            });
            return this.lastGroundCheck.result;
        }
        
        let groundHeight = null;
        
        this.logger.debug('DEBUT DETECTION SOL - Nouvelle recherche', {
            x: Math.round(x * 100) / 100,
            z: Math.round(z * 100) / 100,
            cacheKey: cacheKey,
            worldAvailable: !!world,
            getGroundHeightAtAvailable: !!(world && world.getGroundHeightAt)
        });
        
        // Methode 1: Utiliser getGroundHeightAt si disponible
        if (world.getGroundHeightAt) {
            try {
                groundHeight = world.getGroundHeightAt(x, z);
                this.logger.debug('METHODE 1 - getGroundHeightAt', {
                    x: Math.round(x * 100) / 100,
                    z: Math.round(z * 100) / 100,
                    result: groundHeight,
                    success: groundHeight !== null && groundHeight !== undefined
                });
            } catch (e) {
                this.logger.debug('METHODE 1 ECHEC - Erreur getGroundHeightAt:', {
                    error: e.message,
                    x: Math.round(x * 100) / 100,
                    z: Math.round(z * 100) / 100
                });
            }
        } else {
            this.logger.debug('METHODE 1 INDISPONIBLE - getGroundHeightAt non disponible');
        }
        
        // Methode 2: Fallback manuel si getGroundHeightAt echoue
        if (groundHeight === null || groundHeight === undefined) {
            const chunkX = Math.floor(x / 16);
            const chunkZ = Math.floor(z / 16);
            const chunkKey = `${chunkX},${chunkZ}`;
            
            this.logger.debug('METHODE 2 - Fallback manuel', {
                chunkX: chunkX,
                chunkZ: chunkZ,
                chunkKey: chunkKey,
                chunksAvailable: !!(world.chunks),
                chunkExists: !!(world.chunks && world.chunks.has(chunkKey)),
                totalChunks: world.chunks ? world.chunks.size : 0
            });
            
            if (world.chunks && world.chunks.has(chunkKey)) {
                const chunkData = world.chunks.get(chunkKey);
                const chunk = chunkData?.chunk;
                
                this.logger.debug('CHUNK TROUVE', {
                    chunkKey: chunkKey,
                    chunkDataExists: !!chunkData,
                    chunkExists: !!chunk,
                    getBlockAtAvailable: !!(chunk && chunk.getBlockAt)
                });
                
                if (chunk && chunk.getBlockAt) {
                    const localX = Math.max(0, Math.min(15, Math.floor(x) - chunkX * 16));
                    const localZ = Math.max(0, Math.min(15, Math.floor(z) - chunkZ * 16));
                    
                    this.logger.debug('RECHERCHE DANS CHUNK', {
                        localX: localX,
                        localZ: localZ,
                        globalX: Math.floor(x),
                        globalZ: Math.floor(z)
                    });
                    
                    // Chercher le bloc le plus haut
                    for (let y = 127; y >= 0; y--) {
                        try {
                            const blockType = chunk.getBlockAt(localX, y, localZ);
                            if (blockType && blockType !== 0) {
                                groundHeight = y;
                                this.logger.debug('SOL TROUVE DANS CHUNK', {
                                    localX: localX,
                                    localZ: localZ,
                                    y: y,
                                    blockType: blockType,
                                    groundHeight: groundHeight
                                });
                                break;
                            }
                        } catch (e) {
                            // Continue silencieusement
                            continue;
                        }
                    }
                    
                    if (groundHeight === null) {
                        this.logger.warn('AUCUN BLOC SOLIDE TROUVE DANS CHUNK', {
                            chunkKey: chunkKey,
                            localX: localX,
                            localZ: localZ
                        });
                    }
                } else {
                    this.logger.warn('CHUNK INVALIDE', {
                        chunkKey: chunkKey,
                        chunkDataExists: !!chunkData,
                        chunkExists: !!chunk,
                        getBlockAtAvailable: !!(chunk && chunk.getBlockAt)
                    });
                }
            } else {
                this.logger.warn('CHUNK NON TROUVE', {
                    chunkKey: chunkKey,
                    chunksAvailable: !!(world.chunks),
                    totalChunks: world.chunks ? world.chunks.size : 0,
                    availableChunks: world.chunks ? Array.from(world.chunks.keys()).slice(0, 5) : []
                });
            }
        }
        
        // Mettre a jour le cache
        this.lastGroundCheck = {
            x: Math.floor(x),
            z: Math.floor(z),
            result: groundHeight,
            time: currentTime
        };
        
        this.logger.debug('RESULTAT FINAL DETECTION SOL', {
            x: Math.round(x * 100) / 100,
            z: Math.round(z * 100) / 100,
            groundHeight: groundHeight,
            success: groundHeight !== null && groundHeight !== undefined,
            cacheUpdated: true
        });
        
        return groundHeight;
    }



    // Methode pour mettre a jour les parametres apres l'initialisation
    // Utilisee pour eviter les race conditions lors du changement de parametres
    onSettingsUpdated(newSettings = null) {
        this.logger.info('MISE A JOUR DES PARAMETRES D\'ESCALADE', {
            oldAutoClimb: this.autoClimb,
            oldAutoClimbTrees: this.autoClimbTrees
        });
        
        // CORRECTION CLE: Mettre a jour les proprietes internes du joueur avec les NOUVEAUX parametres
        if (newSettings) {
            this.autoClimb = newSettings.autoClimb !== undefined ? newSettings.autoClimb : this.autoClimb;
            this.autoClimbTrees = newSettings.autoClimbTrees !== undefined ? newSettings.autoClimbTrees : this.autoClimbTrees;
            this.logger.info('PARAMETRES ESCALADE MISE A JOUR depuis newSettings', {
                autoClimb: this.autoClimb,
                autoClimbTrees: this.autoClimbTrees,
                source: 'newSettings'
            });
        } else if (window.optionsManager && window.optionsManager.settings) {
            // Fallback: si newSettings n'est pas fourni, essayer de les recuperer depuis window.optionsManager.settings
            const oldAutoClimb = this.autoClimb;
            const oldAutoClimbTrees = this.autoClimbTrees;
            
            this.autoClimb = window.optionsManager.settings.autoClimb !== undefined ? window.optionsManager.settings.autoClimb : this.autoClimb;
            this.autoClimbTrees = window.optionsManager.settings.autoClimbTrees !== undefined ? window.optionsManager.settings.autoClimbTrees : this.autoClimbTrees;
            
            // Verifier si les parametres ont reellement change
            if (oldAutoClimb !== this.autoClimb || oldAutoClimbTrees !== this.autoClimbTrees) {
                this.logger.info('PARAMETRES ESCALADE MISE A JOUR depuis window.optionsManager', {
                    autoClimb: this.autoClimb,
                    autoClimbTrees: this.autoClimbTrees,
                    source: 'window.optionsManager',
                    changed: true
                });
            } else {
                this.logger.debug('Parametres d\'escalade inchanges, pas de mise a jour necessaire');
            }
        } else {
            this.logger.warn('onSettingsUpdated appele sans nouveaux parametres ni window.optionsManager disponibles.');
        }

        // Forcer la mise a jour des variables internes qui dependent de autoClimb
        // Reinitialiser le cache de verification d'escalade
        this._lastClimbCheck = 0;
        this._climbingLocked = false; // Assurez-vous que cela n'est pas bloque apres une mise a jour des parametres
        
        this.logger.info('PARAMETRES D\'ESCALADE RECHARGES', {
            newAutoClimb: this.autoClimb,
            newAutoClimbTrees: this.autoClimbTrees,
            climbingLocked: this._climbingLocked
        });
    }

    // Methode pour trouver la position du sol au demarrage (spawn dans les plaines)
    findGroundPosition(world) {
        // Utiliser une position de spawn plus proche de l'origine pour eviter les chunks non generes
        if (!this.spawnPosition) {
            // Chercher d'abord dans les chunks deja generes
            const availableChunks = Array.from(world.chunks.keys());
            if (availableChunks.length > 0) {
                // Utiliser le premier chunk disponible proche de l'origine
                const centerChunks = availableChunks.filter(key => {
                    const [x, z] = key.split(',').map(Number);
                    return Math.abs(x) <= 2 && Math.abs(z) <= 2; // Chunks proches de l'origine
                });
                if (centerChunks.length > 0) {
                    const [chunkX, chunkZ] = centerChunks[0].split(',').map(Number);
                    this.spawnPosition = {
                        x: chunkX * 16 + 8, // Centre du chunk
                        z: chunkZ * 16 + 8
                    };
                } else {
                    // Utiliser le premier chunk disponible
                    const [chunkX, chunkZ] = availableChunks[0].split(',').map(Number);
                    this.spawnPosition = {
                        x: chunkX * 16 + 8,
                        z: chunkZ * 16 + 8
                    };
                }
            } else {
                // Fallback a l'origine
                this.spawnPosition = { x: 0, z: 0 };
            }
        }

        const spawnX = this.spawnPosition.x;
        const spawnZ = this.spawnPosition.z;
        
        this.logger.physics('Recherche position de spawn', {
            targetX: spawnX,
            targetZ: spawnZ,
            availableChunks: world.chunks.size
        });

        // Chercher le sol a cette position
        const groundHeight = this.getGroundHeightWithFallback(world, spawnX, spawnZ);
        
        if (groundHeight !== null && groundHeight !== undefined) {
            const spawnY = groundHeight + 1 + this.eyeHeight;
            this.camera.position.set(spawnX, spawnY, spawnZ);
            this.lastStablePosition = { x: spawnX, y: spawnY, z: spawnZ };
            
            this.logger.physics('Position de spawn trouvee', {
                x: spawnX,
                y: spawnY,
                z: spawnZ,
                groundHeight: groundHeight
            });
            return true;
        }
        
        this.logger.warn('Impossible de trouver le sol pour le spawn', {
            spawnX: spawnX,
            spawnZ: spawnZ,
            groundHeight: groundHeight
        });
        return false;
    }

    // Methodes d'interface utilisateur et autres fonctionnalites...
    createUI() {
        // Creation de l'interface utilisateur (hotbar, inventaire, etc.)
        // Cette methode reste inchangee
    }

    // Methodes de minage et autres fonctionnalites...
    startMining(world = null) {
        // Implementation du minage
        // Cette methode reste inchangee
    }

    stopMining() {
        // Arret du minage
        // Cette methode reste inchangee
    }

    updateMining(delta) {
        // Mise a jour du minage
        // Cette methode reste inchangee
    }

    // Autres methodes utilitaires...
    getBlockName(blockType) {
        const blockNames = {
            1: 'Pierre',
            2: 'Terre',
            3: 'Herbe',
            4: 'Sable',
            6: 'Bois',
            7: 'Feuilles',
            8: 'Neige',
            9: 'Glace',
            10: 'Argile',
            11: 'Gravier',
            12: 'Pierre Taillee',
            13: 'Bedrock',
            14: 'Minerai de Charbon',
            15: 'Minerai de Fer',
            16: 'Minerai d\'Or',
            17: 'Minerai de Diamant',
            18: 'Bois de Chene',
            19: 'Bois de Bouleau',
            20: 'Bois de Pin',
            21: 'Feuilles de Chene',
            22: 'Feuilles de Bouleau',
            23: 'Feuilles de Pin',
            24: 'Cactus',
            25: 'Herbe Haute',
            26: 'Fleurs',
            27: 'Champignon',
            28: 'Lave'
        };
        return blockNames[blockType] || `Bloc ${blockType}`;
    }
}