// client/js/world/ClientChunk.js
// Représentation d'un chunk côté client - AFFICHAGE SEULEMENT
import { BLOCK_TYPES, GAME_CONFIG } from '../../../shared/constants.js';

export class ClientChunk {
    constructor(x, z) {
        this.x = x;
        this.z = z;
        this.blocks = new Array(GAME_CONFIG.CHUNK_WIDTH * GAME_CONFIG.CHUNK_HEIGHT * GAME_CONFIG.CHUNK_DEPTH).fill(0);
        
        // Mesh et rendu
        this.mesh = null;
        this.instancedMeshes = new Map(); // Map de blockType -> InstancedMesh
        this.visible = false;
        this.needsRebuild = false;
        
        // Géométries et matériaux partagés
        if (!ClientChunk.sharedGeometry) {
            ClientChunk.initializeSharedResources();
        }
        
        console.log(`📦 ClientChunk créé: (${x}, ${z})`);
    }
    
    // Ressources partagées entre tous les chunks
    static sharedGeometry = null;
    static sharedMaterials = new Map();
    
    static initializeSharedResources() {
        // Géométrie de base pour tous les blocs
        ClientChunk.sharedGeometry = new THREE.BoxGeometry(1, 1, 1);
        
        // Matériaux pour chaque type de bloc
        const blockMaterials = {
            [BLOCK_TYPES.STONE]: { color: 0x808080, name: 'Stone' },
            [BLOCK_TYPES.DIRT]: { color: 0x8B4513, name: 'Dirt' },
            [BLOCK_TYPES.GRASS]: { color: 0x228B22, name: 'Grass' },
            [BLOCK_TYPES.WOOD]: { color: 0x8B4513, name: 'Wood' },
            [BLOCK_TYPES.LEAVES]: { color: 0x228B22, name: 'Leaves' },
            [BLOCK_TYPES.SAND]: { color: 0xF4A460, name: 'Sand' },
            [BLOCK_TYPES.WATER]: { color: 0x4169E1, name: 'Water' },
            [BLOCK_TYPES.COAL_ORE]: { color: 0x2F2F2F, name: 'Coal Ore' },
            [BLOCK_TYPES.IRON_ORE]: { color: 0xCD853F, name: 'Iron Ore' },
            [BLOCK_TYPES.GOLD_ORE]: { color: 0xFFD700, name: 'Gold Ore' },
            [BLOCK_TYPES.DIAMOND_ORE]: { color: 0x00FFFF, name: 'Diamond Ore' },
            [BLOCK_TYPES.BEDROCK]: { color: 0x2F2F2F, name: 'Bedrock' },
            [BLOCK_TYPES.TALL_GRASS]: { color: 0x90EE90, name: 'Tall Grass' },
            [BLOCK_TYPES.FLOWERS]: { color: 0xFF69B4, name: 'Flowers' },
            [BLOCK_TYPES.MUSHROOM]: { color: 0x8B4513, name: 'Mushroom' },
            [BLOCK_TYPES.CACTUS]: { color: 0x228B22, name: 'Cactus' },
            [BLOCK_TYPES.SNOW]: { color: 0xFFFFFF, name: 'Snow' },
            [BLOCK_TYPES.ICE]: { color: 0xB0E0E6, name: 'Ice' },
            [BLOCK_TYPES.CLAY]: { color: 0xA0522D, name: 'Clay' },
            [BLOCK_TYPES.GRAVEL]: { color: 0x696969, name: 'Gravel' }
        };
        
        Object.entries(blockMaterials).forEach(([blockType, config]) => {
            const material = new THREE.MeshLambertMaterial({
                color: config.color,
                transparent: blockType == BLOCK_TYPES.WATER || blockType == BLOCK_TYPES.LEAVES,
                opacity: blockType == BLOCK_TYPES.WATER ? 0.7 : 1.0
            });
            
            ClientChunk.sharedMaterials.set(parseInt(blockType), material);
        });
        
        console.log('🎨 Ressources partagées initialisées');
    }
    
    setBlockData(blockData) {
        if (blockData && blockData.length === this.blocks.length) {
            this.blocks = [...blockData];
            this.needsRebuild = true;
        } else {
            console.error('❌ Données de bloc invalides pour le chunk', this.x, this.z);
        }
    }
    
    getBlock(x, y, z) {
        if (x < 0 || x >= GAME_CONFIG.CHUNK_WIDTH ||
            y < 0 || y >= GAME_CONFIG.CHUNK_HEIGHT ||
            z < 0 || z >= GAME_CONFIG.CHUNK_DEPTH) {
            return 0; // Air en dehors des limites
        }
        
        const index = this.getIndex(x, y, z);
        return this.blocks[index];
    }
    
    setBlock(x, y, z, blockType) {
        if (x < 0 || x >= GAME_CONFIG.CHUNK_WIDTH ||
            y < 0 || y >= GAME_CONFIG.CHUNK_HEIGHT ||
            z < 0 || z >= GAME_CONFIG.CHUNK_DEPTH) {
            return false;
        }
        
        const index = this.getIndex(x, y, z);
        this.blocks[index] = blockType;
        this.needsRebuild = true;
        return true;
    }
    
    getIndex(x, y, z) {
        return y * GAME_CONFIG.CHUNK_WIDTH * GAME_CONFIG.CHUNK_DEPTH + 
               z * GAME_CONFIG.CHUNK_WIDTH + x;
    }
    
    buildMesh() {
        if (this.mesh) {
            this.disposeMesh();
        }
        
        // Créer un groupe pour contenir tous les meshes instanciés
        this.mesh = new THREE.Group();
        this.instancedMeshes.clear();
        
        // Compter les blocs visibles par type
        const blockCounts = new Map();
        const visibleBlocks = new Map(); // Map de blockType -> Array de positions
        
        for (let x = 0; x < GAME_CONFIG.CHUNK_WIDTH; x++) {
            for (let y = 0; y < GAME_CONFIG.CHUNK_HEIGHT; y++) {
                for (let z = 0; z < GAME_CONFIG.CHUNK_DEPTH; z++) {
                    const blockType = this.getBlock(x, y, z);
                    
                    if (blockType !== BLOCK_TYPES.AIR && this.isBlockVisible(x, y, z)) {
                        if (!visibleBlocks.has(blockType)) {
                            visibleBlocks.set(blockType, []);
                        }
                        visibleBlocks.get(blockType).push({ x, y, z });
                        blockCounts.set(blockType, (blockCounts.get(blockType) || 0) + 1);
                    }
                }
            }
        }
        
        // Créer les InstancedMesh pour chaque type de bloc
        visibleBlocks.forEach((positions, blockType) => {
            const material = ClientChunk.sharedMaterials.get(blockType);
            if (!material) {
                console.warn(`⚠️ Matériau manquant pour le bloc type ${blockType}`);
                return;
            }
            
            const count = positions.length;
            const instancedMesh = new THREE.InstancedMesh(
                ClientChunk.sharedGeometry,
                material,
                count
            );
            
            // Configurer les instances
            const matrix = new THREE.Matrix4();
            positions.forEach((pos, index) => {
                matrix.setPosition(
                    this.x * GAME_CONFIG.CHUNK_WIDTH + pos.x + 0.5,
                    pos.y + 0.5,
                    this.z * GAME_CONFIG.CHUNK_DEPTH + pos.z + 0.5
                );
                instancedMesh.setMatrixAt(index, matrix);
            });
            
            instancedMesh.instanceMatrix.needsUpdate = true;
            instancedMesh.castShadow = true;
            instancedMesh.receiveShadow = true;
            
            this.mesh.add(instancedMesh);
            this.instancedMeshes.set(blockType, instancedMesh);
        });
        
        this.needsRebuild = false;
        
        console.log(`🧱 Mesh construit pour chunk (${this.x}, ${this.z}): ${blockCounts.size} types de blocs, ${Array.from(blockCounts.values()).reduce((a, b) => a + b, 0)} blocs visibles`);
        
        return this.mesh;
    }
    
    rebuildMesh() {
        if (this.mesh) {
            // Supprimer l'ancien mesh de la scène si nécessaire
            if (this.mesh.parent) {
                this.mesh.parent.remove(this.mesh);
            }
            
            // Reconstruire
            this.buildMesh();
            
            // Remettre dans la scène si était visible
            if (this.visible && this.mesh && window.game?.clientWorld?.scene) {
                window.game.clientWorld.scene.add(this.mesh);
            }
        }
    }
    
    isBlockVisible(x, y, z) {
        const blockType = this.getBlock(x, y, z);
        if (blockType === BLOCK_TYPES.AIR) return false;
        
        // Vérifier si au moins une face est exposée
        const neighbors = [
            { dx: 1, dy: 0, dz: 0 },  // Est
            { dx: -1, dy: 0, dz: 0 }, // Ouest
            { dx: 0, dy: 1, dz: 0 },  // Haut
            { dx: 0, dy: -1, dz: 0 }, // Bas
            { dx: 0, dy: 0, dz: 1 },  // Sud
            { dx: 0, dy: 0, dz: -1 }  // Nord
        ];
        
        for (const neighbor of neighbors) {
            const nx = x + neighbor.dx;
            const ny = y + neighbor.dy;
            const nz = z + neighbor.dz;
            
            // Si le voisin est en dehors du chunk, considérer comme air
            if (nx < 0 || nx >= GAME_CONFIG.CHUNK_WIDTH ||
                ny < 0 || ny >= GAME_CONFIG.CHUNK_HEIGHT ||
                nz < 0 || nz >= GAME_CONFIG.CHUNK_DEPTH) {
                return true;
            }
            
            const neighborType = this.getBlock(nx, ny, nz);
            if (neighborType === BLOCK_TYPES.AIR || 
                neighborType === BLOCK_TYPES.WATER ||
                neighborType === BLOCK_TYPES.TALL_GRASS ||
                neighborType === BLOCK_TYPES.FLOWERS) {
                return true;
            }
        }
        
        return false;
    }
    
    // Gestion de la visibilité
    setVisible(visible) {
        this.visible = visible;
        if (this.mesh) {
            this.mesh.visible = visible;
        }
    }
    
    isVisible() {
        return this.visible;
    }
    
    getMesh() {
        return this.mesh;
    }
    
    // Optimisations
    updateLOD(distance) {
        // Niveau de détail basé sur la distance
        if (!this.mesh) return;
        
        if (distance > 10) {
            // Distance éloignée - réduire la qualité
            this.instancedMeshes.forEach(mesh => {
                mesh.frustumCulled = true;
            });
        } else {
            // Distance proche - qualité maximale
            this.instancedMeshes.forEach(mesh => {
                mesh.frustumCulled = false;
            });
        }
    }
    
    // Informations de debug
    getDebugInfo() {
        const blockCounts = new Map();
        
        for (let i = 0; i < this.blocks.length; i++) {
            const blockType = this.blocks[i];
            blockCounts.set(blockType, (blockCounts.get(blockType) || 0) + 1);
        }
        
        return {
            position: { x: this.x, z: this.z },
            visible: this.visible,
            needsRebuild: this.needsRebuild,
            blockCounts: Object.fromEntries(blockCounts),
            meshes: this.instancedMeshes.size,
            totalBlocks: this.blocks.length
        };
    }
    
    // Nettoyage
    disposeMesh() {
        if (this.mesh) {
            // Supprimer de la scène si présent
            if (this.mesh.parent) {
                this.mesh.parent.remove(this.mesh);
            }
            
            // Nettoyer les meshes instanciés
            this.instancedMeshes.forEach(mesh => {
                mesh.dispose();
            });
            this.instancedMeshes.clear();
            
            this.mesh = null;
        }
    }
    
    dispose() {
        console.log(`🗑️ Nettoyage chunk (${this.x}, ${this.z})`);
        
        this.disposeMesh();
        this.blocks = null;
    }
}
