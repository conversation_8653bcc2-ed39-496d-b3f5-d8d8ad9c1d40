// PreloadManager.js - Gestionnaire de préchargement intelligent
export class PreloadManager {
    constructor(world) {
        this.world = world;
        this.preloadDistance = 6; // Distance de préchargement
        this.preloadedChunks = new Set();
    }

    update(playerX, playerZ) {
        const chunkX = Math.floor(playerX / 16);
        const chunkZ = Math.floor(playerZ / 16);
        
        // Précharger les chunks dans un rayon plus large
        for (let x = -this.preloadDistance; x <= this.preloadDistance; x++) {
            for (let z = -this.preloadDistance; z <= this.preloadDistance; z++) {
                const targetX = chunkX + x;
                const targetZ = chunkZ + z;
                const key = `${targetX},${targetZ}`;
                
                if (!this.preloadedChunks.has(key)) {
                    this.preloadedChunks.add(key);
                    this.world.workerManager.generateChunk(targetX, targetZ).catch(err => {
                        console.error('Erreur de préchargement:', err);
                    });
                }
            }
        }
    }

    // Nettoyer les chunks trop éloignés
    cleanup(playerX, playerZ) {
        const chunkX = Math.floor(playerX / 16);
        const chunkZ = Math.floor(playerZ / 16);
        const cleanupDistance = this.preloadDistance + 2;
        
        for (const key of this.preloadedChunks) {
            const [x, z] = key.split(',').map(Number);
            const distance = Math.max(Math.abs(x - chunkX), Math.abs(z - chunkZ));
            
            if (distance > cleanupDistance) {
                this.preloadedChunks.delete(key);
            }
        }
    }
} 