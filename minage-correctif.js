/**
 * CORRECTIF POUR LE MINAGE
 * 
 * Ce fichier contient les correctifs pour améliorer les fonctions
 * liées au minage dans le jeu JScraft.
 * 
 * Instructions d'utilisation:
 * 1. Ouvrir la console du jeu (F12)
 * 2. <PERSON><PERSON><PERSON> et coller le contenu de ce script
 * 3. Appuyer sur Entrée pour appliquer les correctifs
 * 4. Tester le minage
 */

(function() {
    console.log('🔧 Application du correctif de minage...');
    
    // Vérifier que les objets nécessaires existent
    if (!window.player) {
        console.error('❌ Player non disponible');
        return;
    }
    
    // Sauvegarde des méthodes originales pour le debug
    window.originalMiningMethods = {
        getTargetBlock: window.player.getTargetBlock,
        updateMining: window.player.updateMining,
        completeMining: window.player.completeMining
    };
    
    console.log('📊 Sauvegarde des méthodes originales effectuée');
    
    /**
     * VERSION AMÉLIORÉE: getTargetBlock
     * Utilise à la fois le raycast et une méthode manuelle pour trouver le bloc ciblé
     */
    window.player.getTargetBlock = function(world) {
        this.logger.mining('🎯 getTargetBlock appelé', {
            worldChunks: world.chunks ? world.chunks.size : 0,
            cameraPosition: {
                x: this.camera.position.x.toFixed(2),
                y: this.camera.position.y.toFixed(2),
                z: this.camera.position.z.toFixed(2)
            }
        });

        // Direction du regard - nécessaire pour les deux méthodes
        const direction = this.camera.getWorldDirection(new THREE.Vector3());
        this.logger.mining('🎯 Direction du regard', {
            origin: {
                x: this.camera.position.x.toFixed(2),
                y: this.camera.position.y.toFixed(2),
                z: this.camera.position.z.toFixed(2)
            },
            direction: {
                x: direction.x.toFixed(3),
                y: direction.y.toFixed(3),
                z: direction.z.toFixed(3)
            }
        });

        // === MÉTHODE 1: Raycast Three.js (plus précis mais peut échouer) ===
        let targetFromRaycast = null;
        try {
            // Récupération des meshes à tester
            const allMeshes = Array.from(world.chunks.values())
                .filter(chunkData => chunkData && chunkData.mesh && chunkData.mesh.visible)
                .flatMap(chunkData => chunkData.mesh.children || []);
            this.logger.mining('📊 Meshes trouvés pour raycast', { count: allMeshes.length });

            // Si des meshes sont disponibles, tenter le raycast
            if (allMeshes.length > 0) {
                // Configurer le raycaster
                this.raycaster = this.raycaster || new THREE.Raycaster();
                this.raycaster.set(this.camera.position, direction);
                const intersects = this.raycaster.intersectObjects(allMeshes);
                
                this.logger.mining('📊 Résultat du raycast', {
                    intersectsCount: intersects.length,
                    hasIntersections: intersects.length > 0
                });
                
                // Traiter l'intersection si elle existe
                if (intersects.length > 0) {
                    const intersection = intersects[0];
                    const point = intersection.point;
                    
                    // Calculer la position du bloc (tenir compte de la normale pour éviter les erreurs de précision)
                    // On décale légèrement le point dans la direction opposée à la normale
                    const epsilon = 0.001; // Petit décalage pour éviter les erreurs de précision
                    const adjustedPoint = new THREE.Vector3(
                        point.x - intersection.face.normal.x * epsilon,
                        point.y - intersection.face.normal.y * epsilon,
                        point.z - intersection.face.normal.z * epsilon
                    );
                    
                    const blockX = Math.floor(adjustedPoint.x);
                    const blockY = Math.floor(adjustedPoint.y);
                    const blockZ = Math.floor(adjustedPoint.z);
                    
                    // Vérifier le type du bloc
                    const blockType = world.getBlockTypeAt(blockX, blockY, blockZ);
                    if (blockType && blockType !== 0) {
                        targetFromRaycast = {
                            x: blockX,
                            y: blockY,
                            z: blockZ,
                            blockType: blockType,
                            distance: intersection.distance,
                            method: 'raycast'
                        };
                        
                        this.logger.mining('✅ Bloc trouvé par raycast', targetFromRaycast);
                    }
                }
            }
        } catch (error) {
            this.logger.error('❌ Erreur lors du raycast', { 
                error: error.message,
                stack: error.stack
            });
            // Continuer avec la méthode manuelle en cas d'erreur
        }
        
        // === MÉTHODE 2: Recherche manuelle (plus robuste mais moins précise) ===
        // Toujours exécuter la méthode manuelle comme fallback ou pour vérification
        let targetFromManual = null;
        try {
            this.logger.mining('🔍 Recherche manuelle de bloc...');
            
            // Pas plus précis pour capturer des blocs plus petits
            for (let distance = 0.5; distance <= this.reach; distance += 0.25) {
                const testPos = this.camera.position.clone().add(direction.clone().multiplyScalar(distance));
                const blockX = Math.floor(testPos.x);
                const blockY = Math.floor(testPos.y);
                const blockZ = Math.floor(testPos.z);
                
                // Vérifier si il y a un bloc à cette position
                const blockType = world?.getBlockTypeAt?.(blockX, blockY, blockZ);
                if (blockType && blockType !== 0) { // 0 = air
                    targetFromManual = {
                        x: blockX,
                        y: blockY,
                        z: blockZ,
                        blockType: blockType,
                        distance: distance,
                        method: 'manual'
                    };
                    
                    this.logger.mining('✅ Bloc trouvé manuellement', targetFromManual);
                    break;
                }
            }
        } catch (error) {
            this.logger.error('❌ Erreur lors de la recherche manuelle', { 
                error: error.message,
                stack: error.stack
            });
        }
        
        // === DÉCISION FINALE ===
        // Préférer le résultat du raycast s'il existe, sinon utiliser la méthode manuelle
        const finalTarget = targetFromRaycast || targetFromManual;
        
        if (finalTarget) {
            this.logger.mining('🎯 Cible finale trouvée', {
                method: finalTarget.method,
                position: { x: finalTarget.x, y: finalTarget.y, z: finalTarget.z },
                blockType: finalTarget.blockType,
                distance: finalTarget.distance
            });
            return finalTarget;
        } else {
            this.logger.mining('❌ Aucun bloc cible trouvé');
            return null;
        }
    };
    
    /**
     * VERSION AMÉLIORÉE: updateMining
     * Ajoute des vérifications supplémentaires et une meilleure gestion des erreurs
     */
    window.player.updateMining = function(delta) {
        // Sortir rapidement si on n'est pas en train de miner
        if (!this.isMining) return;
        
        // Vérifier que la cible de minage existe toujours
        if (!this.miningTarget) {
            this.logger.error('⚠️ Cible de minage perdue, annulation');
            this.stopMining();
            return;
        }

        try {
            // Vérifier si le bloc existe toujours dans le monde
            if (window.world) {
                const { x, y, z } = this.miningTarget;
                const currentBlockType = window.world.getBlockTypeAt(x, y, z);
                
                // Si le bloc a changé (déjà miné ou remplacé), arrêter le minage
                if (currentBlockType === 0 || currentBlockType !== this.miningTarget.blockType) {
                    this.logger.mining('⚠️ Le bloc ciblé a changé ou disparu', {
                        expected: this.miningTarget.blockType,
                        current: currentBlockType
                    });
                    this.stopMining();
                    return;
                }
            }
            
            // Calcul de la progression
            const now = performance.now();
            const elapsed = now - this.miningStartTime;
            const previousProgress = this.miningProgress;
            this.miningProgress = Math.min(elapsed / this.miningDuration, 1.0);
            
            // Journaliser la progression tous les 25%
            if (Math.floor(previousProgress * 4) !== Math.floor(this.miningProgress * 4)) {
                this.logger.mining(`🔄 Progression du minage: ${Math.round(this.miningProgress * 100)}%`);
            }
            
            // Mettre à jour la barre de progression
            const progressBar = document.getElementById('mining-progress-bar');
            if (progressBar) {
                progressBar.style.width = `${this.miningProgress * 100}%`;
            }
            
            // Vérifier si le minage est terminé
            if (this.miningProgress >= 1.0) {
                this.logger.mining('✅ Minage terminé, destruction du bloc...');
                this.completeMining();
            }
            
            // Mettre à jour l'animation de bras
            if (this.isSwinging) {
                this.armSwingTime += delta * 1000;
                if (this.armSwingTime >= this.armSwingDuration) {
                    this.armSwingTime = 0;
                }
            }
        } catch (error) {
            this.logger.error('❌ Erreur dans updateMining', {
                error: error.message,
                stack: error.stack
            });
            this.stopMining(); // Arrêter le minage en cas d'erreur
        }
    };
    
    /**
     * VERSION AMÉLIORÉE: completeMining
     * Ajoute des vérifications supplémentaires et une meilleure gestion des erreurs
     */
    window.player.completeMining = function() {
        try {
            if (!this.miningTarget) {
                this.logger.warn('⚠️ Tentative de compléter un minage sans cible');
                this.stopMining();
                return;
            }
            
            const { x, y, z, blockType } = this.miningTarget;
            this.logger.mining('🧱 Tentative de destruction du bloc', { 
                position: { x, y, z }, 
                type: blockType 
            });
            
            // Vérifier si le bloc existe toujours avant de le détruire
            if (window.world && window.world.getBlockTypeAt) {
                const currentType = window.world.getBlockTypeAt(x, y, z);
                if (currentType === 0 || currentType !== blockType) {
                    this.logger.warn('⚠️ Le bloc a déjà été détruit ou remplacé', { 
                        expected: blockType, 
                        actual: currentType 
                    });
                    this.stopMining();
                    return;
                }
            }
            
            // Détruire le bloc dans le monde
            if (window.world && window.world.setBlockAt) {
                const success = window.world.setBlockAt(x, y, z, 0); // 0 = air
                if (!success) {
                    this.logger.error('❌ Échec de la destruction du bloc', { x, y, z });
                    this.stopMining();
                    return;
                }
                
                // Forcer la mise à jour du mesh du chunk concerné
                try {
                    const chunkX = Math.floor(x / 16);
                    const chunkZ = Math.floor(z / 16);
                    const chunkKey = `${chunkX},${chunkZ}`;
                    const chunkData = window.world.chunks.get(chunkKey);
                    
                    if (chunkData && chunkData.chunk) {
                        this.logger.mining('🔄 Régénération du mesh du chunk', { chunkKey });
                        if (typeof chunkData.chunk.generateMesh === 'function') {
                            chunkData.chunk.generateMesh();
                        }
                    }
                } catch (error) {
                    this.logger.error('❌ Erreur lors de la mise à jour du mesh', { 
                        error: error.message 
                    });
                }
                
                // Ajouter le bloc à l'inventaire
                this.addToInventory(blockType, 1);
                this.logger.mining('✅ Bloc miné avec succès', { 
                    blockType, 
                    position: { x, y, z } 
                });
            } else {
                this.logger.error('❌ Impossible de détruire le bloc (world.setBlockAt non disponible)');
            }
        } catch (error) {
            this.logger.error('❌ Erreur lors de la complétion du minage', { 
                error: error.message,
                stack: error.stack
            });
        } finally {
            // Arrêter le minage dans tous les cas
            this.stopMining();
        }
    };
    
    // Fonction d'aide pour tester le minage
    window.testMinageAvecCorrectif = function() {
        console.log('🧪 Test du minage avec correctif...');
        
        if (!window.player || !window.world) {
            console.error('❌ Player ou World non disponible');
            return;
        }
        
        console.log('👤 État actuel:', {
            isMining: window.player.isMining,
            inventoryOpen: window.player.inventoryOpen,
            position: {
                x: window.player.camera.position.x.toFixed(2),
                y: window.player.camera.position.y.toFixed(2),
                z: window.player.camera.position.z.toFixed(2)
            }
        });
        
        // Forcer la visibilité des chunks autour du joueur
        const playerX = Math.floor(window.player.camera.position.x / 16);
        const playerZ = Math.floor(window.player.camera.position.z / 16);
        let chunksVisible = 0;
        
        console.log('🌍 Vérification des chunks autour du joueur...');
        for (let dx = -2; dx <= 2; dx++) {
            for (let dz = -2; dz <= 2; dz++) {
                const chunkX = playerX + dx;
                const chunkZ = playerZ + dz;
                const chunkKey = `${chunkX},${chunkZ}`;
                
                if (window.world.chunks.has(chunkKey)) {
                    const chunkData = window.world.chunks.get(chunkKey);
                    if (chunkData && chunkData.chunk) {
                        if (!chunkData.mesh || !chunkData.mesh.visible) {
                            if (typeof chunkData.chunk.generateMesh === 'function') {
                                console.log(`🔄 Régénération du mesh pour chunk ${chunkKey}...`);
                                chunkData.chunk.generateMesh();
                            }
                        }
                        
                        if (chunkData.mesh) {
                            chunkData.mesh.visible = true;
                            chunksVisible++;
                        }
                    }
                }
            }
        }
        
        console.log(`✅ ${chunksVisible} chunks visibles autour du joueur`);
        
        // Démarrer le minage
        console.log('⛏️ Démarrage du minage...');
        window.player.startMining(window.world);
    };
    
    console.log('✅ Correctif de minage appliqué avec succès!');
    console.log('💡 Utilisez window.testMinageAvecCorrectif() pour tester');
})();
