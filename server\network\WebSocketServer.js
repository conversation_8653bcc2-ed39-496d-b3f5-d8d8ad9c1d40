// server/network/WebSocketServer.js
import { WebSocketServer as WSServer } from 'ws';
import { MESSAGE_TYPES, Utils } from '../../shared/constants.js';

export class WebSocketServer {
    constructor(httpServer, gameManager) {
        this.gameManager = gameManager;
        this.clients = new Map(); // Map de playerId -> { ws, player }
        
        // Créer le serveur WebSocket
        this.wss = new WSServer({ 
            server: httpServer,
            path: '/ws'
        });
        
        this.setupEventHandlers();
        console.log('🌐 Serveur WebSocket initialisé');
    }
    
    setupEventHandlers() {
        this.wss.on('connection', (ws, request) => {
            const playerId = Utils.generateId();
            console.log(`👤 Nouveau joueur connecté: ${playerId}`);
            
            // Stocker la connexion
            this.clients.set(playerId, { ws, playerId });
            
            // Configurer les événements pour ce client
            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleMessage(playerId, message);
                } catch (error) {
                    console.error(`❌ Erreur parsing message de ${playerId}:`, error);
                    this.sendToClient(playerId, {
                        type: MESSAGE_TYPES.SERVER_ERROR,
                        payload: { error: 'Message invalide' }
                    });
                }
            });
            
            ws.on('close', () => {
                console.log(`👋 Joueur déconnecté: ${playerId}`);
                this.handlePlayerDisconnect(playerId);
            });
            
            ws.on('error', (error) => {
                console.error(`❌ Erreur WebSocket pour ${playerId}:`, error);
                this.handlePlayerDisconnect(playerId);
            });
            
            // Notifier le GameManager de la nouvelle connexion
            this.gameManager.handlePlayerJoin(playerId, this);
            
            // Envoyer l'initialisation au client
            this.sendToClient(playerId, {
                type: MESSAGE_TYPES.SERVER_INIT,
                payload: { 
                    playerId,
                    worldSeed: this.gameManager.worldManager.seed,
                    serverTime: Date.now()
                }
            });
        });
    }
    
    handleMessage(playerId, message) {
        const { type, payload } = message;
        
        switch (type) {
            case MESSAGE_TYPES.CLIENT_INPUT:
                this.gameManager.handlePlayerInput(playerId, payload);
                break;
                
            case MESSAGE_TYPES.CLIENT_MINE_START:
                this.gameManager.handleMineStart(playerId, payload);
                break;
                
            case MESSAGE_TYPES.CLIENT_MINE_STOP:
                this.gameManager.handleMineStop(playerId, payload);
                break;
                
            case MESSAGE_TYPES.CLIENT_PLACE_BLOCK:
                this.gameManager.handlePlaceBlock(playerId, payload);
                break;
                
            case MESSAGE_TYPES.CLIENT_CHAT:
                this.gameManager.handleChat(playerId, payload);
                break;
                
            case MESSAGE_TYPES.CLIENT_DISCONNECT:
                this.handlePlayerDisconnect(playerId);
                break;
                
            default:
                console.warn(`⚠️ Type de message inconnu de ${playerId}: ${type}`);
        }
    }
    
    handlePlayerDisconnect(playerId) {
        if (this.clients.has(playerId)) {
            this.clients.delete(playerId);
            this.gameManager.handlePlayerLeave(playerId);
        }
    }
    
    // Envoyer un message à un client spécifique
    sendToClient(playerId, message) {
        const client = this.clients.get(playerId);
        if (client && client.ws.readyState === 1) { // WebSocket.OPEN
            try {
                client.ws.send(JSON.stringify(message));
            } catch (error) {
                console.error(`❌ Erreur envoi message à ${playerId}:`, error);
                this.handlePlayerDisconnect(playerId);
            }
        }
    }
    
    // Diffuser un message à tous les clients
    broadcast(message, excludePlayerId = null) {
        const messageStr = JSON.stringify(message);
        let sentCount = 0;
        
        this.clients.forEach((client, playerId) => {
            if (playerId !== excludePlayerId && client.ws.readyState === 1) {
                try {
                    client.ws.send(messageStr);
                    sentCount++;
                } catch (error) {
                    console.error(`❌ Erreur broadcast à ${playerId}:`, error);
                    this.handlePlayerDisconnect(playerId);
                }
            }
        });
        
        return sentCount;
    }
    
    // Diffuser à tous les clients dans une zone
    broadcastToArea(message, centerX, centerZ, radius) {
        const messageStr = JSON.stringify(message);
        let sentCount = 0;
        
        this.clients.forEach((client, playerId) => {
            if (client.ws.readyState === 1) {
                const player = this.gameManager.getPlayer(playerId);
                if (player) {
                    const distance = Utils.distance2D(
                        player.position.x, player.position.z,
                        centerX, centerZ
                    );
                    
                    if (distance <= radius) {
                        try {
                            client.ws.send(messageStr);
                            sentCount++;
                        } catch (error) {
                            console.error(`❌ Erreur broadcast area à ${playerId}:`, error);
                            this.handlePlayerDisconnect(playerId);
                        }
                    }
                }
            }
        });
        
        return sentCount;
    }
    
    // Obtenir le nombre de clients connectés
    getClientCount() {
        return this.clients.size;
    }
    
    // Fermer le serveur WebSocket
    close() {
        console.log('🔌 Fermeture du serveur WebSocket...');
        this.clients.forEach((client, playerId) => {
            client.ws.close();
        });
        this.clients.clear();
        this.wss.close();
    }
}
