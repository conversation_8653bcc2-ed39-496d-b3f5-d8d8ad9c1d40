# Scripts d'arrêt des serveurs

Ce dossier contient deux scripts pour arrêter tous les processus Node.js et Python en cours d'exécution sur Windows.

## 📁 Fichiers disponibles

### 1. `kill-servers.ps1` (PowerShell - Recommandé)
**Script PowerShell avancé avec fonctionnalités étendues**

**Fonctionnalités :**
- ✅ Arrêt sécurisé des processus Node.js
- ✅ Arrêt sécurisé des processus Python
- ✅ Détection spécifique des serveurs HTTP Python
- ✅ Libération des ports couramment utilisés (3000, 8000, 8001, 8002, 8080, 5000, 4200, 9000)
- ✅ Gestion d'erreurs avancée
- ✅ Affichage coloré et informatif
- ✅ Vérification finale des processus restants

**Utilisation :**
```powershell
# Méthode 1: Double-clic sur le fichier
# Méthode 2: Depuis PowerShell
.\kill-servers.ps1

# Si vous avez des restrictions d'exécution:
PowerShell -ExecutionPolicy Bypass -File .\kill-servers.ps1
```

### 2. `kill-servers.bat` (Batch)
**Script batch simple et rapide**

**Fonctionnalités :**
- ✅ Arrêt des processus Node.js
- ✅ Arrêt des processus Python et Python3
- ✅ Interface simple
- ✅ Compatible avec tous les systèmes Windows

**Utilisation :**
```cmd
# Double-clic sur le fichier ou depuis l'invite de commande
kill-servers.bat
```

## 🚀 Utilisation recommandée

### Avant de démarrer vos serveurs de développement :
1. Exécutez l'un des scripts pour nettoyer les processus existants
2. Démarrez vos nouveaux serveurs
3. Évitez les conflits de ports et les processus zombies

### Cas d'usage typiques :
- **Développement web** : Avant de redémarrer votre serveur de développement
- **Changement de projet** : Pour nettoyer l'environnement entre projets
- **Résolution de problèmes** : Quand des ports restent occupés
- **Fin de session** : Pour nettoyer avant de fermer l'ordinateur

## ⚠️ Avertissements

- **Ces scripts arrêtent TOUS les processus Node.js et Python**
- Sauvegardez votre travail avant d'exécuter les scripts
- Les processus seront fermés de force (SIGKILL)
- Vérifiez qu'aucune application importante n'utilise Node.js ou Python

## 🔧 Dépannage

### Si le script PowerShell ne s'exécute pas :
```powershell
# Vérifier la politique d'exécution
Get-ExecutionPolicy

# Autoriser temporairement l'exécution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Ou exécuter avec bypass
PowerShell -ExecutionPolicy Bypass -File .\kill-servers.ps1
```

### Si des processus résistent :
1. Redémarrez en tant qu'administrateur
2. Utilisez le Gestionnaire des tâches Windows
3. Redémarrez l'ordinateur en dernier recours

## 📊 Ports surveillés par le script PowerShell

- **3000** : React, Express.js par défaut
- **8000** : Python http.server par défaut
- **8001, 8002** : Serveurs de développement alternatifs
- **8080** : Tomcat, serveurs web alternatifs
- **5000** : Flask par défaut
- **4200** : Angular CLI par défaut
- **9000** : Divers frameworks de développement

## 💡 Conseils

1. **Créez un raccourci** sur le bureau pour un accès rapide
2. **Ajoutez au PATH** pour utiliser depuis n'importe où
3. **Intégrez dans vos scripts** de démarrage de projet
4. **Utilisez avant les déploiements** pour éviter les conflits

---

*Créé pour faciliter le développement et éviter les conflits de processus*