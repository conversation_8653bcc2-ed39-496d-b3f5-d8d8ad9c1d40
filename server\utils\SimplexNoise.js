// server/utils/SimplexNoise.js
// Copie adaptée du SimplexNoise existant pour le serveur

export class SimplexNoise {
    constructor(seed = 0) {
        this.seed = seed;
        this.p = new Array(512);
        this.perm = new Array(512);
        this.permMod12 = new Array(512);
        
        // Initialiser avec la graine
        this.initializeWithSeed(seed);
        
        // Gradients pour le bruit 3D
        this.grad3 = [
            [1,1,0],[-1,1,0],[1,-1,0],[-1,-1,0],
            [1,0,1],[-1,0,1],[1,0,-1],[-1,0,-1],
            [0,1,1],[0,-1,1],[0,1,-1],[0,-1,-1]
        ];
        
        // Facteurs pour le bruit Simplex
        this.F2 = 0.5 * (Math.sqrt(3.0) - 1.0);
        this.G2 = (3.0 - Math.sqrt(3.0)) / 6.0;
        this.F3 = 1.0 / 3.0;
        this.G3 = 1.0 / 6.0;
    }
    
    initializeWithSeed(seed) {
        // Générateur de nombres pseudo-aléatoires basé sur la graine
        let random = this.seededRandom(seed);
        
        // Créer une permutation basée sur la graine
        const p = [];
        for (let i = 0; i < 256; i++) {
            p[i] = i;
        }
        
        // Mélanger avec la graine
        for (let i = 255; i > 0; i--) {
            const j = Math.floor(random() * (i + 1));
            [p[i], p[j]] = [p[j], p[i]];
        }
        
        // Dupliquer pour éviter les débordements
        for (let i = 0; i < 512; i++) {
            this.perm[i] = p[i & 255];
            this.permMod12[i] = this.perm[i] % 12;
        }
    }
    
    seededRandom(seed) {
        let m = 0x80000000; // 2^31
        let a = 1103515245;
        let c = 12345;
        let state = seed;
        
        return function() {
            state = (a * state + c) % m;
            return state / (m - 1);
        };
    }
    
    // Fonction de lissage
    fade(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    // Interpolation linéaire
    lerp(a, b, t) {
        return (1 - t) * a + t * b;
    }
    
    // Produit scalaire pour les gradients 2D
    grad2(hash, x, y) {
        const h = hash & 7;
        const u = h < 4 ? x : y;
        const v = h < 4 ? y : x;
        return ((h & 1) ? -u : u) + ((h & 2) ? -2.0 * v : 2.0 * v);
    }
    
    // Produit scalaire pour les gradients 3D
    grad3(hash, x, y, z) {
        const h = hash & 15;
        const u = h < 8 ? x : y;
        const v = h < 4 ? y : h === 12 || h === 14 ? x : z;
        return ((h & 1) ? -u : u) + ((h & 2) ? -v : v);
    }
    
    // Bruit 2D
    noise2D(xin, yin) {
        let n0, n1, n2;
        
        // Facteur de distorsion pour transformer la grille carrée en grille triangulaire
        const s = (xin + yin) * this.F2;
        const i = Math.floor(xin + s);
        const j = Math.floor(yin + s);
        const t = (i + j) * this.G2;
        const X0 = i - t;
        const Y0 = j - t;
        const x0 = xin - X0;
        const y0 = yin - Y0;
        
        // Déterminer quel simplex nous sommes dans
        let i1, j1;
        if (x0 > y0) {
            i1 = 1; j1 = 0;
        } else {
            i1 = 0; j1 = 1;
        }
        
        // Offsets pour les coins du simplex
        const x1 = x0 - i1 + this.G2;
        const y1 = y0 - j1 + this.G2;
        const x2 = x0 - 1.0 + 2.0 * this.G2;
        const y2 = y0 - 1.0 + 2.0 * this.G2;
        
        // Indices de permutation
        const ii = i & 255;
        const jj = j & 255;
        const gi0 = this.permMod12[ii + this.perm[jj]];
        const gi1 = this.permMod12[ii + i1 + this.perm[jj + j1]];
        const gi2 = this.permMod12[ii + 1 + this.perm[jj + 1]];
        
        // Calculer les contributions de chaque coin
        let t0 = 0.5 - x0 * x0 - y0 * y0;
        if (t0 < 0) {
            n0 = 0.0;
        } else {
            t0 *= t0;
            n0 = t0 * t0 * this.grad2(gi0, x0, y0);
        }
        
        let t1 = 0.5 - x1 * x1 - y1 * y1;
        if (t1 < 0) {
            n1 = 0.0;
        } else {
            t1 *= t1;
            n1 = t1 * t1 * this.grad2(gi1, x1, y1);
        }
        
        let t2 = 0.5 - x2 * x2 - y2 * y2;
        if (t2 < 0) {
            n2 = 0.0;
        } else {
            t2 *= t2;
            n2 = t2 * t2 * this.grad2(gi2, x2, y2);
        }
        
        // Additionner les contributions et normaliser
        return 70.0 * (n0 + n1 + n2);
    }
    
    // Bruit 3D
    noise3D(xin, yin, zin) {
        let n0, n1, n2, n3;
        
        // Facteur de distorsion
        const s = (xin + yin + zin) * this.F3;
        const i = Math.floor(xin + s);
        const j = Math.floor(yin + s);
        const k = Math.floor(zin + s);
        const t = (i + j + k) * this.G3;
        const X0 = i - t;
        const Y0 = j - t;
        const Z0 = k - t;
        const x0 = xin - X0;
        const y0 = yin - Y0;
        const z0 = zin - Z0;
        
        // Déterminer quel simplex nous sommes dans
        let i1, j1, k1;
        let i2, j2, k2;
        
        if (x0 >= y0) {
            if (y0 >= z0) {
                i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 1; k2 = 0;
            } else if (x0 >= z0) {
                i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 0; k2 = 1;
            } else {
                i1 = 0; j1 = 0; k1 = 1; i2 = 1; j2 = 0; k2 = 1;
            }
        } else {
            if (y0 < z0) {
                i1 = 0; j1 = 0; k1 = 1; i2 = 0; j2 = 1; k2 = 1;
            } else if (x0 < z0) {
                i1 = 0; j1 = 1; k1 = 0; i2 = 0; j2 = 1; k2 = 1;
            } else {
                i1 = 0; j1 = 1; k1 = 0; i2 = 1; j2 = 1; k2 = 0;
            }
        }
        
        // Offsets pour les coins du simplex
        const x1 = x0 - i1 + this.G3;
        const y1 = y0 - j1 + this.G3;
        const z1 = z0 - k1 + this.G3;
        const x2 = x0 - i2 + 2.0 * this.G3;
        const y2 = y0 - j2 + 2.0 * this.G3;
        const z2 = z0 - k2 + 2.0 * this.G3;
        const x3 = x0 - 1.0 + 3.0 * this.G3;
        const y3 = y0 - 1.0 + 3.0 * this.G3;
        const z3 = z0 - 1.0 + 3.0 * this.G3;
        
        // Indices de permutation
        const ii = i & 255;
        const jj = j & 255;
        const kk = k & 255;
        const gi0 = this.permMod12[ii + this.perm[jj + this.perm[kk]]];
        const gi1 = this.permMod12[ii + i1 + this.perm[jj + j1 + this.perm[kk + k1]]];
        const gi2 = this.permMod12[ii + i2 + this.perm[jj + j2 + this.perm[kk + k2]]];
        const gi3 = this.permMod12[ii + 1 + this.perm[jj + 1 + this.perm[kk + 1]]];
        
        // Calculer les contributions de chaque coin
        let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;
        if (t0 < 0) {
            n0 = 0.0;
        } else {
            t0 *= t0;
            n0 = t0 * t0 * this.grad3(gi0, x0, y0, z0);
        }
        
        let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;
        if (t1 < 0) {
            n1 = 0.0;
        } else {
            t1 *= t1;
            n1 = t1 * t1 * this.grad3(gi1, x1, y1, z1);
        }
        
        let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;
        if (t2 < 0) {
            n2 = 0.0;
        } else {
            t2 *= t2;
            n2 = t2 * t2 * this.grad3(gi2, x2, y2, z2);
        }
        
        let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;
        if (t3 < 0) {
            n3 = 0.0;
        } else {
            t3 *= t3;
            n3 = t3 * t3 * this.grad3(gi3, x3, y3, z3);
        }
        
        // Additionner les contributions et normaliser
        return 32.0 * (n0 + n1 + n2 + n3);
    }
    
    // Bruit fractal (octaves multiples)
    fractalNoise2D(x, y, octaves = 4, persistence = 0.5, scale = 1.0) {
        let value = 0;
        let amplitude = 1;
        let frequency = scale;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            value += this.noise2D(x * frequency, y * frequency) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= 2;
        }
        
        return value / maxValue;
    }
    
    fractalNoise3D(x, y, z, octaves = 4, persistence = 0.5, scale = 1.0) {
        let value = 0;
        let amplitude = 1;
        let frequency = scale;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            value += this.noise3D(x * frequency, y * frequency, z * frequency) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= 2;
        }
        
        return value / maxValue;
    }
}
