// Générateur de textures procédurales pour les blocs Minecraft
export class TextureGenerator {
    constructor() {
        this.logger = window.GameLogger;
        this.textureCache = new Map();
        this.textureSize = 16; // Taille standard Minecraft
        
        // Définition des types de blocs avec leurs propriétés
        this.blockTypes = {
            // Terrain
            GRASS: { id: 1, name: '<PERSON><PERSON>', colors: ['#7CB342', '#8BC34A', '#689F38'], pattern: 'grass' },
            DIRT: { id: 2, name: 'Terre', colors: ['#8D6E63', '#795548', '#6D4C41'], pattern: 'dirt' },
            STONE: { id: 3, name: '<PERSON>', colors: ['#9E9E9E', '#757575', '#616161'], pattern: 'stone' },
            SAND: { id: 4, name: 'Sable', colors: ['#FFF176', '#FFEB3B', '#F9A825'], pattern: 'sand' },
            
            // Arbres
            WOOD_OAK: { id: 10, name: '<PERSON><PERSON><PERSON> (Tronc)', colors: ['#8D6E63', '#6D4C41', '#5D4037'], pattern: 'wood', isTree: true },
            WOOD_BIRCH: { id: 11, name: 'Bouleau (Tronc)', colors: ['#F5F5F5', '#E0E0E0', '#BDBDBD'], pattern: 'wood', isTree: true },
            WOOD_SPRUCE: { id: 12, name: 'Épicéa (Tronc)', colors: ['#5D4037', '#4E342E', '#3E2723'], pattern: 'wood', isTree: true },
            
            // Feuilles
            LEAVES_OAK: { id: 20, name: 'Feuilles de Chêne', colors: ['#4CAF50', '#388E3C', '#2E7D32'], pattern: 'leaves', isTree: true },
            LEAVES_BIRCH: { id: 21, name: 'Feuilles de Bouleau', colors: ['#8BC34A', '#689F38', '#558B2F'], pattern: 'leaves', isTree: true },
            LEAVES_SPRUCE: { id: 22, name: 'Feuilles d\'Épicéa', colors: ['#2E7D32', '#1B5E20', '#0D5302'], pattern: 'leaves', isTree: true },
            
            // Minerais
            COAL_ORE: { id: 30, name: 'Minerai de Charbon', colors: ['#424242', '#212121', '#000000'], pattern: 'ore' },
            IRON_ORE: { id: 31, name: 'Minerai de Fer', colors: ['#8D6E63', '#6D4C41', '#D7CCC8'], pattern: 'ore' },
            GOLD_ORE: { id: 32, name: 'Minerai d\'Or', colors: ['#FFD54F', '#FFC107', '#FF8F00'], pattern: 'ore' },
            
            // Autres
            WATER: { id: 40, name: 'Eau', colors: ['#2196F3', '#1976D2', '#0D47A1'], pattern: 'water' },
            AIR: { id: 0, name: 'Air', colors: ['transparent'], pattern: 'none' }
        };
        
        this.logger.info('TextureGenerator initialisé', {
            blockTypes: Object.keys(this.blockTypes).length,
            textureSize: this.textureSize
        });
    }
    
    // Générer une texture pour un type de bloc
    generateTexture(blockType) {
        const cacheKey = `${blockType}_${this.textureSize}`;
        
        if (this.textureCache.has(cacheKey)) {
            return this.textureCache.get(cacheKey);
        }
        
        const blockInfo = this.blockTypes[blockType];
        if (!blockInfo) {
            this.logger.warn('Type de bloc inconnu', { blockType });
            return this.generateDefaultTexture();
        }
        
        const canvas = document.createElement('canvas');
        canvas.width = this.textureSize;
        canvas.height = this.textureSize;
        const ctx = canvas.getContext('2d');
        
        // Générer la texture selon le pattern
        switch (blockInfo.pattern) {
            case 'grass':
                this.generateGrassTexture(ctx, blockInfo.colors);
                break;
            case 'dirt':
                this.generateDirtTexture(ctx, blockInfo.colors);
                break;
            case 'stone':
                this.generateStoneTexture(ctx, blockInfo.colors);
                break;
            case 'sand':
                this.generateSandTexture(ctx, blockInfo.colors);
                break;
            case 'wood':
                this.generateWoodTexture(ctx, blockInfo.colors);
                break;
            case 'leaves':
                this.generateLeavesTexture(ctx, blockInfo.colors);
                break;
            case 'ore':
                this.generateOreTexture(ctx, blockInfo.colors);
                break;
            case 'water':
                this.generateWaterTexture(ctx, blockInfo.colors);
                break;
            default:
                this.generateDefaultTexture(ctx, blockInfo.colors);
        }
        
        // Utiliser Three.js depuis la variable globale (importé dans main.js)
        const THREE = window.THREE;
        if (!THREE) {
            console.error('Three.js non disponible pour TextureGenerator');
            return null;
        }
        
        // Créer la texture Three.js
        const texture = new THREE.CanvasTexture(canvas);
        texture.magFilter = THREE.NearestFilter;
        texture.minFilter = THREE.NearestFilter;
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        
        this.textureCache.set(cacheKey, texture);
        
        this.logger.debug('Texture générée', {
            blockType: blockType,
            pattern: blockInfo.pattern,
            colors: blockInfo.colors.length
        });
        
        return texture;
    }
    
    // Générer texture d'herbe
    generateGrassTexture(ctx, colors) {
        const size = this.textureSize;
        
        // Base verte
        ctx.fillStyle = colors[1];
        ctx.fillRect(0, 0, size, size);
        
        // Ajouter des variations de couleur
        for (let i = 0; i < size * 2; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            const colorIndex = Math.floor(Math.random() * colors.length);
            
            ctx.fillStyle = colors[colorIndex];
            ctx.fillRect(x, y, 1, 1);
        }
        
        // Ajouter des brins d'herbe
        ctx.strokeStyle = colors[0];
        ctx.lineWidth = 1;
        for (let i = 0; i < 8; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x, y - 2);
            ctx.stroke();
        }
    }
    
    // Générer texture de terre
    generateDirtTexture(ctx, colors) {
        const size = this.textureSize;
        
        // Base marron
        ctx.fillStyle = colors[1];
        ctx.fillRect(0, 0, size, size);
        
        // Ajouter des variations et des petites pierres
        for (let i = 0; i < size * 3; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            const colorIndex = Math.floor(Math.random() * colors.length);
            
            ctx.fillStyle = colors[colorIndex];
            ctx.fillRect(x, y, Math.random() > 0.7 ? 2 : 1, Math.random() > 0.7 ? 2 : 1);
        }
    }
    
    // Générer texture de pierre
    generateStoneTexture(ctx, colors) {
        const size = this.textureSize;
        
        // Base grise
        ctx.fillStyle = colors[1];
        ctx.fillRect(0, 0, size, size);
        
        // Ajouter des fissures et variations
        for (let i = 0; i < size * 2; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            const colorIndex = Math.floor(Math.random() * colors.length);
            
            ctx.fillStyle = colors[colorIndex];
            ctx.fillRect(x, y, 1, 1);
        }
        
        // Ajouter des lignes de fissure
        ctx.strokeStyle = colors[2];
        ctx.lineWidth = 1;
        for (let i = 0; i < 3; i++) {
            ctx.beginPath();
            ctx.moveTo(Math.random() * size, Math.random() * size);
            ctx.lineTo(Math.random() * size, Math.random() * size);
            ctx.stroke();
        }
    }
    
    // Générer texture de sable
    generateSandTexture(ctx, colors) {
        const size = this.textureSize;
        
        // Base jaune
        ctx.fillStyle = colors[1];
        ctx.fillRect(0, 0, size, size);
        
        // Ajouter des grains de sable
        for (let i = 0; i < size * 4; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            const colorIndex = Math.floor(Math.random() * colors.length);
            
            ctx.fillStyle = colors[colorIndex];
            ctx.fillRect(x, y, 1, 1);
        }
    }
    
    // Générer texture de bois
    generateWoodTexture(ctx, colors) {
        const size = this.textureSize;
        
        // Base marron
        ctx.fillStyle = colors[1];
        ctx.fillRect(0, 0, size, size);
        
        // Ajouter des anneaux de croissance (lignes verticales)
        ctx.strokeStyle = colors[2];
        ctx.lineWidth = 1;
        for (let i = 0; i < 4; i++) {
            const x = Math.floor(Math.random() * size);
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, size);
            ctx.stroke();
        }
        
        // Ajouter des variations de couleur
        for (let i = 0; i < size; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            const colorIndex = Math.floor(Math.random() * colors.length);
            
            ctx.fillStyle = colors[colorIndex];
            ctx.fillRect(x, y, 1, 1);
        }
    }
    
    // Générer texture de feuilles
    generateLeavesTexture(ctx, colors) {
        const size = this.textureSize;
        
        // Base verte
        ctx.fillStyle = colors[1];
        ctx.fillRect(0, 0, size, size);
        
        // Ajouter des variations pour simuler les feuilles
        for (let i = 0; i < size * 3; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            const colorIndex = Math.floor(Math.random() * colors.length);
            
            ctx.fillStyle = colors[colorIndex];
            ctx.fillRect(x, y, Math.random() > 0.5 ? 2 : 1, Math.random() > 0.5 ? 2 : 1);
        }
        
        // Ajouter quelques trous pour la transparence
        ctx.fillStyle = 'rgba(0,0,0,0)';
        for (let i = 0; i < 5; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            ctx.fillRect(x, y, 1, 1);
        }
    }
    
    // Générer texture de minerai
    generateOreTexture(ctx, colors) {
        const size = this.textureSize;
        
        // Base de pierre
        ctx.fillStyle = '#757575';
        ctx.fillRect(0, 0, size, size);
        
        // Ajouter des veines de minerai
        ctx.fillStyle = colors[0];
        for (let i = 0; i < 8; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            const width = Math.floor(Math.random() * 3) + 1;
            const height = Math.floor(Math.random() * 3) + 1;
            ctx.fillRect(x, y, width, height);
        }
        
        // Ajouter des variations de pierre
        for (let i = 0; i < size; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            ctx.fillStyle = Math.random() > 0.5 ? '#9E9E9E' : '#616161';
            ctx.fillRect(x, y, 1, 1);
        }
    }
    
    // Générer texture d'eau
    generateWaterTexture(ctx, colors) {
        const size = this.textureSize;
        
        // Base bleue
        ctx.fillStyle = colors[1];
        ctx.fillRect(0, 0, size, size);
        
        // Ajouter des ondulations
        for (let i = 0; i < size; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            const colorIndex = Math.floor(Math.random() * colors.length);
            
            ctx.fillStyle = colors[colorIndex];
            ctx.fillRect(x, y, 1, 1);
        }
    }
    
    // Générer texture par défaut
    generateDefaultTexture(ctx = null, colors = ['#FF00FF']) {
        if (!ctx) {
            const canvas = document.createElement('canvas');
            canvas.width = this.textureSize;
            canvas.height = this.textureSize;
            ctx = canvas.getContext('2d');
        }
        
        // Texture magenta pour les blocs non définis
        ctx.fillStyle = colors[0];
        ctx.fillRect(0, 0, this.textureSize, this.textureSize);
        
        const THREE = window.THREE;
        if (!THREE) {
            console.error('Three.js non disponible pour texture par défaut');
            return null;
        }
        
        const texture = new THREE.CanvasTexture(ctx.canvas);
        texture.magFilter = THREE.NearestFilter;
        texture.minFilter = THREE.NearestFilter;
        return texture;
    }
    
    // Vérifier si un bloc est un arbre
    isTreeBlock(blockType) {
        const blockInfo = this.blockTypes[blockType];
        return blockInfo && blockInfo.isTree === true;
    }
    
    // Obtenir les informations d'un bloc
    getBlockInfo(blockType) {
        return this.blockTypes[blockType] || null;
    }
    
    // Obtenir tous les types de blocs d'arbres
    getTreeBlockTypes() {
        return Object.keys(this.blockTypes).filter(type => this.blockTypes[type].isTree);
    }
    
    // Nettoyer le cache de textures
    clearCache() {
        this.textureCache.clear();
        this.logger.info('Cache de textures nettoyé');
    }
}