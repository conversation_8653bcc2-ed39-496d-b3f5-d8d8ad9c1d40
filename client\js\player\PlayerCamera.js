// client/js/player/PlayerCamera.js
// Gestion de la caméra du joueur côté client

export class PlayerCamera {
    constructor() {
        // Créer la caméra Three.js
        this.camera = new THREE.PerspectiveCamera(
            75, // FOV
            window.innerWidth / window.innerHeight, // Aspect ratio
            0.1, // Near plane
            1000 // Far plane
        );

        // Position et rotation
        this.position = { x: 0, y: 100, z: 0 };
        this.rotation = { x: 0, y: 0 };

        // Configuration
        this.eyeHeight = 1.7; // Hauteur des yeux par rapport aux pieds
        this.fov = 75;

        // Initialiser la position de la caméra
        this.setPosition(0, 100, 0);

        // Raycaster pour la détection de blocs
        this.raycaster = new THREE.Raycaster();
        this.raycaster.far = 10; // Distance maximale de détection

        console.log('📷 PlayerCamera initialisée à la position:', this.camera.position);
    }
    
    setPosition(x, y, z) {
        this.position.x = x;
        this.position.y = y + this.eyeHeight; // Ajouter la hauteur des yeux
        this.position.z = z;
        
        this.camera.position.set(this.position.x, this.position.y, this.position.z);
    }
    
    setRotation(pitch, yaw) {
        this.rotation.x = pitch;
        this.rotation.y = yaw;
        
        // Appliquer la rotation à la caméra
        this.camera.rotation.order = 'YXZ';
        this.camera.rotation.y = yaw;
        this.camera.rotation.x = pitch;
    }
    
    setFOV(fov) {
        this.fov = fov;
        this.camera.fov = fov;
        this.camera.updateProjectionMatrix();
    }
    
    getCamera() {
        return this.camera;
    }
    
    getPosition() {
        return { ...this.position };
    }
    
    getRotation() {
        return { ...this.rotation };
    }
    
    // Obtenir la direction de la caméra
    getDirection() {
        const direction = new THREE.Vector3();
        this.camera.getWorldDirection(direction);
        return direction;
    }
    
    // Lancer un rayon pour détecter les blocs
    getTargetBlock(maxDistance = 5) {
        // Obtenir la direction de la caméra
        const direction = this.getDirection();
        
        // Configurer le raycaster
        this.raycaster.set(this.camera.position, direction);
        this.raycaster.far = maxDistance;
        
        // Pour l'instant, retourner une position calculée manuellement
        // TODO: Intégrer avec le système de collision du monde
        const targetPosition = this.camera.position.clone().add(direction.multiplyScalar(maxDistance));
        
        return {
            x: Math.floor(targetPosition.x),
            y: Math.floor(targetPosition.y),
            z: Math.floor(targetPosition.z)
        };
    }
    
    // Obtenir les intersections avec les objets de la scène
    getIntersections(objects) {
        const direction = this.getDirection();
        this.raycaster.set(this.camera.position, direction);
        
        return this.raycaster.intersectObjects(objects, true);
    }
    
    // Calculer la distance à un point
    distanceTo(x, y, z) {
        const dx = this.position.x - x;
        const dy = this.position.y - y;
        const dz = this.position.z - z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    // Vérifier si un point est dans le champ de vision
    isInView(x, y, z, angle = Math.PI / 3) {
        const direction = this.getDirection();
        const toTarget = new THREE.Vector3(
            x - this.position.x,
            y - this.position.y,
            z - this.position.z
        ).normalize();
        
        const dot = direction.dot(toTarget);
        const angleToTarget = Math.acos(dot);
        
        return angleToTarget <= angle;
    }
    
    // Gérer le redimensionnement de la fenêtre
    handleResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
    }
    
    // Obtenir les informations de la caméra pour le debug
    getDebugInfo() {
        return {
            position: this.position,
            rotation: this.rotation,
            fov: this.fov,
            aspect: this.camera.aspect,
            direction: this.getDirection()
        };
    }
    
    // Appliquer un effet de secousse (pour les impacts, etc.)
    shake(intensity = 1, duration = 200) {
        const originalPosition = { ...this.position };
        const startTime = Date.now();
        
        const shakeEffect = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;
            
            if (progress >= 1) {
                // Restaurer la position originale
                this.setPosition(originalPosition.x, originalPosition.y - this.eyeHeight, originalPosition.z);
                return;
            }
            
            // Calculer l'intensité décroissante
            const currentIntensity = intensity * (1 - progress);
            
            // Ajouter un décalage aléatoire
            const offsetX = (Math.random() - 0.5) * currentIntensity;
            const offsetY = (Math.random() - 0.5) * currentIntensity;
            const offsetZ = (Math.random() - 0.5) * currentIntensity;
            
            this.camera.position.set(
                originalPosition.x + offsetX,
                originalPosition.y + offsetY,
                originalPosition.z + offsetZ
            );
            
            requestAnimationFrame(shakeEffect);
        };
        
        shakeEffect();
    }
    
    // Interpoler vers une nouvelle position (pour les corrections serveur)
    lerpToPosition(targetX, targetY, targetZ, factor = 0.1) {
        this.position.x += (targetX - this.position.x) * factor;
        this.position.y += (targetY + this.eyeHeight - this.position.y) * factor;
        this.position.z += (targetZ - this.position.z) * factor;
        
        this.camera.position.set(this.position.x, this.position.y, this.position.z);
    }
    
    // Interpoler vers une nouvelle rotation
    lerpToRotation(targetPitch, targetYaw, factor = 0.1) {
        // Gérer la rotation circulaire pour le yaw
        let deltaYaw = targetYaw - this.rotation.y;
        if (deltaYaw > Math.PI) deltaYaw -= 2 * Math.PI;
        if (deltaYaw < -Math.PI) deltaYaw += 2 * Math.PI;
        
        this.rotation.x += (targetPitch - this.rotation.x) * factor;
        this.rotation.y += deltaYaw * factor;
        
        this.setRotation(this.rotation.x, this.rotation.y);
    }
    
    // Obtenir la matrice de vue
    getViewMatrix() {
        return this.camera.matrixWorldInverse;
    }
    
    // Obtenir la matrice de projection
    getProjectionMatrix() {
        return this.camera.projectionMatrix;
    }
    
    // Convertir les coordonnées monde en coordonnées écran
    worldToScreen(worldPosition) {
        const vector = worldPosition.clone();
        vector.project(this.camera);
        
        return {
            x: (vector.x + 1) * window.innerWidth / 2,
            y: (-vector.y + 1) * window.innerHeight / 2,
            z: vector.z
        };
    }
    
    // Convertir les coordonnées écran en coordonnées monde
    screenToWorld(screenX, screenY, depth = 1) {
        const vector = new THREE.Vector3(
            (screenX / window.innerWidth) * 2 - 1,
            -(screenY / window.innerHeight) * 2 + 1,
            depth
        );
        
        vector.unproject(this.camera);
        return vector;
    }
}
