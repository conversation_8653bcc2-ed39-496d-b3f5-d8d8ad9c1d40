// server/utils/SimplexNoise.js
// Version simplifiée et fonctionnelle du SimplexNoise pour le serveur

export class SimplexNoise {
    constructor(seed = 0) {
        this.seed = seed;
        
        // Générateur de nombres pseudo-aléatoires basé sur la graine
        this.random = this.seededRandom(seed);
        
        // Table de permutation
        this.perm = new Array(512);
        this.permMod12 = new Array(512);
        
        // Initialiser la table de permutation
        const p = [];
        for (let i = 0; i < 256; i++) {
            p[i] = i;
        }
        
        // Mélanger avec la graine
        for (let i = 255; i > 0; i--) {
            const j = Math.floor(this.random() * (i + 1));
            [p[i], p[j]] = [p[j], p[i]];
        }
        
        // Dupliquer pour éviter les débordements
        for (let i = 0; i < 512; i++) {
            this.perm[i] = p[i & 255];
            this.permMod12[i] = this.perm[i] % 12;
        }
        
        // Gradients pour le bruit 3D
        this.grad3 = [
            [1,1,0],[-1,1,0],[1,-1,0],[-1,-1,0],
            [1,0,1],[-1,0,1],[1,0,-1],[-1,0,-1],
            [0,1,1],[0,-1,1],[0,1,-1],[0,-1,-1]
        ];
        
        // Facteurs pour le bruit Simplex
        this.F2 = 0.5 * (Math.sqrt(3.0) - 1.0);
        this.G2 = (3.0 - Math.sqrt(3.0)) / 6.0;
        this.F3 = 1.0 / 3.0;
        this.G3 = 1.0 / 6.0;
    }
    
    seededRandom(seed) {
        let m = 0x80000000; // 2^31
        let a = 1103515245;
        let c = 12345;
        let state = seed;
        
        return function() {
            state = (a * state + c) % m;
            return state / (m - 1);
        };
    }
    
    // Fonction de lissage
    fade(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    // Interpolation linéaire
    lerp(a, b, t) {
        return (1 - t) * a + t * b;
    }
    
    // Produit scalaire pour les gradients 2D
    grad2(hash, x, y) {
        const h = hash & 7;
        const u = h < 4 ? x : y;
        const v = h < 4 ? y : x;
        return ((h & 1) ? -u : u) + ((h & 2) ? -2.0 * v : 2.0 * v);
    }
    
    // Produit scalaire pour les gradients 3D
    dot3(g, x, y, z) {
        return g[0] * x + g[1] * y + g[2] * z;
    }
    
    // Bruit 2D simplifié
    noise2D(xin, yin) {
        // Version simplifiée utilisant du bruit de Perlin
        const X = Math.floor(xin) & 255;
        const Y = Math.floor(yin) & 255;
        
        xin -= Math.floor(xin);
        yin -= Math.floor(yin);
        
        const u = this.fade(xin);
        const v = this.fade(yin);
        
        const A = this.perm[X] + Y;
        const AA = this.perm[A];
        const AB = this.perm[A + 1];
        const B = this.perm[X + 1] + Y;
        const BA = this.perm[B];
        const BB = this.perm[B + 1];
        
        return this.lerp(
            this.lerp(
                this.grad2(this.perm[AA], xin, yin),
                this.grad2(this.perm[BA], xin - 1, yin),
                u
            ),
            this.lerp(
                this.grad2(this.perm[AB], xin, yin - 1),
                this.grad2(this.perm[BB], xin - 1, yin - 1),
                u
            ),
            v
        );
    }
    
    // Bruit 3D simplifié
    noise3D(xin, yin, zin) {
        // Version simplifiée utilisant du bruit de Perlin
        const X = Math.floor(xin) & 255;
        const Y = Math.floor(yin) & 255;
        const Z = Math.floor(zin) & 255;
        
        xin -= Math.floor(xin);
        yin -= Math.floor(yin);
        zin -= Math.floor(zin);
        
        const u = this.fade(xin);
        const v = this.fade(yin);
        const w = this.fade(zin);
        
        const A = this.perm[X] + Y;
        const AA = this.perm[A] + Z;
        const AB = this.perm[A + 1] + Z;
        const B = this.perm[X + 1] + Y;
        const BA = this.perm[B] + Z;
        const BB = this.perm[B + 1] + Z;
        
        const grad1 = this.grad3[this.perm[AA] % 12];
        const grad2 = this.grad3[this.perm[BA] % 12];
        const grad3 = this.grad3[this.perm[AB] % 12];
        const grad4 = this.grad3[this.perm[BB] % 12];
        const grad5 = this.grad3[this.perm[AA + 1] % 12];
        const grad6 = this.grad3[this.perm[BA + 1] % 12];
        const grad7 = this.grad3[this.perm[AB + 1] % 12];
        const grad8 = this.grad3[this.perm[BB + 1] % 12];
        
        return this.lerp(
            this.lerp(
                this.lerp(
                    this.dot3(grad1, xin, yin, zin),
                    this.dot3(grad2, xin - 1, yin, zin),
                    u
                ),
                this.lerp(
                    this.dot3(grad3, xin, yin - 1, zin),
                    this.dot3(grad4, xin - 1, yin - 1, zin),
                    u
                ),
                v
            ),
            this.lerp(
                this.lerp(
                    this.dot3(grad5, xin, yin, zin - 1),
                    this.dot3(grad6, xin - 1, yin, zin - 1),
                    u
                ),
                this.lerp(
                    this.dot3(grad7, xin, yin - 1, zin - 1),
                    this.dot3(grad8, xin - 1, yin - 1, zin - 1),
                    u
                ),
                v
            ),
            w
        );
    }
    
    // Bruit fractal (octaves multiples)
    fractalNoise2D(x, y, octaves = 4, persistence = 0.5, scale = 1.0) {
        let value = 0;
        let amplitude = 1;
        let frequency = scale;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            value += this.noise2D(x * frequency, y * frequency) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= 2;
        }
        
        return value / maxValue;
    }
    
    fractalNoise3D(x, y, z, octaves = 4, persistence = 0.5, scale = 1.0) {
        let value = 0;
        let amplitude = 1;
        let frequency = scale;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            value += this.noise3D(x * frequency, y * frequency, z * frequency) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= 2;
        }
        
        return value / maxValue;
    }
}
