// client/js/ui/OptionsManager.js
// Gestionnaire des options et paramètres côté client

export class OptionsManager {
    constructor() {
        this.isOpen = false;
        this.options = {};
        this.defaultOptions = {
            // Graphiques
            renderDistance: 6,
            fov: 75,
            quality: 'medium',
            shadows: true,
            antialiasing: true,
            
            // Contrôles
            mouseSensitivity: 1.0,
            autoClimb: true,
            flyMode: false,
            
            // Audio
            masterVolume: 1.0,
            musicVolume: 0.7,
            soundVolume: 0.8,
            
            // Réseau
            serverAddress: 'localhost:3000',
            autoReconnect: true,
            
            // Interface
            showDebugInfo: false,
            showFPS: true,
            chatFadeTime: 5000,
            
            // Gameplay
            difficulty: 'normal',
            gameMode: 'survival'
        };
        
        // Éléments DOM
        this.optionsMenu = null;
        this.elements = {};
        
        this.initializeElements();
        this.setupEventListeners();
        
        console.log('⚙️ OptionsManager initialisé');
    }
    
    async init() {
        // Charger les options sauvegardées
        await this.loadOptions();
        
        // Appliquer les options
        this.applyAllOptions();
        
        console.log('⚙️ Options chargées et appliquées');
    }
    
    initializeElements() {
        this.optionsMenu = document.getElementById('options-menu');
        
        if (!this.optionsMenu) {
            console.warn('⚠️ Menu des options non trouvé');
            return;
        }
        
        // Récupérer tous les éléments d'options
        this.elements = {
            // Graphiques
            renderDistance: document.getElementById('render-distance'),
            renderDistanceValue: document.getElementById('render-distance-value'),
            fov: document.getElementById('fov'),
            fovValue: document.getElementById('fov-value'),
            
            // Contrôles
            mouseSensitivity: document.getElementById('mouse-sensitivity'),
            mouseSensitivityValue: document.getElementById('mouse-sensitivity-value'),
            autoClimb: document.getElementById('auto-climb'),
            
            // Réseau
            serverAddress: document.getElementById('server-address'),
            reconnectButton: document.getElementById('reconnect-button'),
            
            // Boutons
            resetOptions: document.getElementById('reset-options'),
            closeOptions: document.getElementById('close-options')
        };
        
        // Vérifier les éléments manquants
        const missingElements = Object.keys(this.elements).filter(key => !this.elements[key]);
        if (missingElements.length > 0) {
            console.warn('⚠️ Éléments d\'options manquants:', missingElements);
        }
    }
    
    setupEventListeners() {
        // Boutons principaux
        if (this.elements.closeOptions) {
            this.elements.closeOptions.addEventListener('click', () => this.close());
        }
        
        if (this.elements.resetOptions) {
            this.elements.resetOptions.addEventListener('click', () => this.resetToDefaults());
        }
        
        if (this.elements.reconnectButton) {
            this.elements.reconnectButton.addEventListener('click', () => this.reconnectToServer());
        }
        
        // Sliders avec mise à jour en temps réel
        this.setupSlider('renderDistance', (value) => {
            this.setOption('renderDistance', parseInt(value));
            this.applyRenderDistance();
        });
        
        this.setupSlider('fov', (value) => {
            this.setOption('fov', parseInt(value));
            this.applyFOV();
        });
        
        this.setupSlider('mouseSensitivity', (value) => {
            this.setOption('mouseSensitivity', parseFloat(value));
            this.applyMouseSensitivity();
        });
        
        // Checkboxes
        this.setupCheckbox('autoClimb', (checked) => {
            this.setOption('autoClimb', checked);
            this.applyAutoClimb();
        });
        
        // Champs de texte
        this.setupTextInput('serverAddress', (value) => {
            this.setOption('serverAddress', value);
        });
        
        // Fermeture avec Escape
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Escape' && this.isOpen) {
                this.close();
            }
        });
        
        // Fermeture en cliquant à l'extérieur
        if (this.optionsMenu) {
            this.optionsMenu.addEventListener('click', (e) => {
                if (e.target === this.optionsMenu) {
                    this.close();
                }
            });
        }
    }
    
    setupSlider(elementName, callback) {
        const slider = this.elements[elementName];
        const valueDisplay = this.elements[elementName + 'Value'];
        
        if (!slider) return;
        
        slider.addEventListener('input', (e) => {
            const value = e.target.value;
            if (valueDisplay) {
                valueDisplay.textContent = this.formatSliderValue(elementName, value);
            }
            callback(value);
        });
    }
    
    setupCheckbox(elementName, callback) {
        const checkbox = this.elements[elementName];
        if (!checkbox) return;
        
        checkbox.addEventListener('change', (e) => {
            callback(e.target.checked);
        });
    }
    
    setupTextInput(elementName, callback) {
        const input = this.elements[elementName];
        if (!input) return;
        
        input.addEventListener('change', (e) => {
            callback(e.target.value);
        });
        
        input.addEventListener('blur', (e) => {
            callback(e.target.value);
        });
    }
    
    formatSliderValue(sliderName, value) {
        switch (sliderName) {
            case 'fov':
                return value + '°';
            case 'mouseSensitivity':
                return parseFloat(value).toFixed(1);
            default:
                return value;
        }
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        if (!this.optionsMenu || this.isOpen) return;
        
        this.isOpen = true;
        this.optionsMenu.classList.remove('hidden');
        
        // Mettre à jour l'affichage des valeurs
        this.updateDisplayValues();
        
        // Désactiver les contrôles du jeu
        this.disableGameControls();
        
        console.log('⚙️ Menu des options ouvert');
    }
    
    close() {
        if (!this.optionsMenu || !this.isOpen) return;
        
        this.isOpen = false;
        this.optionsMenu.classList.add('hidden');
        
        // Sauvegarder les options
        this.saveOptions();
        
        // Réactiver les contrôles du jeu
        this.enableGameControls();
        
        console.log('⚙️ Menu des options fermé');
    }
    
    updateDisplayValues() {
        // Mettre à jour tous les éléments avec les valeurs actuelles
        Object.keys(this.elements).forEach(key => {
            const element = this.elements[key];
            if (!element) return;
            
            const optionKey = key.replace('Value', '');
            const value = this.getOption(optionKey);
            
            if (element.type === 'range') {
                element.value = value;
                const valueDisplay = this.elements[key + 'Value'];
                if (valueDisplay) {
                    valueDisplay.textContent = this.formatSliderValue(optionKey, value);
                }
            } else if (element.type === 'checkbox') {
                element.checked = value;
            } else if (element.type === 'text') {
                element.value = value;
            }
        });
    }
    
    // Gestion des options
    setOption(key, value) {
        this.options[key] = value;
        console.log(`⚙️ Option mise à jour: ${key} = ${value}`);
    }
    
    getOption(key) {
        return this.options[key] !== undefined ? this.options[key] : this.defaultOptions[key];
    }
    
    resetToDefaults() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser toutes les options ?')) {
            this.options = { ...this.defaultOptions };
            this.updateDisplayValues();
            this.applyAllOptions();
            this.saveOptions();
            
            console.log('⚙️ Options réinitialisées aux valeurs par défaut');
        }
    }
    
    // Application des options
    applyAllOptions() {
        this.applyRenderDistance();
        this.applyFOV();
        this.applyMouseSensitivity();
        this.applyAutoClimb();
        this.applyQuality();
    }
    
    applyRenderDistance() {
        const distance = this.getOption('renderDistance');
        if (window.game && window.game.clientWorld) {
            window.game.clientWorld.setRenderDistance(distance);
        }
    }
    
    applyFOV() {
        const fov = this.getOption('fov');
        if (window.game && window.game.playerController) {
            const camera = window.game.playerController.camera;
            if (camera) {
                camera.setFOV(fov);
            }
        }
    }
    
    applyMouseSensitivity() {
        const sensitivity = this.getOption('mouseSensitivity');
        if (window.game && window.game.playerController) {
            window.game.playerController.setMouseSensitivity(sensitivity);
        }
    }
    
    applyAutoClimb() {
        const autoClimb = this.getOption('autoClimb');
        if (window.game && window.game.playerController) {
            // TODO: Implémenter l'auto-climb dans PlayerController
            console.log('Auto-climb:', autoClimb);
        }
    }
    
    applyQuality() {
        const quality = this.getOption('quality');
        if (window.game && window.game.renderer) {
            window.game.renderer.setQuality(quality);
        }
    }
    
    // Gestion du réseau
    reconnectToServer() {
        if (window.game && window.game.reconnect) {
            window.game.reconnect();
        }
    }
    
    // Contrôles du jeu
    disableGameControls() {
        // Empêcher les contrôles du jeu pendant que le menu est ouvert
        if (window.game && window.game.playerController) {
            // TODO: Implémenter la désactivation temporaire des contrôles
        }
    }
    
    enableGameControls() {
        // Réactiver les contrôles du jeu
        if (window.game && window.game.playerController) {
            // TODO: Implémenter la réactivation des contrôles
        }
    }
    
    // Sauvegarde et chargement
    async saveOptions() {
        try {
            localStorage.setItem('jscraft_options', JSON.stringify(this.options));
            console.log('⚙️ Options sauvegardées');
        } catch (error) {
            console.error('❌ Erreur sauvegarde options:', error);
        }
    }
    
    async loadOptions() {
        try {
            const saved = localStorage.getItem('jscraft_options');
            if (saved) {
                const loadedOptions = JSON.parse(saved);
                this.options = { ...this.defaultOptions, ...loadedOptions };
                console.log('⚙️ Options chargées depuis localStorage');
            } else {
                this.options = { ...this.defaultOptions };
                console.log('⚙️ Options par défaut utilisées');
            }
        } catch (error) {
            console.error('❌ Erreur chargement options:', error);
            this.options = { ...this.defaultOptions };
        }
    }
    
    // Export/Import des options
    exportOptions() {
        const dataStr = JSON.stringify(this.options, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'jscraft_options.json';
        link.click();
        
        console.log('⚙️ Options exportées');
    }
    
    importOptions(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedOptions = JSON.parse(e.target.result);
                this.options = { ...this.defaultOptions, ...importedOptions };
                this.updateDisplayValues();
                this.applyAllOptions();
                this.saveOptions();
                
                console.log('⚙️ Options importées');
            } catch (error) {
                console.error('❌ Erreur import options:', error);
                alert('Fichier d\'options invalide');
            }
        };
        reader.readAsText(file);
    }
    
    // Statistiques et debug
    getDebugInfo() {
        return {
            isOpen: this.isOpen,
            optionsCount: Object.keys(this.options).length,
            defaultsCount: Object.keys(this.defaultOptions).length,
            elementsFound: Object.keys(this.elements).filter(key => this.elements[key]).length
        };
    }
    
    // Nettoyage
    dispose() {
        console.log('🧹 Nettoyage OptionsManager...');
        
        // Sauvegarder avant de nettoyer
        this.saveOptions();
        
        // Fermer le menu
        this.close();
        
        // Nettoyer les références
        this.elements = {};
        this.options = {};
    }
}
