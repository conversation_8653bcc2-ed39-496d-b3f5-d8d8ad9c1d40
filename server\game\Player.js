// server/game/Player.js
// Logique serveur du joueur - LA SOURCE DE VÉRITÉ
import { GAME_CONFIG, PLAYER_STATES, Utils } from '../../shared/constants.js';

export class Player {
    constructor(id) {
        this.id = id;
        
        // Position et mouvement (source de vérité)
        this.position = { x: 0, y: 100, z: 0 };
        this.velocity = { x: 0, y: 0, z: 0 };
        this.rotation = { x: 0, y: 0 }; // Rotation de la caméra
        
        // État physique
        this.onGround = false;
        this.isJumping = false;
        this.state = PLAYER_STATES.IDLE;
        
        // Propriétés de collision
        this.height = GAME_CONFIG.PLAYER_HEIGHT;
        this.eyeHeight = GAME_CONFIG.PLAYER_EYE_HEIGHT;
        this.collisionRadius = GAME_CONFIG.PLAYER_COLLISION_RADIUS;
        this.stepHeight = GAME_CONFIG.PLAYER_STEP_HEIGHT;
        
        // Modes spéciaux
        this.flyMode = false;
        this.autoClimb = true;
        
        // Minage
        this.isMining = false;
        this.miningTarget = null;
        this.miningStartTime = 0;
        
        // Inventaire (simplifié pour le serveur)
        this.inventory = new Array(36).fill(null);
        this.selectedSlot = 0;
        
        // Statistiques
        this.health = 100;
        this.hunger = 100;
        this.experience = 0;
        
        // Timestamps
        this.lastUpdate = Date.now();
        this.lastInputTime = Date.now();
        
        console.log(`👤 Joueur ${id} créé à la position (${this.position.x}, ${this.position.y}, ${this.position.z})`);
    }
    
    // Appliquer les entrées du client
    applyInput(input) {
        this.lastInputTime = Date.now();
        
        // Valider et appliquer les entrées
        const speed = GAME_CONFIG.MOVEMENT_SPEED;
        const sprintMultiplier = input.sprint ? GAME_CONFIG.SPRINT_MULTIPLIER : 1.0;
        
        // Réinitialiser la vélocité horizontale
        this.velocity.x = 0;
        this.velocity.z = 0;
        
        // Calculer le mouvement basé sur les touches pressées
        let moveX = 0;
        let moveZ = 0;
        
        if (input.keys) {
            if (input.keys.includes('KeyW') || input.keys.includes('ArrowUp')) moveZ -= 1;
            if (input.keys.includes('KeyS') || input.keys.includes('ArrowDown')) moveZ += 1;
            if (input.keys.includes('KeyA') || input.keys.includes('ArrowLeft')) moveX -= 1;
            if (input.keys.includes('KeyD') || input.keys.includes('ArrowRight')) moveX += 1;
        }
        
        // Normaliser le vecteur de mouvement
        if (moveX !== 0 || moveZ !== 0) {
            const length = Math.sqrt(moveX * moveX + moveZ * moveZ);
            moveX /= length;
            moveZ /= length;
            
            // Appliquer la rotation de la caméra
            if (input.rotation) {
                this.rotation.x = Utils.clamp(input.rotation.x, -Math.PI/2, Math.PI/2);
                this.rotation.y = input.rotation.y;
            }
            
            const cos = Math.cos(this.rotation.y);
            const sin = Math.sin(this.rotation.y);
            
            this.velocity.x = (moveX * cos - moveZ * sin) * speed * sprintMultiplier;
            this.velocity.z = (moveX * sin + moveZ * cos) * speed * sprintMultiplier;
            
            this.state = input.sprint ? PLAYER_STATES.RUNNING : PLAYER_STATES.WALKING;
        } else {
            this.state = PLAYER_STATES.IDLE;
        }
        
        // Gestion du saut
        if (input.keys && input.keys.includes('Space')) {
            if (this.flyMode) {
                this.velocity.y = speed * sprintMultiplier;
            } else if (this.onGround && !this.isJumping) {
                this.velocity.y = GAME_CONFIG.JUMP_FORCE;
                this.isJumping = true;
                this.onGround = false;
                this.state = PLAYER_STATES.JUMPING;
            }
        }
        
        // Mode vol - descente
        if (this.flyMode && input.keys && input.keys.includes('ShiftLeft')) {
            this.velocity.y = -speed * sprintMultiplier;
        }
        
        // Mise à jour de la rotation de la caméra
        if (input.rotation) {
            this.rotation.x = Utils.clamp(input.rotation.x, -Math.PI/2, Math.PI/2);
            this.rotation.y = input.rotation.y;
        }
    }
    
    // Mise à jour physique principale (appelée par GameManager)
    update(deltaTime, worldManager) {
        const oldPosition = { ...this.position };
        
        // Appliquer la gravité si pas en mode vol
        if (!this.flyMode) {
            this.velocity.y -= GAME_CONFIG.GRAVITY * deltaTime;
        } else if (this.velocity.y !== 0) {
            // En mode vol, réduire progressivement la vélocité verticale
            this.velocity.y *= 0.9;
            if (Math.abs(this.velocity.y) < 0.1) {
                this.velocity.y = 0;
            }
        }
        
        // Appliquer le mouvement
        this.position.x += this.velocity.x * deltaTime;
        this.position.y += this.velocity.y * deltaTime;
        this.position.z += this.velocity.z * deltaTime;
        
        // Vérifier les collisions et ajuster la position
        this.handleCollisions(worldManager, oldPosition);
        
        // Mettre à jour l'état
        this.updateState();
        
        // Gérer le minage
        if (this.isMining) {
            this.updateMining(worldManager);
        }
        
        this.lastUpdate = Date.now();
    }
    
    handleCollisions(worldManager, oldPosition) {
        // Vérification du sol
        const groundHeight = worldManager.getGroundHeightAt(this.position.x, this.position.z);
        const minY = groundHeight + this.eyeHeight;
        
        if (this.position.y <= minY) {
            this.position.y = minY;
            if (this.velocity.y <= 0) {
                this.velocity.y = 0;
                this.onGround = true;
                this.isJumping = false;
                
                // Auto-climb si activé
                if (this.autoClimb && this.velocity.x !== 0 || this.velocity.z !== 0) {
                    const stepUpHeight = groundHeight + this.eyeHeight + this.stepHeight;
                    if (stepUpHeight - this.position.y <= this.stepHeight) {
                        this.position.y = stepUpHeight;
                    }
                }
            }
        } else {
            this.onGround = false;
        }
        
        // Vérification des collisions horizontales
        const collisionPoints = this.getCollisionPoints();
        let hasCollision = false;
        
        for (const point of collisionPoints) {
            if (worldManager.hasCollisionAt(point.x, this.position.y, point.z)) {
                hasCollision = true;
                break;
            }
        }
        
        if (hasCollision) {
            // Restaurer l'ancienne position horizontale
            this.position.x = oldPosition.x;
            this.position.z = oldPosition.z;
            this.velocity.x = 0;
            this.velocity.z = 0;
        }
        
        // Vérification du plafond
        const ceilingY = this.position.y + (this.height - this.eyeHeight);
        if (worldManager.hasCollisionAt(this.position.x, ceilingY, this.position.z)) {
            if (this.velocity.y > 0) {
                this.velocity.y = 0;
            }
        }
        
        // Limites du monde
        this.position.x = Utils.clamp(this.position.x, -GAME_CONFIG.WORLD_BORDER, GAME_CONFIG.WORLD_BORDER);
        this.position.z = Utils.clamp(this.position.z, -GAME_CONFIG.WORLD_BORDER, GAME_CONFIG.WORLD_BORDER);
        this.position.y = Math.max(this.position.y, GAME_CONFIG.WORLD_HEIGHT_MIN);
    }
    
    getCollisionPoints() {
        const points = [];
        const segments = 8;
        
        for (let i = 0; i < segments; i++) {
            const angle = (i / segments) * Math.PI * 2;
            const x = this.position.x + Math.cos(angle) * this.collisionRadius;
            const z = this.position.z + Math.sin(angle) * this.collisionRadius;
            points.push({ x, z });
        }
        
        return points;
    }
    
    updateState() {
        if (!this.onGround && this.velocity.y < 0) {
            this.state = PLAYER_STATES.FALLING;
        } else if (this.flyMode) {
            this.state = PLAYER_STATES.FLYING;
        } else if (this.isMining) {
            this.state = PLAYER_STATES.MINING;
        }
        // Les autres états sont définis dans applyInput
    }
    
    // Gestion du minage
    startMining(targetPosition, worldManager) {
        const distance = Utils.distance3D(
            this.position.x, this.position.y, this.position.z,
            targetPosition.x, targetPosition.y, targetPosition.z
        );
        
        if (distance > GAME_CONFIG.MINING_REACH) {
            return { success: false, reason: 'Trop loin' };
        }
        
        const blockType = worldManager.getBlockTypeAt(targetPosition.x, targetPosition.y, targetPosition.z);
        if (blockType === 0) { // Air
            return { success: false, reason: 'Aucun bloc à miner' };
        }
        
        this.isMining = true;
        this.miningTarget = targetPosition;
        this.miningStartTime = Date.now();
        
        return { success: true, blockType, estimatedTime: GAME_CONFIG.MINING_TIME };
    }
    
    updateMining(worldManager) {
        if (!this.isMining || !this.miningTarget) return;
        
        const elapsed = Date.now() - this.miningStartTime;
        if (elapsed >= GAME_CONFIG.MINING_TIME) {
            // Minage terminé
            const blockType = worldManager.removeBlock(
                this.miningTarget.x, 
                this.miningTarget.y, 
                this.miningTarget.z
            );
            
            if (blockType > 0) {
                // Ajouter le bloc à l'inventaire
                this.addToInventory(blockType);
            }
            
            this.stopMining();
        }
    }
    
    stopMining() {
        this.isMining = false;
        this.miningTarget = null;
        this.miningStartTime = 0;
    }
    
    // Gestion de l'inventaire
    addToInventory(blockType, quantity = 1) {
        // Logique simplifiée d'ajout à l'inventaire
        for (let i = 0; i < this.inventory.length; i++) {
            if (this.inventory[i] === null) {
                this.inventory[i] = { type: blockType, quantity };
                return true;
            } else if (this.inventory[i].type === blockType) {
                this.inventory[i].quantity += quantity;
                return true;
            }
        }
        return false; // Inventaire plein
    }
    
    // Utilitaires
    setPosition(x, y, z) {
        this.position.x = x;
        this.position.y = y;
        this.position.z = z;
    }
    
    // Retourne l'état pour la synchronisation réseau
    getNetworkState() {
        return {
            id: this.id,
            position: { ...this.position },
            velocity: { ...this.velocity },
            rotation: { ...this.rotation },
            state: this.state,
            onGround: this.onGround,
            health: this.health,
            isMining: this.isMining
        };
    }
    
    // Retourne l'état complet (pour sauvegarde)
    getFullState() {
        return {
            id: this.id,
            position: { ...this.position },
            velocity: { ...this.velocity },
            rotation: { ...this.rotation },
            state: this.state,
            onGround: this.onGround,
            flyMode: this.flyMode,
            autoClimb: this.autoClimb,
            inventory: [...this.inventory],
            selectedSlot: this.selectedSlot,
            health: this.health,
            hunger: this.hunger,
            experience: this.experience
        };
    }
}
