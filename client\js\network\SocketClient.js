// client/js/network/SocketClient.js
// Gestion de la communication WebSocket côté client
import { MESSAGE_TYPES } from '../../../shared/constants.js';

export class SocketClient {
    constructor(url, gameInstance) {
        this.url = url;
        this.game = gameInstance;
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1 seconde
        
        // Callbacks
        this.onConnect = null;
        this.onDisconnect = null;
        this.onError = null;
        
        // Statistiques
        this.stats = {
            messagesSent: 0,
            messagesReceived: 0,
            bytesReceived: 0,
            lastPing: 0,
            averagePing: 0
        };
        
        // Buffer pour les messages en attente
        this.messageQueue = [];
        this.isReconnecting = false;
        
        this.connect();
    }
    
    connect() {
        try {
            console.log(`🔌 Tentative de connexion à ${this.url}`);
            
            this.ws = new WebSocket(this.url);
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('❌ Erreur lors de la création de la WebSocket:', error);
            this.handleConnectionError(error);
        }
    }
    
    setupEventHandlers() {
        this.ws.onopen = (event) => {
            console.log('✅ Connexion WebSocket établie');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.isReconnecting = false;
            
            // Envoyer les messages en attente
            this.flushMessageQueue();
            
            // Démarrer le ping
            this.startPing();
            
            if (this.onConnect) {
                this.onConnect(event);
            }
        };
        
        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
                
                this.stats.messagesReceived++;
                this.stats.bytesReceived += event.data.length;
                
            } catch (error) {
                console.error('❌ Erreur parsing message serveur:', error);
            }
        };
        
        this.ws.onclose = (event) => {
            console.log('🔌 Connexion WebSocket fermée', event.code, event.reason);
            this.isConnected = false;
            
            if (this.onDisconnect) {
                this.onDisconnect(event);
            }
            
            // Reconnexion automatique désactivée - gérée par main.js
            // if (!this.isReconnecting && this.reconnectAttempts < this.maxReconnectAttempts) {
            //     this.attemptReconnect();
            // }
        };
        
        this.ws.onerror = (error) => {
            console.error('❌ Erreur WebSocket:', error);
            this.handleConnectionError(error);
        };
    }
    
    handleMessage(message) {
        const { type, payload } = message;
        
        // Gérer les messages système
        switch (type) {
            case 'server:ping':
                this.handlePing(payload);
                return;
                
            case 'server:pong':
                this.handlePong(payload);
                return;
        }
        
        // Transmettre au jeu
        if (this.game && this.game.onServerMessage) {
            this.game.onServerMessage(message);
        }
    }
    
    handlePing(payload) {
        // Répondre au ping du serveur
        this.send({
            type: 'client:pong',
            payload: { timestamp: payload.timestamp }
        });
    }
    
    handlePong(payload) {
        // Calculer le ping
        const now = Date.now();
        const ping = now - payload.timestamp;
        this.stats.lastPing = ping;
        
        // Calculer la moyenne mobile
        if (this.stats.averagePing === 0) {
            this.stats.averagePing = ping;
        } else {
            this.stats.averagePing = (this.stats.averagePing * 0.9) + (ping * 0.1);
        }
    }
    
    send(message) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            try {
                const messageStr = JSON.stringify(message);
                this.ws.send(messageStr);
                this.stats.messagesSent++;
                return true;
            } catch (error) {
                console.error('❌ Erreur envoi message:', error);
                return false;
            }
        } else {
            // Ajouter à la file d'attente
            this.messageQueue.push(message);
            return false;
        }
    }
    
    // Méthodes spécifiques pour les types de messages
    sendInput(inputData) {
        return this.send({
            type: MESSAGE_TYPES.CLIENT_INPUT,
            payload: inputData
        });
    }
    
    sendMineStart(position) {
        return this.send({
            type: MESSAGE_TYPES.CLIENT_MINE_START,
            payload: { position }
        });
    }
    
    sendMineStop() {
        return this.send({
            type: MESSAGE_TYPES.CLIENT_MINE_STOP,
            payload: {}
        });
    }
    
    sendPlaceBlock(position, blockType) {
        return this.send({
            type: MESSAGE_TYPES.CLIENT_PLACE_BLOCK,
            payload: { position, blockType }
        });
    }
    
    sendChat(message) {
        return this.send({
            type: MESSAGE_TYPES.CLIENT_CHAT,
            payload: { message }
        });
    }
    
    // Gestion de la reconnexion
    attemptReconnect() {
        if (this.isReconnecting) return;
        
        this.isReconnecting = true;
        this.reconnectAttempts++;
        
        console.log(`🔄 Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        
        setTimeout(() => {
            if (this.reconnectAttempts <= this.maxReconnectAttempts) {
                this.connect();
            } else {
                console.error('❌ Nombre maximum de tentatives de reconnexion atteint');
                this.isReconnecting = false;
                
                if (this.onError) {
                    this.onError(new Error('Impossible de se reconnecter au serveur'));
                }
            }
        }, this.reconnectDelay * this.reconnectAttempts);
    }
    
    handleConnectionError(error) {
        this.isConnected = false;
        
        if (this.onError) {
            this.onError(error);
        }
    }
    
    flushMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message);
        }
    }
    
    startPing() {
        // Envoyer un ping toutes les 30 secondes
        this.pingInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({
                    type: 'client:ping',
                    payload: { timestamp: Date.now() }
                });
            }
        }, 30000);
    }
    
    stopPing() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
        }
    }
    
    // Méthodes publiques
    disconnect() {
        console.log('🔌 Déconnexion manuelle');
        this.isReconnecting = false;
        this.reconnectAttempts = this.maxReconnectAttempts; // Empêcher la reconnexion auto
        
        this.stopPing();
        
        if (this.ws) {
            this.ws.close(1000, 'Déconnexion manuelle');
        }
    }
    
    getConnectionState() {
        if (!this.ws) return 'disconnected';
        
        switch (this.ws.readyState) {
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return 'connected';
            case WebSocket.CLOSING:
                return 'closing';
            case WebSocket.CLOSED:
                return 'closed';
            default:
                return 'unknown';
        }
    }
    
    getStats() {
        return {
            ...this.stats,
            connectionState: this.getConnectionState(),
            reconnectAttempts: this.reconnectAttempts,
            queuedMessages: this.messageQueue.length
        };
    }
    
    // Méthodes de debug
    enableDebugMode() {
        this.debugMode = true;
        
        // Logger tous les messages
        const originalSend = this.send.bind(this);
        this.send = (message) => {
            console.log('📤 Envoi:', message);
            return originalSend(message);
        };
        
        const originalHandleMessage = this.handleMessage.bind(this);
        this.handleMessage = (message) => {
            console.log('📥 Réception:', message);
            return originalHandleMessage(message);
        };
    }
    
    disableDebugMode() {
        this.debugMode = false;
        // Restaurer les méthodes originales si nécessaire
    }
}
