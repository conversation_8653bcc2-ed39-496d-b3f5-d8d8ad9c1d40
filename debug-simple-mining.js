// Simple mining debug - to be executed in the console
console.log('🔧 Simple Mining Debug - Loaded');

// Function to check global objects
window.checkGlobals = function() {
    console.log('--- Checking global objects ---');
    console.log('window.world:', !!window.world);
    console.log('window.player:', !!window.player);
    console.log('window.controls:', !!window.controls);
    console.log('---------------------------------');
};

// Function to check chunks
window.checkChunks = function() {
    console.log('--- Checking chunks ---');
    if (window.world && window.world.chunks) {
        console.log('Total chunks:', window.world.chunks.size);
        let visibleMeshes = 0;
        window.world.chunks.forEach(chunk => {
            if (chunk.mesh && chunk.mesh.visible) {
                visibleMeshes++;
            }
        });
        console.log('Visible chunk meshes:', visibleMeshes);
    } else {
        console.log('World or chunks not available.');
    }
    console.log('-------------------------');
};

// Function to test raycast
window.testRaycast = function() {
    console.log('--- Testing raycast ---');
    if (window.player && window.world) {
        const direction = window.player.camera.getWorldDirection(new THREE.Vector3());
        window.player.raycaster.set(window.player.camera.position, direction);
        
        const meshes = [];
        window.world.chunks.forEach(chunk => {
            if (chunk.mesh) meshes.push(chunk.mesh);
        });
        
        const intersects = window.player.raycaster.intersectObjects(meshes, true);
        
        if (intersects.length > 0) {
            console.log('Intersection found at:', intersects[0].distance.toFixed(2));
            console.log('Intersection point:', intersects[0].point);
        } else {
            console.log('No intersection found.');
        }
    } else {
        console.log('Player or world not available.');
    }
    console.log('-------------------------');
};

// Function to simulate a mining click
window.simulateClick = function() {
    console.log('--- Simulating click ---');
    if (window.player) {
        // Simulate a mousedown event
        const downEvent = new MouseEvent('mousedown', { button: 0, bubbles: true });
        document.dispatchEvent(downEvent);
        console.log('Mousedown event dispatched.');

        // Simulate a mouseup event after a short delay
        setTimeout(() => {
            const upEvent = new MouseEvent('mouseup', { button: 0, bubbles: true });
            document.dispatchEvent(upEvent);
            console.log('Mouseup event dispatched.');
        }, 200); // 200ms delay
    } else {
        console.log('Player not available.');
    }
    console.log('--------------------------');
};

// Automatically display available functions
setTimeout(() => {
    console.log('--- Available debug functions ---');
    console.log('checkGlobals() - Checks for world, player, controls.');
    console.log('checkChunks() - Displays chunk information.');
    console.log('testRaycast() - Performs a raycast from the player camera.');
    console.log('simulateClick() - Simulates a mouse click for mining.');
    console.log('---------------------------------');
}, 500);
