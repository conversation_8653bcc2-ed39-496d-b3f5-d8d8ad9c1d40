// client/js/player/Inventory.js
// Gestionnaire d'inventaire côté client
import { BLOCK_TYPES } from '../../../shared/constants.js';

export class Inventory {
    constructor() {
        this.slots = new Array(36).fill(null); // 36 emplacements (9 hotbar + 27 inventaire)
        this.selectedSlot = 0; // Emplacement sélectionné dans la hotbar
        this.isOpen = false;
        
        // Éléments DOM
        this.hotbarElement = null;
        this.inventoryElement = null;
        
        // Configuration
        this.maxStackSize = 64;
        
        this.initializeElements();
        this.setupEventListeners();
        
        // Ajouter quelques items de test
        this.addTestItems();
        
        console.log('🎒 Inventory initialisé');
    }
    
    initializeElements() {
        this.hotbarElement = document.getElementById('hotbar');
        
        if (!this.hotbarElement) {
            console.warn('⚠️ Élément hotbar non trouvé');
            return;
        }
        
        // Créer les slots de la hotbar s'ils n'existent pas
        if (this.hotbarElement.children.length === 0) {
            for (let i = 0; i < 9; i++) {
                const slot = document.createElement('div');
                slot.className = 'hotbar-slot';
                slot.dataset.slot = i.toString();
                if (i === 0) slot.classList.add('selected');
                this.hotbarElement.appendChild(slot);
            }
        }
    }
    
    setupEventListeners() {
        // Sélection avec les touches numériques
        document.addEventListener('keydown', (e) => {
            if (e.code >= 'Digit1' && e.code <= 'Digit9') {
                const slot = parseInt(e.code.replace('Digit', '')) - 1;
                this.selectSlot(slot);
            }
        });
        
        // Molette de la souris pour changer d'emplacement
        document.addEventListener('wheel', (e) => {
            if (document.pointerLockElement) {
                e.preventDefault();
                const direction = e.deltaY > 0 ? 1 : -1;
                this.selectSlot((this.selectedSlot + direction + 9) % 9);
            }
        });
        
        // Clic sur les slots de la hotbar
        if (this.hotbarElement) {
            this.hotbarElement.addEventListener('click', (e) => {
                const slot = e.target.closest('.hotbar-slot');
                if (slot) {
                    const slotIndex = parseInt(slot.dataset.slot);
                    this.selectSlot(slotIndex);
                }
            });
        }
    }
    
    addTestItems() {
        // Ajouter quelques items de test pour la démonstration
        this.addItem(BLOCK_TYPES.STONE, 64);
        this.addItem(BLOCK_TYPES.DIRT, 32);
        this.addItem(BLOCK_TYPES.WOOD, 16);
        this.addItem(BLOCK_TYPES.COAL_ORE, 8);
        this.addItem(BLOCK_TYPES.IRON_ORE, 4);
    }
    
    // Ajouter un item à l'inventaire
    addItem(itemType, quantity = 1) {
        let remainingQuantity = quantity;
        
        // D'abord, essayer de remplir les stacks existants
        for (let i = 0; i < this.slots.length && remainingQuantity > 0; i++) {
            const slot = this.slots[i];
            if (slot && slot.type === itemType && slot.quantity < this.maxStackSize) {
                const canAdd = Math.min(remainingQuantity, this.maxStackSize - slot.quantity);
                slot.quantity += canAdd;
                remainingQuantity -= canAdd;
            }
        }
        
        // Ensuite, créer de nouveaux stacks dans les emplacements vides
        for (let i = 0; i < this.slots.length && remainingQuantity > 0; i++) {
            if (!this.slots[i]) {
                const stackSize = Math.min(remainingQuantity, this.maxStackSize);
                this.slots[i] = {
                    type: itemType,
                    quantity: stackSize
                };
                remainingQuantity -= stackSize;
            }
        }
        
        // Mettre à jour l'affichage
        this.updateDisplay();
        
        // Retourner la quantité qui n'a pas pu être ajoutée
        return remainingQuantity;
    }
    
    // Supprimer un item de l'inventaire
    removeItem(itemType, quantity = 1) {
        let remainingToRemove = quantity;
        
        // Supprimer des stacks existants
        for (let i = this.slots.length - 1; i >= 0 && remainingToRemove > 0; i--) {
            const slot = this.slots[i];
            if (slot && slot.type === itemType) {
                const canRemove = Math.min(remainingToRemove, slot.quantity);
                slot.quantity -= canRemove;
                remainingToRemove -= canRemove;
                
                // Supprimer le slot s'il est vide
                if (slot.quantity <= 0) {
                    this.slots[i] = null;
                }
            }
        }
        
        // Mettre à jour l'affichage
        this.updateDisplay();
        
        // Retourner la quantité effectivement supprimée
        return quantity - remainingToRemove;
    }
    
    // Obtenir l'item dans l'emplacement sélectionné
    getSelectedItem() {
        return this.slots[this.selectedSlot];
    }
    
    // Sélectionner un emplacement de la hotbar
    selectSlot(slotIndex) {
        if (slotIndex < 0 || slotIndex >= 9) return;
        
        this.selectedSlot = slotIndex;
        this.updateHotbarSelection();
        
        console.log(`🎒 Emplacement sélectionné: ${slotIndex}`, this.getSelectedItem());
    }
    
    // Mettre à jour la sélection visuelle de la hotbar
    updateHotbarSelection() {
        if (!this.hotbarElement) return;
        
        const slots = this.hotbarElement.querySelectorAll('.hotbar-slot');
        slots.forEach((slot, index) => {
            if (index === this.selectedSlot) {
                slot.classList.add('selected');
            } else {
                slot.classList.remove('selected');
            }
        });
    }
    
    // Mettre à jour l'affichage de l'inventaire
    updateDisplay() {
        this.updateHotbar();
        // TODO: Mettre à jour l'inventaire complet quand il sera implémenté
    }
    
    updateHotbar() {
        if (!this.hotbarElement) return;
        
        const slots = this.hotbarElement.querySelectorAll('.hotbar-slot');
        
        for (let i = 0; i < 9; i++) {
            const slotElement = slots[i];
            const item = this.slots[i];
            
            if (item) {
                slotElement.innerHTML = `
                    <div class="item-icon" style="background-color: ${this.getItemColor(item.type)}">
                        <div class="item-symbol">${this.getItemSymbol(item.type)}</div>
                    </div>
                    <div class="item-count">${item.quantity > 1 ? item.quantity : ''}</div>
                `;
                slotElement.classList.add('has-item');
            } else {
                slotElement.innerHTML = '';
                slotElement.classList.remove('has-item');
            }
        }
    }
    
    getItemColor(itemType) {
        // Couleurs pour les différents types d'items
        const colors = {
            [BLOCK_TYPES.STONE]: '#808080',
            [BLOCK_TYPES.DIRT]: '#8B4513',
            [BLOCK_TYPES.GRASS]: '#228B22',
            [BLOCK_TYPES.WOOD]: '#8B4513',
            [BLOCK_TYPES.LEAVES]: '#228B22',
            [BLOCK_TYPES.SAND]: '#F4A460',
            [BLOCK_TYPES.WATER]: '#4169E1',
            [BLOCK_TYPES.COAL_ORE]: '#2F2F2F',
            [BLOCK_TYPES.IRON_ORE]: '#CD853F',
            [BLOCK_TYPES.GOLD_ORE]: '#FFD700',
            [BLOCK_TYPES.DIAMOND_ORE]: '#00FFFF',
            [BLOCK_TYPES.BEDROCK]: '#2F2F2F'
        };
        
        return colors[itemType] || '#666666';
    }
    
    getItemSymbol(itemType) {
        // Symboles pour les différents types d'items
        const symbols = {
            [BLOCK_TYPES.STONE]: '🪨',
            [BLOCK_TYPES.DIRT]: '🟫',
            [BLOCK_TYPES.GRASS]: '🟩',
            [BLOCK_TYPES.WOOD]: '🪵',
            [BLOCK_TYPES.LEAVES]: '🍃',
            [BLOCK_TYPES.SAND]: '🟨',
            [BLOCK_TYPES.WATER]: '💧',
            [BLOCK_TYPES.COAL_ORE]: '⚫',
            [BLOCK_TYPES.IRON_ORE]: '🔘',
            [BLOCK_TYPES.GOLD_ORE]: '🟡',
            [BLOCK_TYPES.DIAMOND_ORE]: '💎',
            [BLOCK_TYPES.BEDROCK]: '⬛'
        };
        
        return symbols[itemType] || '❓';
    }
    
    // Vérifier si l'inventaire contient un item
    hasItem(itemType, quantity = 1) {
        let totalQuantity = 0;
        
        for (const slot of this.slots) {
            if (slot && slot.type === itemType) {
                totalQuantity += slot.quantity;
                if (totalQuantity >= quantity) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // Obtenir la quantité totale d'un item
    getItemCount(itemType) {
        let totalQuantity = 0;
        
        for (const slot of this.slots) {
            if (slot && slot.type === itemType) {
                totalQuantity += slot.quantity;
            }
        }
        
        return totalQuantity;
    }
    
    // Vider l'inventaire
    clear() {
        this.slots.fill(null);
        this.updateDisplay();
        console.log('🎒 Inventaire vidé');
    }
    
    // Obtenir l'état de l'inventaire pour la synchronisation
    getState() {
        return {
            slots: this.slots.map(slot => slot ? { ...slot } : null),
            selectedSlot: this.selectedSlot
        };
    }
    
    // Restaurer l'état de l'inventaire depuis le serveur
    setState(state) {
        if (state.slots) {
            this.slots = state.slots.map(slot => slot ? { ...slot } : null);
        }
        if (state.selectedSlot !== undefined) {
            this.selectedSlot = state.selectedSlot;
        }
        
        this.updateDisplay();
        this.updateHotbarSelection();
    }
    
    // Échanger deux emplacements
    swapSlots(slotA, slotB) {
        if (slotA < 0 || slotA >= this.slots.length || 
            slotB < 0 || slotB >= this.slots.length) {
            return false;
        }
        
        const temp = this.slots[slotA];
        this.slots[slotA] = this.slots[slotB];
        this.slots[slotB] = temp;
        
        this.updateDisplay();
        return true;
    }
    
    // Obtenir les emplacements libres
    getFreeSlots() {
        const freeSlots = [];
        for (let i = 0; i < this.slots.length; i++) {
            if (!this.slots[i]) {
                freeSlots.push(i);
            }
        }
        return freeSlots;
    }
    
    // Vérifier si l'inventaire est plein
    isFull() {
        return this.getFreeSlots().length === 0;
    }
    
    // Obtenir des informations de debug
    getDebugInfo() {
        const itemCounts = {};
        let totalItems = 0;
        
        for (const slot of this.slots) {
            if (slot) {
                itemCounts[slot.type] = (itemCounts[slot.type] || 0) + slot.quantity;
                totalItems += slot.quantity;
            }
        }
        
        return {
            selectedSlot: this.selectedSlot,
            totalItems,
            uniqueItems: Object.keys(itemCounts).length,
            freeSlots: this.getFreeSlots().length,
            itemCounts
        };
    }
    
    // Nettoyage
    dispose() {
        console.log('🧹 Nettoyage Inventory...');
        
        // Nettoyer les event listeners
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('wheel', this.handleWheel);
        
        // Nettoyer les données
        this.slots = [];
    }
}
