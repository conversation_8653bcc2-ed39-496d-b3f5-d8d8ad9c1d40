# Regles de Projet JScraft

## Conventions de Codage

### Langue du Code
- **Code source**: OBLIGATOIREMENT en anglais
- **Variables**: noms en anglais uniquement
- **Fonctions**: noms en anglais uniquement
- **Classes**: noms en anglais uniquement
- **Commentaires de code**: en anglais

### Caracteres Speciaux
- **INTERDICTION ABSOLUE** de caracteres speciaux dans le code source
- Pas d'accents, cedilles, ou caracteres non-ASCII dans:
  - Noms de variables
  - Noms de fonctions
  - Noms de classes
  - Commentaires de code
  - Strings de code (sauf necessite absolue)

### Documentation
- **Descriptions**: peuvent etre en francais
- **SANS caracteres speciaux** dans la documentation
- Utiliser des equivalents sans accents:
  - "a" au lieu de "à"
  - "e" au lieu de "é", "è", "ê"
  - "c" au lieu de "ç"
  - "u" au lieu de "ù"

## Exemples Conformes

### ✅ CORRECT
```javascript
// Player movement system
class PlayerController {
    constructor() {
        this.isMoving = false;
        this.speed = 5;
    }
    
    // Move player to target position
    moveToPosition(x, y) {
        // Implementation here
    }
}
```

### ❌ INCORRECT
```javascript
// Système de déplacement du joueur
class ContrôleurJoueur {
    constructor() {
        this.estEnMouvement = false;
        this.vitesse = 5;
    }
    
    // Déplacer le joueur vers la position cible
    déplacerVersPosition(x, y) {
        // Implémentation ici
    }
}
```

## Regles Specifiques

### Nommage
- **camelCase** pour variables et fonctions
- **PascalCase** pour classes
- **UPPER_SNAKE_CASE** pour constantes
- Noms descriptifs en anglais

### Commentaires
- Commentaires de code en anglais
- Documentation utilisateur peut etre en francais (sans accents)
- Pas de caracteres speciaux dans les commentaires

### Strings et Messages
- Messages utilisateur: francais sans accents autorise
- Strings techniques: anglais uniquement
- Pas de caracteres speciaux sauf necessite absolue

### Exceptions
- Caracteres speciaux autorises uniquement pour:
  - Contenu utilisateur final (affichage)
  - Donnees externes (APIs, fichiers)
  - Cas ou la specification l'exige

## Verification

### Avant Commit
- Verifier absence de caracteres speciaux dans le code
- Valider que tous les noms sont en anglais
- Controler la documentation (francais sans accents)

### Outils
- Utiliser des linters pour detecter les caracteres non-ASCII
- Validation automatique des conventions de nommage

## Encodage et Compatibilite

### Encodage des Fichiers
- **OBLIGATOIRE**: Tous les fichiers doivent etre encodes en UTF-8 sans BOM
- **Scripts PowerShell**: Utiliser uniquement des caracteres ASCII (7-bit)
- **Pas d'emojis** dans les scripts ou le code source
- **Pas de caracteres Unicode** dans les noms de fichiers

### Gestion des Scripts
- **PowerShell**: Eviter absolument les caracteres non-ASCII
- **Batch**: Utiliser uniquement des caracteres ASCII
- **Messages utilisateur**: Francais sans accents dans les scripts
- **Commentaires techniques**: Anglais uniquement

### Problemes d'Encodage a Eviter
- Caracteres corrompus (ðŸ„, Ã©, etc.)
- Emojis dans les scripts systeme
- Accents dans les messages PowerShell
- Caracteres speciaux dans les chemins

### Verification d'Encodage
- Tester les scripts sur differents systemes
- Valider l'affichage correct des messages
- Verifier la compatibilite cross-platform
- Utiliser des outils de validation d'encodage

## Objectifs
- **Compatibilite**: eviter les problemes d'encodage
- **Lisibilite**: code uniforme et comprehensible
- **Maintenance**: faciliter la collaboration internationale
- **Performance**: eviter les problemes de charset
- **Robustesse**: scripts fonctionnels sur tous les environnements

---

*Ces regles garantissent un code propre, maintenable et compatible avec tous les environnements de developpement.*