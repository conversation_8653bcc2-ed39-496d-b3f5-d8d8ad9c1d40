// server/utils/WorldGenerator.js
// Générateur de monde côté serveur - adapté du client existant
import { SimplexNoise } from './SimplexNoise.js';
import { BLOCK_TYPES, BIOMES } from '../../shared/constants.js';

export class WorldGenerator {
    constructor(seed = Math.random() * 10000) {
        this.seed = seed;
        
        // Générateurs de bruit pour différentes couches de terrain
        this.baseNoise = new SimplexNoise(seed);
        this.detailNoise = new SimplexNoise(seed + 1000);
        this.biomeNoise = new SimplexNoise(seed + 2000);
        this.mountainNoise = new SimplexNoise(seed + 3000);
        this.caveNoise = new SimplexNoise(seed + 4000);
        this.oreNoise = new SimplexNoise(seed + 5000);
        
        // Paramètres de terrain améliorés
        this.baseAmplitude = 25;   // Augmenté pour plus de relief
        this.detailAmplitude = 4;  // Plus de variation
        this.mountainAmplitude = 40; // Pour les zones montagneuses
        this.waterLevel = 60;      // Niveau d'eau fixe
        
        // Échelles de bruit
        this.baseScale = 0.004;    // Terrain de base
        this.detailScale = 0.025;  // Détails fins
        this.biomeScale = 0.0015;  // Biomes plus larges
        this.mountainScale = 0.002; // Montagnes
        this.caveScale = 0.05;     // Grottes
        this.oreScale = 0.1;       // Minerais
        
        // Définition des biomes (adaptée des constantes partagées)
        this.biomes = {
            OCEAN: { min: 0.0, max: 0.15, name: 'Ocean', id: BIOMES.OCEAN.id },
            BEACH: { min: 0.15, max: 0.25, name: 'Beach', id: BIOMES.PLAINS.id }, // Pas de biome beach séparé
            PLAINS: { min: 0.25, max: 0.45, name: 'Plains', id: BIOMES.PLAINS.id },
            FOREST: { min: 0.45, max: 0.65, name: 'Forest', id: BIOMES.FOREST.id },
            HILLS: { min: 0.65, max: 0.8, name: 'Hills', id: BIOMES.MOUNTAINS.id },
            MOUNTAINS: { min: 0.8, max: 1.0, name: 'Mountains', id: BIOMES.MOUNTAINS.id }
        };
        
        console.log(`🌱 WorldGenerator initialisé avec seed: ${seed}`);
    }

    // Hauteur de base du terrain - Calcul amélioré avec biomes
    getHeight(x, z) {
        const biomeValue = this.getBiomeValue(x, z);
        const biome = this.getBiome(biomeValue);
        
        // Bruit de base
        const baseNoise = this.baseNoise.noise2D(x * this.baseScale, z * this.baseScale);
        
        // Modifier la hauteur selon le biome
        let baseHeight = this.waterLevel;
        let amplitude = this.baseAmplitude;
        
        switch (biome.name) {
            case 'Ocean':
                baseHeight = this.waterLevel - 15;
                amplitude = 8;
                break;
            case 'Beach':
                baseHeight = this.waterLevel - 2;
                amplitude = 3;
                break;
            case 'Plains':
                baseHeight = this.waterLevel + 5;
                amplitude = 12;
                break;
            case 'Forest':
                baseHeight = this.waterLevel + 8;
                amplitude = 18;
                break;
            case 'Hills':
                baseHeight = this.waterLevel + 15;
                amplitude = 25;
                break;
            case 'Mountains':
                baseHeight = this.waterLevel + 25;
                amplitude = this.mountainAmplitude;
                // Ajouter du bruit de montagne pour plus de relief
                const mountainNoise = this.mountainNoise.noise2D(x * this.mountainScale, z * this.mountainScale);
                baseHeight += mountainNoise * 20;
                break;
        }
        
        const finalHeight = baseHeight + (baseNoise * amplitude);
        return Math.floor(Math.max(10, finalHeight)); // Hauteur minimum de 10
    }
    
    // Variations de détail pour le terrain
    getDetailNoise(x, z) {
        return this.detailNoise.noise2D(x * this.detailScale, z * this.detailScale) * this.detailAmplitude;
    }
    
    // Valeur de biome (0-1) pour déterminer le type de terrain
    getBiomeValue(x, z) {
        return (this.biomeNoise.noise2D(x * this.biomeScale, z * this.biomeScale) + 1) * 0.5;
    }
    
    // Obtenir le biome à partir d'une valeur
    getBiome(biomeValue) {
        for (const biome of Object.values(this.biomes)) {
            if (biomeValue >= biome.min && biomeValue < biome.max) {
                return biome;
            }
        }
        return this.biomes.PLAINS; // Biome par défaut
    }
    
    // Vérifier s'il doit y avoir une grotte à cette position
    shouldHaveCave(x, y, z) {
        // Limiter les grottes à certaines profondeurs pour éviter les trous en surface
        if (y > 35 || y < 10) return false;
        
        const terrainHeight = this.getHeight(x, z);
        if (y > terrainHeight - 8) return false; // Pas de grottes près de la surface
        
        const caveNoise = this.caveNoise.noise3D(x * this.caveScale, y * this.caveScale, z * this.caveScale);
        return caveNoise > 0.7; // Seuil plus élevé pour moins de grottes
    }
    
    // Obtenir le type de minerai à une position donnée
    getOreType(x, y, z) {
        if (y > 40) return null; // Pas de minerais près de la surface
        
        const oreNoise = this.oreNoise.noise3D(x * this.oreScale, y * this.oreScale, z * this.oreScale);
        
        // Distribution des minerais par profondeur
        if (y < 12) {
            // Zone profonde - diamant et or
            if (oreNoise > 0.85) return BLOCK_TYPES.DIAMOND_ORE;
            if (oreNoise > 0.75) return BLOCK_TYPES.GOLD_ORE;
        }
        
        if (y < 25) {
            // Zone moyenne - fer et or
            if (oreNoise > 0.8) return BLOCK_TYPES.IRON_ORE;
            if (oreNoise > 0.7) return BLOCK_TYPES.GOLD_ORE;
        }
        
        if (y < 40) {
            // Zone haute - charbon et fer
            if (oreNoise > 0.75) return BLOCK_TYPES.COAL_ORE;
            if (oreNoise > 0.85) return BLOCK_TYPES.IRON_ORE;
        }
        
        return null;
    }
    
    // Déterminer le type d'arbre selon le biome
    getTreeType(biomeValue) {
        const biome = this.getBiome(biomeValue);
        
        switch (biome.name) {
            case 'Forest':
                return Math.random() < 0.7 ? 'OAK' : 'BIRCH';
            case 'Hills':
                return Math.random() < 0.8 ? 'PINE' : 'OAK';
            case 'Mountains':
                return 'PINE';
            default:
                return 'OAK';
        }
    }
    
    // Générer le type de bloc à une position donnée
    getBlockType(x, y, z) {
        // Bedrock au fond
        if (y <= 0) return BLOCK_TYPES.BEDROCK;
        
        // Obtenir la hauteur du terrain
        const terrainHeight = this.getHeight(x, z);
        const biomeValue = this.getBiomeValue(x, z);
        const biome = this.getBiome(biomeValue);
        
        // Air au-dessus du terrain
        if (y > terrainHeight) {
            // Eau dans les océans
            if (biome.name === 'Ocean' && y <= this.waterLevel && terrainHeight < this.waterLevel - 5) {
                return BLOCK_TYPES.WATER;
            }
            return BLOCK_TYPES.AIR;
        }
        
        // Vérifier les grottes
        if (this.shouldHaveCave(x, y, z)) {
            return BLOCK_TYPES.AIR;
        }
        
        // Vérifier les minerais
        const oreType = this.getOreType(x, y, z);
        if (oreType) {
            return oreType;
        }
        
        // Blocs de terrain selon la profondeur et le biome
        const depthFromSurface = terrainHeight - y;
        
        if (depthFromSurface === 0) {
            // Surface
            switch (biome.name) {
                case 'Ocean':
                case 'Beach':
                    return BLOCK_TYPES.SAND;
                case 'Desert':
                    return BLOCK_TYPES.SAND;
                case 'Tundra':
                    return BLOCK_TYPES.SNOW;
                default:
                    return BLOCK_TYPES.GRASS;
            }
        } else if (depthFromSurface <= 3) {
            // Couche de terre
            switch (biome.name) {
                case 'Ocean':
                case 'Beach':
                case 'Desert':
                    return BLOCK_TYPES.SAND;
                case 'Tundra':
                    return BLOCK_TYPES.DIRT; // Terre gelée
                default:
                    return BLOCK_TYPES.DIRT;
            }
        } else if (depthFromSurface <= 8) {
            // Transition terre/pierre
            return Math.random() < 0.3 ? BLOCK_TYPES.DIRT : BLOCK_TYPES.STONE;
        } else {
            // Pierre profonde
            return BLOCK_TYPES.STONE;
        }
    }
    
    // Générer la végétation de surface pour un chunk
    generateSurfaceFeatures(chunkX, chunkZ, blocks, getIndex) {
        for (let x = 0; x < 16; x++) {
            for (let z = 0; z < 16; z++) {
                const worldX = chunkX * 16 + x;
                const worldZ = chunkZ * 16 + z;
                
                const height = this.getHeight(worldX, worldZ);
                const biomeValue = this.getBiomeValue(worldX, worldZ);
                const biome = this.getBiome(biomeValue);
                
                // Vérifier que la surface est de l'herbe
                if (height < 128 && blocks[getIndex(x, height, z)] === BLOCK_TYPES.GRASS) {
                    this.generateVegetation(x, height, z, biome, blocks, getIndex);
                }
            }
        }
    }
    
    generateVegetation(x, height, z, biome, blocks, getIndex) {
        switch (biome.name) {
            case 'Plains':
                // Herbe haute et fleurs occasionnelles
                if (Math.random() < 0.3) {
                    blocks[getIndex(x, height + 1, z)] = BLOCK_TYPES.TALL_GRASS;
                }
                if (Math.random() < 0.05) {
                    blocks[getIndex(x, height + 1, z)] = BLOCK_TYPES.FLOWERS;
                }
                // Arbres rares
                if (Math.random() < 0.01) {
                    this.generateTree(x, height + 1, z, 'OAK', blocks, getIndex);
                }
                break;
                
            case 'Forest':
                // Beaucoup d'arbres
                if (Math.random() < 0.15) {
                    const treeType = this.getTreeType(this.getBiomeValue(x, z));
                    this.generateTree(x, height + 1, z, treeType, blocks, getIndex);
                }
                // Champignons sous les arbres
                if (Math.random() < 0.02) {
                    blocks[getIndex(x, height + 1, z)] = BLOCK_TYPES.MUSHROOM;
                }
                break;
                
            case 'Desert':
                // Cactus occasionnels
                if (Math.random() < 0.02) {
                    this.generateCactus(x, height + 1, z, blocks, getIndex);
                }
                break;
        }
    }
    
    generateTree(x, y, z, treeType, blocks, getIndex) {
        if (x < 2 || x >= 14 || z < 2 || z >= 14 || y + 6 >= 128) return;
        
        const trunkHeight = 4 + Math.floor(Math.random() * 3);
        
        // Tronc
        for (let i = 0; i < trunkHeight; i++) {
            blocks[getIndex(x, y + i, z)] = BLOCK_TYPES.WOOD;
        }
        
        // Feuilles
        const leafY = y + trunkHeight;
        for (let dx = -2; dx <= 2; dx++) {
            for (let dz = -2; dz <= 2; dz++) {
                for (let dy = 0; dy <= 2; dy++) {
                    const leafX = x + dx;
                    const leafZ = z + dz;
                    const currentY = leafY + dy;
                    
                    if (leafX >= 0 && leafX < 16 && leafZ >= 0 && leafZ < 16 && currentY < 128) {
                        const distance = Math.abs(dx) + Math.abs(dz) + Math.abs(dy);
                        if (distance <= 3 && Math.random() < 0.8) {
                            blocks[getIndex(leafX, currentY, leafZ)] = BLOCK_TYPES.LEAVES;
                        }
                    }
                }
            }
        }
    }
    
    generateCactus(x, y, z, blocks, getIndex) {
        if (x < 0 || x >= 16 || z < 0 || z >= 16 || y + 3 >= 128) return;
        
        const height = 2 + Math.floor(Math.random() * 2);
        for (let i = 0; i < height; i++) {
            blocks[getIndex(x, y + i, z)] = BLOCK_TYPES.CACTUS;
        }
    }
}
