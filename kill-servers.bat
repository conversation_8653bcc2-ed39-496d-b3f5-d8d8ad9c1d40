@echo off
chcp 65001 >nul
echo.
echo 🔄 Arrêt de tous les serveurs Node.js et Python...
echo.

echo 📋 Recherche des processus Node.js...
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo ⏹️  Arrêt des processus Node.js...
    taskkill /F /IM node.exe /T 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Processus Node.js arrêtés avec succès
    ) else (
        echo ❌ Erreur lors de l'arrêt des processus Node.js
    )
) else (
    echo ℹ️  Aucun processus Node.js en cours d'exécution
)

echo.
echo 📋 Recherche des processus Python...
tasklist /FI "IMAGENAME eq python.exe" 2>nul | find /I "python.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo ⏹️  Arrêt des processus Python...
    taskkill /F /IM python.exe /T 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Processus Python arrêtés avec succès
    ) else (
        echo ❌ Erreur lors de l'arrêt des processus Python
    )
) else (
    echo ℹ️  Aucun processus Python en cours d'exécution
)

echo.
echo 📋 Recherche des processus Python3...
tasklist /FI "IMAGENAME eq python3.exe" 2>nul | find /I "python3.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo ⏹️  Arrêt des processus Python3...
    taskkill /F /IM python3.exe /T 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Processus Python3 arrêtés avec succès
    ) else (
        echo ❌ Erreur lors de l'arrêt des processus Python3
    )
) else (
    echo ℹ️  Aucun processus Python3 en cours d'exécution
)

echo.
echo 🔍 Vérification finale...
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo ⚠️  Des processus Node.js sont encore actifs
) else (
    echo ✅ Aucun processus Node.js actif
)

tasklist /FI "IMAGENAME eq python.exe" 2>nul | find /I "python.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo ⚠️  Des processus Python sont encore actifs
) else (
    echo ✅ Aucun processus Python actif
)

echo.
echo 🎉 Script terminé !
echo 💡 Conseil: Utilisez ce script avant de redémarrer vos serveurs de développement
echo.
echo Appuyez sur une touche pour continuer...
pause >nul