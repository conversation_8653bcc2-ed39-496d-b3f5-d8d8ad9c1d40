// client/js/main.js
// Point d'entrée du client - Architecture Client-Serveur
import { SocketClient } from './network/SocketClient.js';
import { Renderer } from './rendering/Renderer.js';
import { PlayerController } from './player/PlayerController.js';
import { ClientWorld } from './world/ClientWorld.js';
import { OptionsManager } from './ui/OptionsManager.js';
import { ChatManager } from './ui/ChatManager.js';
import { UIManager } from './ui/UIManager.js';

class Game {
    constructor() {
        this.isInitialized = false;
        this.isConnected = false;
        this.playerId = null;
        
        // Composants principaux
        this.renderer = null;
        this.clientWorld = null;
        this.playerController = null;
        this.socketClient = null;
        this.optionsManager = null;
        this.chatManager = null;
        this.uiManager = null;
        
        // État du jeu
        this.gameState = 'connecting'; // connecting, playing, paused
        this.lastFrameTime = performance.now();
        this.frameCount = 0;
        this.fps = 0;
        
        // Bind des méthodes
        this.animate = this.animate.bind(this);
        this.onWindowResize = this.onWindowResize.bind(this);
        this.onVisibilityChange = this.onVisibilityChange.bind(this);
        
        console.log('🎮 Game client initialisé');
    }
    
    async init() {
        try {
            console.log('🚀 Initialisation du client...');
            
            // Initialiser l'interface utilisateur
            this.uiManager = new UIManager();
            this.uiManager.showConnectionScreen();
            
            // Initialiser le gestionnaire d'options
            this.optionsManager = new OptionsManager();
            await this.optionsManager.init();
            
            // Initialiser le renderer
            this.renderer = new Renderer();
            await this.renderer.init();
            
            // Initialiser le monde client
            this.clientWorld = new ClientWorld(this.renderer.scene);
            
            // Initialiser le contrôleur de joueur
            this.playerController = new PlayerController();
            
            // Initialiser le chat
            this.chatManager = new ChatManager();
            
            // Connecter au serveur
            await this.connectToServer();
            
            // Configurer les événements
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ Client initialisé avec succès');
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation:', error);
            this.uiManager.showConnectionError(error.message);
        }
    }
    
    async connectToServer() {
        const serverAddress = this.optionsManager.getOption('serverAddress') || 'localhost:3000';
        const wsUrl = `ws://${serverAddress}/ws`;
        
        console.log(`🌐 Connexion au serveur: ${wsUrl}`);
        
        this.socketClient = new SocketClient(wsUrl, this);
        
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Timeout de connexion'));
            }, 10000);
            
            this.socketClient.onConnect = () => {
                clearTimeout(timeout);
                this.isConnected = true;
                this.gameState = 'playing';
                this.uiManager.hideConnectionScreen();
                this.uiManager.showInstructions();
                console.log('✅ Connecté au serveur');
                resolve();
            };
            
            this.socketClient.onDisconnect = () => {
                this.isConnected = false;
                this.gameState = 'connecting';
                this.uiManager.showConnectionScreen();
                console.log('❌ Déconnecté du serveur');
            };
            
            this.socketClient.onError = (error) => {
                clearTimeout(timeout);
                reject(error);
            };
        });
    }
    
    setupEventListeners() {
        // Redimensionnement de la fenêtre
        window.addEventListener('resize', this.onWindowResize);
        
        // Changement de visibilité de la page
        document.addEventListener('visibilitychange', this.onVisibilityChange);
        
        // Gestion des erreurs globales
        window.addEventListener('error', (event) => {
            console.error('❌ Erreur globale:', event.error);
        });
        
        // Gestion des promesses rejetées
        window.addEventListener('unhandledrejection', (event) => {
            console.error('❌ Promesse rejetée:', event.reason);
        });
        
        // Bouton de reconnexion
        const retryButton = document.getElementById('retry-connection');
        if (retryButton) {
            retryButton.addEventListener('click', () => {
                this.reconnect();
            });
        }
        
        // Bouton de reconnexion dans les options
        const reconnectButton = document.getElementById('reconnect-button');
        if (reconnectButton) {
            reconnectButton.addEventListener('click', () => {
                this.reconnect();
            });
        }
    }
    
    async reconnect() {
        console.log('🔄 Tentative de reconnexion...');
        
        if (this.socketClient) {
            this.socketClient.disconnect();
        }
        
        this.isConnected = false;
        this.gameState = 'connecting';
        this.uiManager.showConnectionScreen();
        
        try {
            await this.connectToServer();
        } catch (error) {
            console.error('❌ Échec de la reconnexion:', error);
            this.uiManager.showConnectionError(error.message);
        }
    }
    
    start() {
        if (!this.isInitialized) {
            console.error('❌ Le jeu n\'est pas initialisé');
            return;
        }
        
        console.log('🎮 Démarrage de la boucle de jeu');
        this.animate();
    }
    
    // Boucle de rendu principale du client
    animate() {
        requestAnimationFrame(this.animate);
        
        const currentTime = performance.now();
        const deltaTime = (currentTime - this.lastFrameTime) / 1000;
        this.lastFrameTime = currentTime;
        
        // Calculer les FPS
        this.frameCount++;
        if (this.frameCount % 60 === 0) {
            this.fps = Math.round(1 / deltaTime);
            this.uiManager.updateFPS(this.fps);
        }
        
        // Ne pas mettre à jour si le jeu n'est pas en cours
        if (this.gameState !== 'playing' || !this.isConnected) {
            return;
        }
        
        try {
            // 1. Mettre à jour le contrôleur (qui envoie les entrées au serveur)
            const inputPayload = this.playerController.update(deltaTime);
            if (inputPayload && this.socketClient) {
                this.socketClient.sendInput(inputPayload);
            }
            
            // 2. Mettre à jour le monde client (interpolation, etc.)
            this.clientWorld.update(deltaTime);
            
            // 3. Mettre à jour l'interface utilisateur
            this.updateUI();
            
            // 4. Rendre la scène
            const scene = this.renderer.getScene();
            const camera = this.playerController.getCamera();

            if (!scene) {
                console.error('❌ Scène manquante');
                return;
            }

            if (!camera) {
                console.error('❌ Caméra manquante');
                return;
            }

            // Debug: Afficher les infos de rendu une fois
            if (!this.debugRendered) {
                console.log('🎨 Infos de rendu:');
                console.log('- Scène enfants:', scene.children.length);
                console.log('- Position caméra:', camera.position);
                console.log('- Rotation caméra:', camera.rotation);
                this.debugRendered = true;
            }

            this.renderer.render(scene, camera);
            
        } catch (error) {
            console.error('❌ Erreur dans la boucle de jeu:', error);
        }
    }
    
    updateUI() {
        // Mettre à jour la position du joueur
        const position = this.playerController.getPosition();
        this.uiManager.updatePosition(position);
        
        // Mettre à jour le nombre de chunks
        const chunkCount = this.clientWorld.getChunkCount();
        this.uiManager.updateChunkCount(chunkCount);
        
        // Mettre à jour le statut de connexion
        this.uiManager.updateConnectionStatus(this.isConnected);
        
        // Mettre à jour le nombre de joueurs
        const playerCount = this.clientWorld.getPlayerCount();
        this.uiManager.updatePlayerCount(playerCount);
    }
    
    // Appelé par SocketClient quand des données arrivent du serveur
    onServerMessage(message) {
        const { type, payload } = message;
        
        switch (type) {
            case 'server:init':
                this.handleServerInit(payload);
                break;
                
            case 'server:worldUpdate':
                this.handleWorldUpdate(payload);
                break;
                
            case 'server:chunkData':
                this.handleChunkData(payload);
                break;
                
            case 'server:blockUpdate':
                this.handleBlockUpdate(payload);
                break;
                
            case 'server:playerJoin':
                this.handlePlayerJoin(payload);
                break;
                
            case 'server:playerLeave':
                this.handlePlayerLeave(payload);
                break;
                
            case 'server:chat':
                this.handleChat(payload);
                break;
                
            case 'server:error':
                this.handleServerError(payload);
                break;
                
            default:
                console.warn('⚠️ Type de message serveur inconnu:', type);
        }
    }
    
    handleServerInit(payload) {
        this.playerId = payload.playerId;
        console.log(`👤 ID joueur reçu: ${this.playerId}`);
        
        // Configurer le contrôleur avec l'ID du joueur
        this.playerController.setPlayerId(this.playerId);
        
        // Configurer le monde avec la graine du serveur
        this.clientWorld.setSeed(payload.worldSeed);
    }
    
    handleWorldUpdate(payload) {
        this.clientWorld.handleServerUpdate(payload);
        this.playerController.handleServerUpdate(payload);
    }
    
    handleChunkData(payload) {
        this.clientWorld.handleChunkData(payload);
    }
    
    handleBlockUpdate(payload) {
        this.clientWorld.handleBlockUpdate(payload);
    }
    
    handlePlayerJoin(payload) {
        this.clientWorld.addPlayer(payload.playerId, payload.playerState);
        this.chatManager.addSystemMessage(`${payload.playerId} a rejoint le jeu`);
    }
    
    handlePlayerLeave(payload) {
        this.clientWorld.removePlayer(payload.playerId);
        this.chatManager.addSystemMessage(`${payload.playerId} a quitté le jeu`);
    }
    
    handleChat(payload) {
        this.chatManager.addMessage(payload.playerId, payload.message, payload.timestamp);
    }
    
    handleServerError(payload) {
        console.error('❌ Erreur serveur:', payload.error);
        this.uiManager.showNotification(`Erreur: ${payload.error}`, 'error');
    }
    
    // Gestionnaires d'événements
    onWindowResize() {
        if (this.renderer) {
            this.renderer.handleResize();
        }
        
        if (this.playerController) {
            this.playerController.handleResize();
        }
    }
    
    onVisibilityChange() {
        if (document.hidden) {
            // Page cachée - réduire l'activité
            this.gameState = 'paused';
        } else {
            // Page visible - reprendre l'activité
            if (this.isConnected) {
                this.gameState = 'playing';
            }
        }
    }
    
    // Méthodes publiques pour l'interface
    toggleOptions() {
        this.optionsManager.toggle();
    }
    
    toggleChat() {
        this.chatManager.toggle();
    }
    
    sendChatMessage(message) {
        if (this.socketClient && message.trim()) {
            this.socketClient.sendChat(message);
        }
    }
    
    // Nettoyage
    destroy() {
        console.log('🧹 Nettoyage du client...');
        
        if (this.socketClient) {
            this.socketClient.disconnect();
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        window.removeEventListener('resize', this.onWindowResize);
        document.removeEventListener('visibilitychange', this.onVisibilityChange);
    }
}

// Initialisation et démarrage du jeu
const game = new Game();

// Démarrer le jeu quand la page est chargée
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await game.init();
        game.start();
    } catch (error) {
        console.error('❌ Erreur fatale:', error);
    }
});

// Nettoyage avant fermeture
window.addEventListener('beforeunload', () => {
    game.destroy();
});

// Exposer le jeu globalement pour le debug
window.game = game;
