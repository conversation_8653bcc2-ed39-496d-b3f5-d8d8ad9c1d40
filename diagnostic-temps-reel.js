// Script de diagnostic en temps réel pour surveiller les corrections
console.log('🔍 Diagnostic en temps réel - Corrections de minage et physique');

class DiagnosticManager {
    constructor() {
        this.isRunning = false;
        this.interval = null;
        this.stats = {
            gravityEvents: 0,
            miningAttempts: 0,
            cameraRotations: 0,
            spawnAttempts: 0,
            lastGravityValue: 0,
            lastSensitivity: 0
        };
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // DÉSACTIVER la surveillance des logs pour éviter la boucle infinie
        // Le problème était que console.log se rappelait lui-même

        // Surveiller directement les événements de minage sans intercepter console.log
        
        // Surveiller les mouvements de souris
        document.addEventListener('mousemove', (e) => {
            if (document.pointerLockElement) {
                this.stats.cameraRotations++;
                this.checkMouseSensitivity(e);
            }
        });
        
        // Surveiller les clics
        document.addEventListener('mousedown', (e) => {
            if (e.button === 0 && document.pointerLockElement) {
                console.log('🖱️ DIAGNOSTIC: Clic de minage détecté', {
                    timestamp: Date.now(),
                    pointerLocked: !!document.pointerLockElement,
                    worldExists: !!window.world,
                    playerExists: !!window.player
                });
            }
        });
    }
    
    logMiningEvent(message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`⛏️ [${timestamp}] MINING EVENT:`, message);
    }
    
    extractGravityInfo(message) {
        // Extraire les valeurs de gravité des logs
        const gravityMatch = message.match(/gravity[:\s]*([0-9.]+)/i);
        if (gravityMatch) {
            this.stats.lastGravityValue = parseFloat(gravityMatch[1]);
        }
    }
    
    checkMouseSensitivity(event) {
        if (window.controls && window.controls.sensitivity) {
            this.stats.lastSensitivity = window.controls.sensitivity;
        }
    }
    
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        console.log('🚀 Diagnostic en temps réel démarré');
        
        this.interval = setInterval(() => {
            this.generateReport();
        }, 5000); // Rapport toutes les 5 secondes
    }
    
    stop() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        console.log('⏹️ Diagnostic en temps réel arrêté');
    }
    
    generateReport() {
        const timestamp = new Date().toLocaleTimeString();
        
        // Collecter les données actuelles
        const currentData = {
            timestamp,
            player: this.getPlayerData(),
            physics: this.getPhysicsData(),
            controls: this.getControlsData(),
            mining: this.getMiningData(),
            performance: this.getPerformanceData()
        };
        
        console.log('📊 RAPPORT DIAGNOSTIC', currentData);
        
        // Vérifier les problèmes
        this.checkForIssues(currentData);
        
        // Reset des compteurs
        this.resetCounters();
    }
    
    getPlayerData() {
        if (!window.player) return { status: 'Non disponible' };
        
        return {
            position: {
                x: window.player.camera.position.x.toFixed(2),
                y: window.player.camera.position.y.toFixed(2),
                z: window.player.camera.position.z.toFixed(2)
            },
            velocity: {
                x: window.player.velocity.x.toFixed(2),
                y: window.player.velocity.y.toFixed(2),
                z: window.player.velocity.z.toFixed(2)
            },
            onGround: window.player.onGround,
            flyMode: window.player.flyMode,
            isMining: window.player.isMining
        };
    }
    
    getPhysicsData() {
        return {
            gravityEvents: this.stats.gravityEvents,
            lastGravityValue: this.stats.lastGravityValue,
            spawnAttempts: this.stats.spawnAttempts
        };
    }
    
    getControlsData() {
        if (!window.controls) return { status: 'Non disponible' };
        
        return {
            sensitivity: window.controls.sensitivity || 'Non définie',
            pointerLocked: !!document.pointerLockElement,
            cameraRotations: this.stats.cameraRotations
        };
    }
    
    getMiningData() {
        return {
            attempts: this.stats.miningAttempts,
            worldChunks: window.world ? window.world.chunks.size : 0,
            playerMining: window.player ? window.player.isMining : false
        };
    }
    
    getPerformanceData() {
        return {
            fps: this.estimateFPS(),
            memoryUsage: this.getMemoryUsage()
        };
    }
    
    estimateFPS() {
        // Estimation simple du FPS
        if (!this.lastFrameTime) {
            this.lastFrameTime = performance.now();
            return 'Calcul...';
        }
        
        const now = performance.now();
        const delta = now - this.lastFrameTime;
        this.lastFrameTime = now;
        
        return Math.round(1000 / delta);
    }
    
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB'
            };
        }
        return 'Non disponible';
    }
    
    checkForIssues(data) {
        const issues = [];
        
        // Vérifier la gravité excessive
        if (Math.abs(parseFloat(data.player.velocity?.y || 0)) > 15) {
            issues.push('⚠️ Vitesse de chute élevée détectée');
        }
        
        // Vérifier la sensibilité de la souris
        if (data.controls.sensitivity > 0.002) {
            issues.push('⚠️ Sensibilité souris élevée détectée');
        }
        
        // Vérifier les tentatives de minage
        if (this.stats.miningAttempts === 0 && data.controls.pointerLocked) {
            issues.push('⚠️ Aucune tentative de minage détectée');
        }
        
        // Vérifier les chunks
        if (data.mining.worldChunks === 0) {
            issues.push('⚠️ Aucun chunk généré');
        }
        
        if (issues.length > 0) {
            console.warn('🚨 PROBLÈMES DÉTECTÉS:', issues);
        } else {
            console.log('✅ Aucun problème détecté');
        }
    }
    
    resetCounters() {
        this.stats.gravityEvents = 0;
        this.stats.miningAttempts = 0;
        this.stats.cameraRotations = 0;
    }
    
    // Méthodes de test manuel
    testMining() {
        console.log('🧪 Test de minage manuel...');
        if (window.player && window.world) {
            window.player.startMining(window.world);
            setTimeout(() => {
                window.player.stopMining();
                console.log('✅ Test de minage terminé');
            }, 2000);
        } else {
            console.error('❌ Player ou World non disponible pour le test');
        }
    }
    
    testPhysics() {
        console.log('🧪 Test de physique manuel...');
        if (window.player) {
            const originalY = window.player.camera.position.y;
            window.player.velocity.y = 8; // Force de saut
            console.log('✅ Saut forcé appliqué');
            
            setTimeout(() => {
                console.log('📊 Résultat du test de physique:', {
                    positionInitiale: originalY,
                    positionFinale: window.player.camera.position.y,
                    velocityY: window.player.velocity.y
                });
            }, 3000);
        } else {
            console.error('❌ Player non disponible pour le test');
        }
    }
}

// Créer l'instance globale
window.DiagnosticManager = new DiagnosticManager();

// Fonctions globales pour faciliter l'utilisation
window.startDiagnostic = () => window.DiagnosticManager.start();
window.stopDiagnostic = () => window.DiagnosticManager.stop();
window.testMining = () => window.DiagnosticManager.testMining();
window.testPhysics = () => window.DiagnosticManager.testPhysics();

// Auto-démarrage après 2 secondes
setTimeout(() => {
    console.log('🔧 Diagnostic Manager chargé. Utilisez:');
    console.log('  - startDiagnostic() pour démarrer');
    console.log('  - stopDiagnostic() pour arrêter');
    console.log('  - testMining() pour tester le minage');
    console.log('  - testPhysics() pour tester la physique');
    
    // Démarrer automatiquement
    window.DiagnosticManager.start();
}, 2000);
