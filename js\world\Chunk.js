// Three.js est chargé globalement depuis le CDN
const THREE = window.THREE;

// Définition des types de blocs et de leurs textures
const BLOCK_TYPES = {
    AIR: 0, STONE: 1, DIRT: 2, GRASS: 3, SAND: 4, WATER: 5, WOOD: 6, LEAVES: 7, SNOW: 8,
    ICE: 9, CLAY: 10, GRAVEL: 11, COBBLESTONE: 12, BEDROCK: 13, COAL_ORE: 14, IRON_ORE: 15,
    GOLD_ORE: 16, DIAMOND_ORE: 17, OAK_WOOD: 18, BIRCH_WOOD: 19, PINE_WOOD: 20, OAK_LEAVES: 21,
    BIRCH_LEAVES: 22, PINE_LEAVES: 23, CACTUS: 24, TALL_GRASS: 25, FLOWERS: 26, MUSHROOM: 27, LAVA: 28
};

export class Chunk {
    constructor(x, z, worldGenerator, textureGenerator = null) {
        this.x = x;
        this.z = z;
        this.blocks = new Array(16 * 128 * 16).fill(0); // 0 = air, hauteur limitée à 128
        this.mesh = null;
        this.worldGenerator = worldGenerator;
        this.textureGenerator = textureGenerator;
        this.isGenerated = false;
        this.instancedMeshes = new Map(); // Stockage des meshes instanciés par type de bloc
        
        // Mapping des types de blocs vers les textures
        this.blockTextureMapping = {
            [BLOCK_TYPES.STONE]: 'STONE',
            [BLOCK_TYPES.DIRT]: 'DIRT',
            [BLOCK_TYPES.GRASS]: 'GRASS',
            [BLOCK_TYPES.SAND]: 'SAND',
            [BLOCK_TYPES.WATER]: 'WATER',
            [BLOCK_TYPES.OAK_WOOD]: 'WOOD_OAK',
            [BLOCK_TYPES.BIRCH_WOOD]: 'WOOD_BIRCH',
            [BLOCK_TYPES.PINE_WOOD]: 'WOOD_SPRUCE',
            [BLOCK_TYPES.OAK_LEAVES]: 'LEAVES_OAK',
            [BLOCK_TYPES.BIRCH_LEAVES]: 'LEAVES_BIRCH',
            [BLOCK_TYPES.PINE_LEAVES]: 'LEAVES_SPRUCE',
            [BLOCK_TYPES.COAL_ORE]: 'COAL_ORE',
            [BLOCK_TYPES.IRON_ORE]: 'IRON_ORE',
            [BLOCK_TYPES.GOLD_ORE]: 'GOLD_ORE'
        };
        
        // Générer le chunk si le générateur est fourni
        if (worldGenerator) {
            this.generate();
        }
    }

    // Géométrie et matériaux partagés pour tous les chunks
    static sharedGeometry = null;
    static sharedMaterials = new Map();
    static textureLoader = new THREE.TextureLoader();
    static texturesLoaded = false;
    
    // Initialisation des ressources partagées
    static initializeSharedResources(textureGenerator = null) {
        if (!Chunk.sharedGeometry) {
            // Créer la géométrie de base pour tous les blocs
            Chunk.sharedGeometry = new THREE.BoxGeometry(1, 1, 1);
            
            // Charger les matériaux avec ou sans TextureGenerator
            Chunk.loadTextures(textureGenerator);
        }
    }
    
    // ===== SECTION INTÉGRALEMENT CORRIGÉE ET RESTRUCTURÉE =====
    static loadTextures(textureGenerator = null) {
        try {
            // Déclaration de toutes les variables de matériaux pour qu'elles aient la bonne portée
            let stoneMaterial, dirtMaterial, grassSideMaterial, grassTopMaterial, sandMaterial, waterMaterial,
                woodMaterial, leavesMaterial, snowMaterial, iceMaterial, clayMaterial, gravelMaterial,
                cobblestoneMaterial, bedrockMaterial, coalOreMaterial, ironOreMaterial, goldOreMaterial,
                diamondOreMaterial, oakWoodMaterial, birchWoodMaterial, pineWoodMaterial, oakLeavesMaterial,
                birchLeavesMaterial, pineLeavesMaterial, cactusMaterial, tallGrassMaterial, flowersMaterial,
                mushroomMaterial, lavaMaterial;

            if (textureGenerator) {
                // Utiliser le TextureGenerator pour créer des textures procédurales
                const stoneTexture = textureGenerator.generateTexture('STONE');
                const dirtTexture = textureGenerator.generateTexture('DIRT');
                // Assurez-vous que votre TextureGenerator peut gérer 'GRASS_TOP' et 'GRASS_SIDE'
                const grassTopTexture = textureGenerator.generateTexture('GRASS_TOP');
                const grassSideTexture = textureGenerator.generateTexture('GRASS_SIDE');
                const sandTexture = textureGenerator.generateTexture('SAND');
                const waterTexture = textureGenerator.generateTexture('WATER');
                const oakWoodTexture = textureGenerator.generateTexture('WOOD_OAK');
                const birchWoodTexture = textureGenerator.generateTexture('WOOD_BIRCH');
                const spruceWoodTexture = textureGenerator.generateTexture('WOOD_SPRUCE');
                const oakLeavesTexture = textureGenerator.generateTexture('LEAVES_OAK');
                const birchLeavesTexture = textureGenerator.generateTexture('LEAVES_BIRCH');
                const spruceLeavesTexture = textureGenerator.generateTexture('LEAVES_SPRUCE');
                const coalOreTexture = textureGenerator.generateTexture('COAL_ORE');
                const ironOreTexture = textureGenerator.generateTexture('IRON_ORE');
                const goldOreTexture = textureGenerator.generateTexture('GOLD_ORE');
                
                // Créer les matériaux avec textures procédurales
                stoneMaterial = new THREE.MeshLambertMaterial({ map: stoneTexture });
                dirtMaterial = new THREE.MeshLambertMaterial({ map: dirtTexture });
                grassSideMaterial = new THREE.MeshLambertMaterial({ map: grassSideTexture });
                grassTopMaterial = new THREE.MeshLambertMaterial({ map: grassTopTexture });
                sandMaterial = new THREE.MeshLambertMaterial({ map: sandTexture });
                waterMaterial = new THREE.MeshLambertMaterial({ map: waterTexture, transparent: true, opacity: 0.7 });
                oakWoodMaterial = new THREE.MeshLambertMaterial({ map: oakWoodTexture });
                birchWoodMaterial = new THREE.MeshLambertMaterial({ map: birchWoodTexture });
                pineWoodMaterial = new THREE.MeshLambertMaterial({ map: spruceWoodTexture });
                oakLeavesMaterial = new THREE.MeshLambertMaterial({ map: oakLeavesTexture, transparent: true, opacity: 0.9, side: THREE.DoubleSide });
                birchLeavesMaterial = new THREE.MeshLambertMaterial({ map: birchLeavesTexture, transparent: true, opacity: 0.9, side: THREE.DoubleSide });
                // CORRECTION DE LA FAUTE DE FRAPPE ICI
                pineLeavesMaterial = new THREE.MeshLambertMaterial({ map: spruceLeavesTexture, transparent: true, opacity: 0.9, side: THREE.DoubleSide });
                coalOreMaterial = new THREE.MeshLambertMaterial({ map: coalOreTexture });
                ironOreMaterial = new THREE.MeshLambertMaterial({ map: ironOreTexture });
                goldOreMaterial = new THREE.MeshLambertMaterial({ map: goldOreTexture });
                
                console.log('🎨 Textures procédurales générées avec succès');
                
            } else {
                // Fallback vers les matériaux de couleur simple
                stoneMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
                dirtMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                grassSideMaterial = new THREE.MeshLambertMaterial({ color: 0x9B7653 }); // Une couleur latérale pour l'herbe
                grassTopMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
                sandMaterial = new THREE.MeshLambertMaterial({ color: 0xF0E68C });
                waterMaterial = new THREE.MeshLambertMaterial({ color: 0x1E90FF, transparent: true, opacity: 0.7 });
                woodMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22, transparent: true, opacity: 0.9, side: THREE.DoubleSide });
            }
            
            // Nouveaux matériaux (communs ou fallback)
            snowMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
            iceMaterial = new THREE.MeshLambertMaterial({ color: 0xB0E0E6, transparent: true, opacity: 0.8 });
            clayMaterial = new THREE.MeshLambertMaterial({ color: 0xCD853F });
            gravelMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
            cobblestoneMaterial = new THREE.MeshLambertMaterial({ color: 0x555555 });
            bedrockMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
            diamondOreMaterial = new THREE.MeshLambertMaterial({ color: 0x00FFFF });
            
            // Différents types de bois si non définis par texture
            oakWoodMaterial = oakWoodMaterial || new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            birchWoodMaterial = birchWoodMaterial || new THREE.MeshLambertMaterial({ color: 0xF5DEB3 });
            pineWoodMaterial = pineWoodMaterial || new THREE.MeshLambertMaterial({ color: 0x654321 });
            
            // Différents types de feuilles si non définis par texture
            oakLeavesMaterial = oakLeavesMaterial || new THREE.MeshLambertMaterial({ color: 0x228B22, transparent: true, opacity: 0.9, side: THREE.DoubleSide });
            birchLeavesMaterial = birchLeavesMaterial || new THREE.MeshLambertMaterial({ color: 0x90EE90, transparent: true, opacity: 0.9, side: THREE.DoubleSide });
            pineLeavesMaterial = pineLeavesMaterial || new THREE.MeshLambertMaterial({ color: 0x006400, transparent: true, opacity: 0.9, side: THREE.DoubleSide });

            // Végétation et décorations
            cactusMaterial = new THREE.MeshLambertMaterial({ color: 0x32CD32 });
            tallGrassMaterial = new THREE.MeshLambertMaterial({ color: 0x9ACD32, transparent: true, opacity: 0.8, side: THREE.DoubleSide });
            flowersMaterial = new THREE.MeshLambertMaterial({ color: 0xFF69B4 });
            mushroomMaterial = new THREE.MeshLambertMaterial({ color: 0xDC143C });
            
            // Matériau pour la lave
            lavaMaterial = new THREE.MeshLambertMaterial({ color: 0xFF4500, emissive: 0xFF2000, emissiveIntensity: 0.5, transparent: true, opacity: 0.9 });

            // Créer le matériau composite pour l'herbe (maintenant que les variables sont garanties d'exister)
            const grassMaterials = [grassSideMaterial, grassSideMaterial, grassTopMaterial, dirtMaterial, grassSideMaterial, grassSideMaterial];
            
            // Assignation finale et unique des matériaux
            Chunk.sharedMaterials.set(BLOCK_TYPES.STONE, stoneMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.DIRT, dirtMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.GRASS, grassMaterials);
            Chunk.sharedMaterials.set(BLOCK_TYPES.SAND, sandMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.WATER, waterMaterial);
            if(woodMaterial) Chunk.sharedMaterials.set(BLOCK_TYPES.WOOD, woodMaterial);
            if(leavesMaterial) Chunk.sharedMaterials.set(BLOCK_TYPES.LEAVES, leavesMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.SNOW, snowMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.ICE, iceMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.CLAY, clayMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.GRAVEL, gravelMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.COBBLESTONE, cobblestoneMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.BEDROCK, bedrockMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.COAL_ORE, coalOreMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.IRON_ORE, ironOreMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.GOLD_ORE, goldOreMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.DIAMOND_ORE, diamondOreMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.OAK_WOOD, oakWoodMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.BIRCH_WOOD, birchWoodMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.PINE_WOOD, pineWoodMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.OAK_LEAVES, oakLeavesMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.BIRCH_LEAVES, birchLeavesMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.PINE_LEAVES, pineLeavesMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.CACTUS, cactusMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.TALL_GRASS, tallGrassMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.FLOWERS, flowersMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.MUSHROOM, mushroomMaterial);
            Chunk.sharedMaterials.set(BLOCK_TYPES.LAVA, lavaMaterial);
            
            Chunk.texturesLoaded = true;
            console.log('Matériaux de base configurés avec succès!');
            
        } catch (error) {
            console.error('Erreur lors de la configuration des matériaux:', error);
            Chunk.loadFallbackMaterials();
        }
    }
    
    // Méthode de fallback pour les matériaux simples
    static loadFallbackMaterials() {
        const stoneMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
        const dirtMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const grassTopMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const sandMaterial = new THREE.MeshLambertMaterial({ color: 0xF0E68C });
        const waterMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x1E90FF, 
            transparent: true, 
            opacity: 0.7 
        });
        
        Chunk.sharedMaterials.set(BLOCK_TYPES.STONE, stoneMaterial);
        Chunk.sharedMaterials.set(BLOCK_TYPES.DIRT, dirtMaterial);
        Chunk.sharedMaterials.set(BLOCK_TYPES.GRASS, grassTopMaterial);
        Chunk.sharedMaterials.set(BLOCK_TYPES.SAND, sandMaterial);
        Chunk.sharedMaterials.set(BLOCK_TYPES.WATER, waterMaterial);
        
        console.log('Matériaux de fallback chargés');
    }

    generate() {
        // Vérifier si le chunk est déjà généré
        if (this.isGenerated || this.blocks.some(block => block !== 0)) {
            this.isGenerated = true;
            return;
        }
        
        // Génération du terrain avec biomes améliorés
        for (let x = 0; x < 16; x++) {
            for (let z = 0; z < 16; z++) {
                const worldX = this.x * 16 + x;
                const worldZ = this.z * 16 + z;
                
                // Obtenir les informations du biome
                const biomeValue = this.worldGenerator.getBiomeValue(worldX, worldZ);
                const biome = this.worldGenerator.getBiome(biomeValue);
                const baseHeight = this.worldGenerator.getHeight(worldX, worldZ);
                const detailNoise = this.worldGenerator.getDetailNoise(worldX, worldZ);
                const height = Math.floor(baseHeight + detailNoise);
                
                // Limiter la hauteur pour les performances
                const maxHeight = Math.min(height + 15, 128);
                
                // Générer les couches de blocs selon le biome
                for (let y = 0; y < maxHeight; y++) {
                    const worldY = y;
                    
                    // Vérifier d'abord les grottes (mais pas trop près de la surface)
                    if (y < height - 5 && this.worldGenerator.shouldHaveCave(worldX, worldY, worldZ)) {
                        this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.AIR;
                        continue;
                    }
                    
                    // Génération de la bedrock au fond
                    if (y <= 2) {
                        this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.BEDROCK;
                        continue;
                    }
                    
                    // Génération selon la profondeur et le biome
                    if (y < height - 8) {
                        // Couche profonde: pierre avec minerais
                        const oreType = this.worldGenerator.getOreType(worldX, worldY, worldZ);
                        if (oreType) {
                            this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES[oreType];
                        } else {
                            this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.STONE;
                        }
                    }
                    else if (y < height - 2) {
                        // Couche intermédiaire selon le biome
                        switch (biome.name) {
                            case 'Beach':
                                this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.SAND;
                                break;
                            case 'Mountains':
                                this.blocks[this.getIndex(x, y, z)] = Math.random() < 0.3 ? BLOCK_TYPES.STONE : BLOCK_TYPES.DIRT;
                                break;
                            default:
                                this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.DIRT;
                        }
                    }
                    else if (y <= height) {
                        // Surface selon le biome
                        switch (biome.name) {
                            case 'Ocean':
                            case 'Beach':
                                this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.SAND;
                                break;
                            case 'Mountains':
                                if (height > 90) {
                                    this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.SNOW;
                                } else if (height > 80) {
                                    this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.STONE;
                                } else {
                                    this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.GRASS;
                                }
                                break;
                            default:
                                this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.GRASS;
                        }
                    }
                    
                    // Génération d'eau seulement dans les océans profonds
                    const waterLevel = this.worldGenerator.waterLevel;
                    if (y <= waterLevel && y > height && biome.name === 'Ocean' && height < waterLevel - 5) {
                        // Eau seulement si le terrain est suffisamment bas (océan profond)
                        this.blocks[this.getIndex(x, y, z)] = BLOCK_TYPES.WATER;
                    }
                }
                
                // Génération de végétation selon le biome
                this.generateVegetation(x, z, height, biome, biomeValue);
            }
        }
        
        this.isGenerated = true;
    }
    
    // Générer la végétation selon le biome
    generateVegetation(x, z, height, biome, biomeValue) {
        
        switch (biome.name) {
            case 'Plains':
                // Herbe haute et fleurs occasionnelles
                if (Math.random() < 0.3) {
                    this.blocks[this.getIndex(x, height + 1, z)] = BLOCK_TYPES.TALL_GRASS;
                }
                if (Math.random() < 0.05) {
                    this.blocks[this.getIndex(x, height + 1, z)] = BLOCK_TYPES.FLOWERS;
                }
                // Arbres rares
                if (Math.random() < 0.01) {
                    const treeType = this.worldGenerator.getTreeType(biomeValue);
                    this.generateTree(x, height + 1, z, treeType);
                }
                break;
                
            case 'Forest':
                // Beaucoup d'arbres
                if (Math.random() < 0.15) {
                    const treeType = this.worldGenerator.getTreeType(biomeValue);
                    this.generateTree(x, height + 1, z, treeType);
                }
                // Champignons sous les arbres
                if (Math.random() < 0.02) {
                    this.blocks[this.getIndex(x, height + 1, z)] = BLOCK_TYPES.MUSHROOM;
                }
                break;
                
            case 'Hills':
                // Arbres épars et herbe
                if (Math.random() < 0.08) {
                    const treeType = this.worldGenerator.getTreeType(biomeValue);
                    this.generateTree(x, height + 1, z, treeType);
                }
                if (Math.random() < 0.2) {
                    this.blocks[this.getIndex(x, height + 1, z)] = BLOCK_TYPES.TALL_GRASS;
                }
                break;
                
            case 'Mountains':
                // Pins rares en altitude
                if (height < 85 && Math.random() < 0.03) {
                    this.generateTree(x, height + 1, z, 'PINE');
                }
                break;
                
            case 'Beach':
                // Cactus occasionnels (simulation de végétation côtière)
                if (Math.random() < 0.01) {
                    this.generateCactus(x, height + 1, z);
                }
                break;
        }
    }
    
    // Générer un arbre selon le type spécifié
    generateTree(x, y, z, treeType = 'OAK') {
        if (x < 2 || x > 13 || z < 2 || z > 13 || y + 24 >= 128) return;

        let woodType, leavesType, trunkHeight, leavesLayers;

        // Configuration selon le type d'arbre
        switch (treeType) {
            case 'OAK':
                woodType = BLOCK_TYPES.OAK_WOOD;
                leavesType = BLOCK_TYPES.OAK_LEAVES;
                trunkHeight = (4 + Math.floor(Math.random() * 2)) * 3;
                leavesLayers = 3;
                break;
            case 'BIRCH':
                woodType = BLOCK_TYPES.BIRCH_WOOD;
                leavesType = BLOCK_TYPES.BIRCH_LEAVES;
                trunkHeight = (5 + Math.floor(Math.random() * 2)) * 3;
                leavesLayers = 2;
                break;
            case 'PINE':
                woodType = BLOCK_TYPES.PINE_WOOD;
                leavesType = BLOCK_TYPES.PINE_LEAVES;
                trunkHeight = (6 + Math.floor(Math.random() * 3)) * 3;
                leavesLayers = 4;
                break;
            default:
                woodType = BLOCK_TYPES.WOOD;
                leavesType = BLOCK_TYPES.LEAVES;
                trunkHeight = (4 + Math.floor(Math.random() * 2)) * 3;
                leavesLayers = 3;
        }
        
        // Générer le tronc
        for (let i = 0; i < trunkHeight; i++) {
            this.blocks[this.getIndex(x, y + i, z)] = woodType;
        }
        
        // Générer les feuilles selon le type d'arbre
        if (treeType === 'PINE') {
            // Forme conique pour les pins
            for (let ly = 0; ly < leavesLayers; ly++) {
                const radius = Math.max(1, leavesLayers - ly);
                const layerY = y + trunkHeight - 2 + ly;
                
                for (let lx = -radius; lx <= radius; lx++) {
                    for (let lz = -radius; lz <= radius; lz++) {
                        const distance = Math.sqrt(lx * lx + lz * lz);
                        if (distance <= radius && Math.random() < 0.8) {
                            const leafX = x + lx;
                            const leafZ = z + lz;
                            if (leafX >= 0 && leafX < 16 && leafZ >= 0 && leafZ < 16) {
                                if (!(lx === 0 && lz === 0)) {
                                    this.blocks[this.getIndex(leafX, layerY, leafZ)] = leavesType;
                                }
                            }
                        }
                    }
                }
            }
        } else {
            // Forme ronde pour chênes et bouleaux
            const leavesY = y + trunkHeight - 1;
            for (let ly = 0; ly < leavesLayers; ly++) {
                const radius = ly === 0 ? 2 : (ly === leavesLayers - 1 ? 1 : 2);
                for (let lx = -radius; lx <= radius; lx++) {
                    for (let lz = -radius; lz <= radius; lz++) {
                        // Éviter les coins trop carrés et ajouter de la randomisation
                        if (Math.abs(lx) === radius && Math.abs(lz) === radius && Math.random() < 0.5) continue;
                        
                        const leafX = x + lx;
                        const leafZ = z + lz;
                        if (leafX >= 0 && leafX < 16 && leafZ >= 0 && leafZ < 16) {
                            if (!(lx === 0 && lz === 0)) {
                                this.blocks[this.getIndex(leafX, leavesY + ly, leafZ)] = leavesType;
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Générer un cactus
    generateCactus(x, y, z) {
        if (x < 0 || x >= 16 || z < 0 || z >= 16 || y + 3 >= 128) return;
        
        const height = 2 + Math.floor(Math.random() * 2); // 2 à 3 blocs
        for (let i = 0; i < height; i++) {
            this.blocks[this.getIndex(x, y + i, z)] = BLOCK_TYPES.CACTUS;
        }
    }

    getIndex(x, y, z) {
        return x * 128 * 16 + y * 16 + z; // Hauteur limitée à 128
    }
    
    // Obtenir le type de bloc à une position donnée
    getBlockAt(x, y, z) {
        if (x < 0 || x >= 16 || y < 0 || y >= 128 || z < 0 || z >= 16) {
            return BLOCK_TYPES.AIR; // Hors limites = air
        }
        return this.blocks[this.getIndex(x, y, z)];
    }
    
    // Définir le type de bloc à une position donnée
    setBlockAt(x, y, z, blockType) {
        if (x < 0 || x >= 16 || y < 0 || y >= 128 || z < 0 || z >= 16) {
            return false; // Hors limites
        }
        
        const oldBlockType = this.blocks[this.getIndex(x, y, z)];
        this.blocks[this.getIndex(x, y, z)] = blockType;
        
        console.log(`🔨 Bloc modifié dans chunk (${this.x}, ${this.z}) à position locale (${x}, ${y}, ${z}): ${oldBlockType} → ${blockType}`);
        return true;
    }
    
    // Vérifier si un bloc est visible (c'est-à-dire s'il a au moins une face exposée à l'air)
    isBlockVisible(x, y, z) {
        // Si c'est de l'air, pas besoin de le rendre
        const blockType = this.getBlockAt(x, y, z);
        if (blockType === BLOCK_TYPES.AIR) return false;
        
        // Si c'est transparent comme l'eau, toujours visible
        if (blockType === BLOCK_TYPES.WATER) return true;
        
        // Vérifier les 6 faces pour voir si au moins une est exposée
        // Face avant
        if (this.getBlockAt(x, y, z + 1) === BLOCK_TYPES.AIR || this.getBlockAt(x, y, z + 1) === BLOCK_TYPES.WATER) return true;
        // Face arrière
        if (this.getBlockAt(x, y, z - 1) === BLOCK_TYPES.AIR || this.getBlockAt(x, y, z - 1) === BLOCK_TYPES.WATER) return true;
        // Face droite
        if (this.getBlockAt(x + 1, y, z) === BLOCK_TYPES.AIR || this.getBlockAt(x + 1, y, z) === BLOCK_TYPES.WATER) return true;
        // Face gauche
        if (this.getBlockAt(x - 1, y, z) === BLOCK_TYPES.AIR || this.getBlockAt(x - 1, y, z) === BLOCK_TYPES.WATER) return true;
        // Face supérieure
        if (this.getBlockAt(x, y + 1, z) === BLOCK_TYPES.AIR || this.getBlockAt(x, y + 1, z) === BLOCK_TYPES.WATER) return true;
        // Face inférieure
        if (this.getBlockAt(x, y - 1, z) === BLOCK_TYPES.AIR || this.getBlockAt(x, y - 1, z) === BLOCK_TYPES.WATER) return true;
        
        // Toutes les faces sont couvertes, le bloc n'est pas visible
        return false;
    }

    buildMesh() {
        // Initialiser les ressources partagées si nécessaire
        Chunk.initializeSharedResources(this.textureGenerator);
        
        // Supprimer les anciens meshes s'ils existent
        if (this.mesh) {
            // Si c'est un groupe, supprimer tous les enfants
            if (this.mesh.isGroup) {
                while (this.mesh.children.length > 0) {
                    const child = this.mesh.children[0];
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(m => m.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                    this.mesh.remove(child);
                }
            } else {
                // C'est un seul mesh
                if (this.mesh.geometry) this.mesh.geometry.dispose();
                if (this.mesh.material) {
                    if (Array.isArray(this.mesh.material)) {
                        this.mesh.material.forEach(m => m.dispose());
                    } else {
                        this.mesh.material.dispose();
                    }
                }
            }
        }
        
        // Créer un nouveau groupe pour contenir tous les meshes
        this.mesh = new THREE.Group();
        
        // Map pour compter le nombre de blocs de chaque type
        const blockCounts = new Map();
        
        // Première passe : compter les blocs visibles de chaque type
        for (let x = 0; x < 16; x++) {
            for (let y = 0; y < 128; y++) {
                for (let z = 0; z < 16; z++) {
                    const blockType = this.blocks[this.getIndex(x, y, z)];
                    if (blockType !== BLOCK_TYPES.AIR && this.isBlockVisible(x, y, z)) {
                        blockCounts.set(blockType, (blockCounts.get(blockType) || 0) + 1);
                    }
                }
            }
        }
        
        // Créer un mesh instancié pour chaque type de bloc
        const matrix = new THREE.Matrix4();
        
        blockCounts.forEach((count, blockType) => {
            // Récupérer le matériau pour ce type de bloc
            const material = Chunk.sharedMaterials.get(blockType);
            if (!material) return;
            
            // Créer un mesh instancié pour ce type de bloc
            const instancedMesh = new THREE.InstancedMesh(
                Chunk.sharedGeometry,
                material,
                count
            );
            instancedMesh.castShadow = true;
            instancedMesh.receiveShadow = true;
            
            // Remplir le mesh avec les instances de blocs
            let index = 0;
            for (let x = 0; x < 16; x++) {
                for (let y = 0; y < 128; y++) {
                    for (let z = 0; z < 16; z++) {
                        if (this.blocks[this.getIndex(x, y, z)] === blockType && this.isBlockVisible(x, y, z)) {
                            matrix.setPosition(this.x * 16 + x, y, this.z * 16 + z);
                            instancedMesh.setMatrixAt(index++, matrix);
                        }
                    }
                }
            }
            
            // Mettre à jour les matrices pour le rendu
            instancedMesh.instanceMatrix.needsUpdate = true;
            
            // Ajouter au groupe
            this.mesh.add(instancedMesh);
            
            // Stocker pour référence
            this.instancedMeshes.set(blockType, instancedMesh);
        });
        
        return this.mesh;
    }
    
    // Régénérer le mesh du chunk (pour le minage)
    generateMesh() {
        console.log(`🔄 Début de régénération du mesh du chunk (${this.x}, ${this.z})`);
        
        // Sauvegarder la référence au parent (scene)
        let parentScene = null;
        if (this.mesh && this.mesh.parent) {
            parentScene = this.mesh.parent;
        }
        
        // Supprimer l'ancien mesh s'il existe
        if (this.mesh) {
            if (this.mesh.parent) {
                this.mesh.parent.remove(this.mesh);
            }
            
            // Si c'est un groupe, nettoyer tous les enfants
            if (this.mesh.isGroup) {
                while (this.mesh.children.length > 0) {
                    const child = this.mesh.children[0];
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(m => m.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                    this.mesh.remove(child);
                }
            } else {
                // C'est un mesh simple
                if (this.mesh.geometry) this.mesh.geometry.dispose();
                if (this.mesh.material) {
                    if (Array.isArray(this.mesh.material)) {
                        this.mesh.material.forEach(mat => mat.dispose());
                    } else {
                        this.mesh.material.dispose();
                    }
                }
            }
            this.mesh = null;
        }
        
        // Supprimer les anciens meshes instanciés
        for (const [blockType, mesh] of this.instancedMeshes) {
            if (mesh && mesh.parent) {
                mesh.parent.remove(mesh);
                mesh.geometry.dispose();
                if (mesh.material) {
                    if (Array.isArray(mesh.material)) {
                        mesh.material.forEach(mat => mat.dispose());
                    } else {
                        mesh.material.dispose();
                    }
                }
            }
        }
        this.instancedMeshes.clear();
        
        // Régénérer le mesh
        const newMesh = this.buildMesh();
        
        // Ajouter le nouveau mesh à la scène si on avait un parent
        if (newMesh && parentScene) {
            parentScene.add(newMesh);
            this.mesh = newMesh;
        }
        
        console.log(`🔄 Mesh du chunk (${this.x}, ${this.z}) régénéré avec succès`);
        return newMesh;
    }
}