// Immediate mining test - to be executed in the console
console.log('🔧 Immediate Mining Test - Loaded');

// Simple test function
window.testMiningNow = function() {
    console.log('🧪 === IMMEDIATE MINING TEST ===');
    
    // 1. Check basic objects
    console.log('📊 Basic checks:');
    console.log('  - window.world:', !!window.world);
    console.log('  - window.player:', !!window.player);
    console.log('  - window.controls:', !!window.controls);
    console.log('  - GameLogger:', !!window.GameLogger);
    console.log('  - pointer locked:', !!document.pointerLockElement);
    
    if (!window.player) {
        console.error('❌ Player not available');
        return;
    }
    
    if (!window.world) {
        console.error('❌ World not available');
        return;
    }
    
    // 2. Check logger methods
    console.log('🔍 Checking logger:');
    if (window.player.logger) {
        console.log('  - player.logger exists:', true);
        console.log('  - mining() method:', typeof window.player.logger.mining === 'function');
        console.log('  - info() method:', typeof window.player.logger.info === 'function');
        
        // Test the mining method
        try {
            window.player.logger.mining('Testing mining method', { test: true });
            console.log('  ✅ mining() method works');
        } catch (error) {
            console.error('  ❌ Error in mining() method:', error.message);
        }
    } else {
        console.error('❌ player.logger not available');
        return;
    }
    
    // 3. Check chunks and meshes
    console.log('🌍 Checking chunks:');
    const totalChunks = window.world.chunks.size;
    console.log('  - Total number of chunks:', totalChunks);
    
    let chunksWithMesh = 0;
    let visibleChunks = 0;
    let totalMeshes = 0;
    
    window.world.chunks.forEach((chunkData, key) => {
        if (chunkData.mesh) {
            chunksWithMesh++;
            if (chunkData.mesh.visible) {
                visibleChunks++;
                totalMeshes += chunkData.mesh.children ? chunkData.mesh.children.length : 0;
            }
        }
    });
    
    console.log('  - Chunks with mesh:', chunksWithMesh);
    console.log('  - Visible chunks:', visibleChunks);
    console.log('  - Total child meshes:', totalMeshes);
    
    if (totalMeshes === 0) {
        console.warn('⚠️ No child meshes available for raycast');
        return;
    }
    
    // 4. Test raycast
    console.log('🎯 Testing raycast:');
    try {
        const allMeshes = Array.from(window.world.chunks.values())
            .filter(chunkData => chunkData && chunkData.mesh && chunkData.mesh.visible)
            .flatMap(chunkData => chunkData.mesh.children || []);
        
        console.log('  - Meshes for raycast:', allMeshes.length);
        
        if (allMeshes.length > 0) {
            const direction = window.player.camera.getWorldDirection(new THREE.Vector3());
            window.player.raycaster.set(window.player.camera.position, direction);
            const intersects = window.player.raycaster.intersectObjects(allMeshes);
            
            console.log('  - Intersections found:', intersects.length);
            
            if (intersects.length > 0) {
                console.log('  - First intersection:', {
                    distance: intersects[0].distance.toFixed(2),
                    point: {
                        x: intersects[0].point.x.toFixed(2),
                        y: intersects[0].point.y.toFixed(2),
                        z: intersects[0].point.z.toFixed(2)
                    }
                });
            }
        }
    } catch (error) {
        console.error('❌ Error during raycast:', error.message);
    }
    
    // 5. Direct mining test
    console.log('⛏️ Direct mining test:');
    console.log('  - Player.isMining before:', window.player.isMining);
    console.log('  - Player.inventoryOpen:', window.player.inventoryOpen);
    
    try {
        // Direct call to startMining
        console.log('🚀 Calling startMining...');
        window.player.startMining(window.world);
        
        console.log('  - Player.isMining after:', window.player.isMining);
        
        // Stop after 2 seconds
        setTimeout(() => {
            console.log('⏹️ Stopping mining...');
            window.player.stopMining();
            console.log('  - Player.isMining final:', window.player.isMining);
            console.log('✅ Mining test finished');
        }, 2000);
        
    } catch (error) {
        console.error('❌ Error during mining:', error.message);
        console.error('Stack:', error.stack);
    }
};

// Function to simulate a mining click
window.simulateMiningClick = function() {
    console.log('🖱️ Simulating a mining click...');
    
    // Check if the pointer is locked
    if (!document.pointerLockElement) {
        console.warn('⚠️ Pointer not locked - automatic locking...');
        document.body.requestPointerLock();
        
        setTimeout(() => {
            if (document.pointerLockElement) {
                console.log('✅ Pointer locked, simulating click...');
                simulateClickEvent();
            } else {
                console.error('❌ Could not lock pointer');
            }
        }, 500);
    } else {
        simulateClickEvent();
    }
    
    function simulateClickEvent() {
        // Create a mousedown event
        const mouseDownEvent = new MouseEvent('mousedown', {
            button: 0,
            buttons: 1,
            clientX: window.innerWidth / 2,
            clientY: window.innerHeight / 2,
            bubbles: true,
            cancelable: true
        });
        
        console.log('📤 Dispatching mousedown event...');
        document.dispatchEvent(mouseDownEvent);
        
        // Create a mouseup event after 1 second
        setTimeout(() => {
            const mouseUpEvent = new MouseEvent('mouseup', {
                button: 0,
                buttons: 0,
                clientX: window.innerWidth / 2,
                clientY: window.innerHeight / 2,
                bubbles: true,
                cancelable: true
            });
            
            console.log('📤 Dispatching mouseup event...');
            document.dispatchEvent(mouseUpEvent);
            console.log('✅ Click simulation finished');
        }, 1000);
    }
};

// Function to show detailed state
window.showMiningState = function() {
    console.log('📊 === DETAILED MINING STATE ===');
    
    if (window.player) {
        console.log('👤 Player:');
        console.log('  - isMining:', window.player.isMining);
        console.log('  - inventoryOpen:', window.player.inventoryOpen);
        console.log('  - miningTarget:', window.player.miningTarget);
        console.log('  - miningProgress:', window.player.miningProgress);
        console.log('  - position:', {
            x: window.player.camera.position.x.toFixed(2),
            y: window.player.camera.position.y.toFixed(2),
            z: window.player.camera.position.z.toFixed(2)
        });
    }
    
    if (window.controls) {
        console.log('🎮 Controls:');
        console.log('  - sensitivity:', window.controls.sensitivity);
        console.log('  - isPointerLocked:', window.controls.isPointerLocked);
    }
    
    if (window.world) {
        console.log('🌍 World:');
        console.log('  - chunks.size:', window.world.chunks.size);
        console.log('  - renderDistance:', window.world.renderDistance);
    }
};
