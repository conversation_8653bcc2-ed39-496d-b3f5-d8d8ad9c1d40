// server/game/WorldManager.js
// Gestionnaire du monde côté serveur - LA SOURCE DE VÉRITÉ
import { GAME_CONFIG, BLOCK_TYPES, Utils } from '../../shared/constants.js';
import { WorldGenerator } from '../utils/WorldGenerator.js';

export class WorldManager {
    constructor(seed = null) {
        this.seed = seed || Math.floor(Math.random() * 1000000);
        this.chunks = new Map(); // Map de "x,z" -> ChunkData
        this.loadedChunks = new Set(); // Set des chunks chargés
        
        // Configuration
        this.loadDistance = GAME_CONFIG.DEFAULT_LOAD_DISTANCE;
        this.maxLoadedChunks = 400; // Limite pour éviter la surcharge mémoire
        
        // Cache pour optimiser les requêtes fréquentes
        this.groundHeightCache = new Map();
        this.blockCache = new Map();
        this.cacheTimeout = 5000; // 5 secondes
        
        // Générateur de monde
        this.worldGenerator = new WorldGenerator(this.seed);
        
        // Statistiques
        this.stats = {
            chunksLoaded: 0,
            chunksGenerated: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        console.log(`🌍 WorldManager initialisé avec seed: ${this.seed}`);
    }
    

    
    // Mise à jour du monde basée sur les positions des joueurs
    update(playerPositions) {
        // Déterminer quels chunks doivent être chargés
        const requiredChunks = new Set();
        
        playerPositions.forEach(pos => {
            const chunkCoords = this.getChunkCoordinates(pos.x, pos.z);
            
            // Ajouter tous les chunks dans la distance de chargement
            for (let dx = -this.loadDistance; dx <= this.loadDistance; dx++) {
                for (let dz = -this.loadDistance; dz <= this.loadDistance; dz++) {
                    const chunkX = chunkCoords.x + dx;
                    const chunkZ = chunkCoords.z + dz;
                    const distance = Math.sqrt(dx * dx + dz * dz);
                    
                    if (distance <= this.loadDistance) {
                        requiredChunks.add(`${chunkX},${chunkZ}`);
                    }
                }
            }
        });
        
        // Charger les nouveaux chunks
        const newChunks = [];
        requiredChunks.forEach(chunkKey => {
            if (!this.loadedChunks.has(chunkKey)) {
                const [x, z] = chunkKey.split(',').map(Number);
                const chunkData = this.generateChunk(x, z);
                this.chunks.set(chunkKey, chunkData);
                this.loadedChunks.add(chunkKey);
                newChunks.push(chunkData);
                this.stats.chunksGenerated++;
            }
        });
        
        // Décharger les chunks trop éloignés
        if (this.loadedChunks.size > this.maxLoadedChunks) {
            this.unloadDistantChunks(playerPositions);
        }
        
        // Nettoyer les caches périodiquement
        this.cleanupCaches();
        
        return newChunks;
    }
    
    generateChunk(chunkX, chunkZ) {
        const chunkData = {
            x: chunkX,
            z: chunkZ,
            blocks: new Array(GAME_CONFIG.CHUNK_WIDTH * GAME_CONFIG.CHUNK_HEIGHT * GAME_CONFIG.CHUNK_DEPTH).fill(0),
            generated: true,
            timestamp: Date.now()
        };
        
        // Générer les blocs
        for (let x = 0; x < GAME_CONFIG.CHUNK_WIDTH; x++) {
            for (let z = 0; z < GAME_CONFIG.CHUNK_DEPTH; z++) {
                const worldX = chunkX * GAME_CONFIG.CHUNK_WIDTH + x;
                const worldZ = chunkZ * GAME_CONFIG.CHUNK_DEPTH + z;
                
                for (let y = 0; y < GAME_CONFIG.CHUNK_HEIGHT; y++) {
                    const blockType = this.worldGenerator.getBlockType(worldX, y, worldZ);
                    const index = this.getBlockIndex(x, y, z);
                    chunkData.blocks[index] = blockType;
                }
            }
        }

        // Générer les caractéristiques de surface (arbres, végétation)
        this.worldGenerator.generateSurfaceFeatures(chunkX, chunkZ, chunkData.blocks, (x, y, z) => {
            return this.getBlockIndex(x, y, z);
            }
        }
        
        this.stats.chunksLoaded++;
        console.log(`🧱 Chunk généré: (${chunkX}, ${chunkZ})`);
        
        return chunkData;
    }
    
    // Obtenir le type de bloc à une position mondiale
    getBlockTypeAt(worldX, worldY, worldZ) {
        // Vérifier les limites
        if (worldY < 0 || worldY >= GAME_CONFIG.CHUNK_HEIGHT) {
            return BLOCK_TYPES.AIR;
        }
        
        // Calculer les coordonnées du chunk
        const chunkX = Math.floor(worldX / GAME_CONFIG.CHUNK_WIDTH);
        const chunkZ = Math.floor(worldZ / GAME_CONFIG.CHUNK_DEPTH);
        const chunkKey = `${chunkX},${chunkZ}`;
        
        // Vérifier le cache
        const cacheKey = `${worldX},${worldY},${worldZ}`;
        const cached = this.blockCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            this.stats.cacheHits++;
            return cached.blockType;
        }
        
        // Obtenir le chunk
        let chunk = this.chunks.get(chunkKey);
        if (!chunk) {
            // Générer le chunk à la demande
            chunk = this.generateChunk(chunkX, chunkZ);
            this.chunks.set(chunkKey, chunk);
            this.loadedChunks.add(chunkKey);
        }
        
        // Calculer les coordonnées locales dans le chunk
        const localX = worldX - chunkX * GAME_CONFIG.CHUNK_WIDTH;
        const localZ = worldZ - chunkZ * GAME_CONFIG.CHUNK_DEPTH;
        const index = this.getBlockIndex(localX, worldY, localZ);
        
        const blockType = chunk.blocks[index];
        
        // Mettre en cache
        this.blockCache.set(cacheKey, {
            blockType,
            timestamp: Date.now()
        });
        this.stats.cacheMisses++;
        
        return blockType;
    }
    
    // Modifier un bloc
    setBlockAt(worldX, worldY, worldZ, blockType) {
        if (worldY < 0 || worldY >= GAME_CONFIG.CHUNK_HEIGHT) {
            return false;
        }
        
        const chunkX = Math.floor(worldX / GAME_CONFIG.CHUNK_WIDTH);
        const chunkZ = Math.floor(worldZ / GAME_CONFIG.CHUNK_DEPTH);
        const chunkKey = `${chunkX},${chunkZ}`;
        
        let chunk = this.chunks.get(chunkKey);
        if (!chunk) {
            chunk = this.generateChunk(chunkX, chunkZ);
            this.chunks.set(chunkKey, chunk);
            this.loadedChunks.add(chunkKey);
        }
        
        const localX = worldX - chunkX * GAME_CONFIG.CHUNK_WIDTH;
        const localZ = worldZ - chunkZ * GAME_CONFIG.CHUNK_DEPTH;
        const index = this.getBlockIndex(localX, worldY, localZ);
        
        const oldBlockType = chunk.blocks[index];
        chunk.blocks[index] = blockType;
        
        // Invalider le cache
        const cacheKey = `${worldX},${worldY},${worldZ}`;
        this.blockCache.delete(cacheKey);
        this.groundHeightCache.delete(`${worldX},${worldZ}`);
        
        return oldBlockType;
    }
    
    // Supprimer un bloc (pour le minage)
    removeBlock(worldX, worldY, worldZ) {
        const blockType = this.getBlockTypeAt(worldX, worldY, worldZ);
        if (blockType !== BLOCK_TYPES.AIR) {
            this.setBlockAt(worldX, worldY, worldZ, BLOCK_TYPES.AIR);
            return blockType;
        }
        return 0;
    }
    
    // Placer un bloc
    placeBlock(worldX, worldY, worldZ, blockType, player) {
        // Vérifications de base
        if (blockType === BLOCK_TYPES.AIR) {
            return { success: false, reason: 'Type de bloc invalide' };
        }
        
        const currentBlock = this.getBlockTypeAt(worldX, worldY, worldZ);
        if (currentBlock !== BLOCK_TYPES.AIR) {
            return { success: false, reason: 'Position occupée' };
        }
        
        // Vérifier la distance
        const distance = Utils.distance3D(
            player.position.x, player.position.y, player.position.z,
            worldX, worldY, worldZ
        );
        
        if (distance > GAME_CONFIG.MINING_REACH) {
            return { success: false, reason: 'Trop loin' };
        }
        
        // Placer le bloc
        this.setBlockAt(worldX, worldY, worldZ, blockType);
        
        return { success: true };
    }
    
    // Obtenir la hauteur du sol à une position
    getGroundHeightAt(worldX, worldZ) {
        const cacheKey = `${worldX},${worldZ}`;
        const cached = this.groundHeightCache.get(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.height;
        }
        
        // Chercher le bloc le plus haut qui n'est pas de l'air
        let height = GAME_CONFIG.CHUNK_HEIGHT - 1;
        
        for (let y = GAME_CONFIG.CHUNK_HEIGHT - 1; y >= 0; y--) {
            const blockType = this.getBlockTypeAt(worldX, y, worldZ);
            if (blockType !== BLOCK_TYPES.AIR) {
                height = y;
                break;
            }
        }
        
        // Mettre en cache
        this.groundHeightCache.set(cacheKey, {
            height,
            timestamp: Date.now()
        });
        
        return height;
    }
    
    // Vérifier s'il y a une collision à une position
    hasCollisionAt(worldX, worldY, worldZ) {
        const blockType = this.getBlockTypeAt(Math.floor(worldX), Math.floor(worldY), Math.floor(worldZ));
        return blockType !== BLOCK_TYPES.AIR && blockType !== BLOCK_TYPES.WATER;
    }
    
    // Trouver une position de spawn sûre
    findSafeSpawnPosition() {
        // Chercher autour du spawn (0, 0)
        for (let attempts = 0; attempts < 100; attempts++) {
            const x = Math.floor(Math.random() * 200 - 100);
            const z = Math.floor(Math.random() * 200 - 100);
            const groundHeight = this.getGroundHeightAt(x, z);
            const spawnY = groundHeight + GAME_CONFIG.PLAYER_EYE_HEIGHT;
            
            // Vérifier que l'espace est libre
            if (!this.hasCollisionAt(x, spawnY, z) && !this.hasCollisionAt(x, spawnY + 1, z)) {
                return { x, y: spawnY, z };
            }
        }
        
        // Position par défaut si aucune position sûre trouvée
        return { x: 0, y: 100, z: 0 };
    }
    
    // Utilitaires
    getChunkCoordinates(worldX, worldZ) {
        return {
            x: Math.floor(worldX / GAME_CONFIG.CHUNK_WIDTH),
            z: Math.floor(worldZ / GAME_CONFIG.CHUNK_DEPTH)
        };
    }
    
    getChunksInRadius(centerChunkX, centerChunkZ, radius) {
        const chunks = [];
        
        for (let dx = -radius; dx <= radius; dx++) {
            for (let dz = -radius; dz <= radius; dz++) {
                const distance = Math.sqrt(dx * dx + dz * dz);
                if (distance <= radius) {
                    const chunkX = centerChunkX + dx;
                    const chunkZ = centerChunkZ + dz;
                    const chunkKey = `${chunkX},${chunkZ}`;
                    
                    const chunk = this.chunks.get(chunkKey);
                    if (chunk) {
                        chunks.push(chunk);
                    }
                }
            }
        }
        
        return chunks;
    }
    
    getBlockIndex(x, y, z) {
        return y * GAME_CONFIG.CHUNK_WIDTH * GAME_CONFIG.CHUNK_DEPTH + z * GAME_CONFIG.CHUNK_WIDTH + x;
    }
    
    unloadDistantChunks(playerPositions) {
        const chunksToUnload = [];
        
        this.loadedChunks.forEach(chunkKey => {
            const [chunkX, chunkZ] = chunkKey.split(',').map(Number);
            let shouldKeep = false;
            
            // Vérifier si le chunk est proche d'au moins un joueur
            for (const pos of playerPositions) {
                const playerChunk = this.getChunkCoordinates(pos.x, pos.z);
                const distance = Utils.distance2D(chunkX, chunkZ, playerChunk.x, playerChunk.z);
                
                if (distance <= this.loadDistance + 2) { // +2 pour la marge
                    shouldKeep = true;
                    break;
                }
            }
            
            if (!shouldKeep) {
                chunksToUnload.push(chunkKey);
            }
        });
        
        // Décharger les chunks
        chunksToUnload.forEach(chunkKey => {
            this.chunks.delete(chunkKey);
            this.loadedChunks.delete(chunkKey);
        });
        
        if (chunksToUnload.length > 0) {
            console.log(`🗑️ ${chunksToUnload.length} chunks déchargés`);
        }
    }
    
    cleanupCaches() {
        const now = Date.now();
        
        // Nettoyer le cache des blocs
        for (const [key, value] of this.blockCache.entries()) {
            if (now - value.timestamp > this.cacheTimeout) {
                this.blockCache.delete(key);
            }
        }
        
        // Nettoyer le cache des hauteurs
        for (const [key, value] of this.groundHeightCache.entries()) {
            if (now - value.timestamp > this.cacheTimeout) {
                this.groundHeightCache.delete(key);
            }
        }
    }
    
    // Statistiques
    getStats() {
        return {
            ...this.stats,
            chunksLoaded: this.loadedChunks.size,
            cacheSize: this.blockCache.size + this.groundHeightCache.size
        };
    }
}
