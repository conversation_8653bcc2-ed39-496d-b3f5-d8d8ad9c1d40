// ===== SYSTÈME DE CACHE-BUSTING ET VERSIONING CENTRALISÉ =====
import { VERSION_CONFIG } from './version.js';

// Vérification du système de versioning (sans rechargement automatique)
VERSION_CONFIG.forceReloadIfNeeded(); // Juste pour afficher le message, sans recharger

// Fonction pour importer avec cache-busting
async function importWithCacheBuster(modulePath) {
    const fullPath = `${modulePath}?v=${VERSION_CONFIG.CACHE_BUSTER}`;
    console.log(`📦 Import avec cache-buster: ${fullPath}`);
    return import(fullPath);
}

// Imports avec cache-busting
const { Logger } = await importWithCacheBuster('./utils/Logger.js');
const { TextureGenerator } = await importWithCacheBuster('./utils/TextureGenerator.js');
const { World } = await importWithCacheBuster('./world/World.js');
const { Player } = await importWithCacheBuster('./player/Player.js');
const { Controls } = await importWithCacheBuster('./player/Controls.js');
const { OptionsManager } = await importWithCacheBuster('./ui/OptionsManager.js');

// Initialiser le logger global
const logger = window.GameLogger;
logger.info('Tous les modules importés avec succès', { 
    version: VERSION_CONFIG.GAME_VERSION,
    cacheBuster: VERSION_CONFIG.CACHE_BUSTER
});

// Three.js est chargé globalement depuis le CDN
const THREE = window.THREE;

// Vérifier que Three.js est chargé
if (!THREE) {
    logger.error('Three.js n\'est pas chargé !');
    throw new Error('Three.js non disponible');
}

logger.info('Three.js chargé', { revision: THREE.REVISION });

// Vérification des modules - SimplexNoise n'est plus nécessaire car nous utilisons notre propre classe Noise
logger.info('Modules de génération de terrain chargés');

const scene = new THREE.Scene();
scene.background = new THREE.Color(0x87CEEB);

const canvas = document.getElementById('game');
if (!canvas) {
    logger.error('Canvas non trouvé !');
    throw new Error('Canvas manquant');
}

const renderer = new THREE.WebGLRenderer({ 
    canvas: canvas,
    antialias: true,
    alpha: false,
    powerPreference: "high-performance"
});

// Configuration initiale du renderer
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Limiter pour les performances

logger.info('Renderer créé', { 
    size: { width: window.innerWidth, height: window.innerHeight },
    pixelRatio: renderer.getPixelRatio(),
    canvas: canvas.id 
});

logger.info('Création du générateur de textures...');
const textureGenerator = new TextureGenerator();
logger.info('Générateur de textures créé');

// Créer le gestionnaire d'options EN PREMIER pour éviter les race conditions
logger.info('Création du gestionnaire d\'options...');
const optionsManager = new OptionsManager();
logger.info('Gestionnaire d\'options créé');

// Récupérer les paramètres initiaux pour les passer au constructeur du joueur
const initialSettings = {
    autoClimb: optionsManager.getSetting('autoClimb'),
    autoClimbTrees: optionsManager.getSetting('autoClimbTrees'),
    fov: optionsManager.getSetting('fov')
};
logger.info('Paramètres initiaux récupérés', initialSettings);

logger.info('Création du monde...');
const world = new World(scene, textureGenerator);
logger.info('Monde créé', { chunks: world.chunks ? Object.keys(world.chunks).length : 0 });

logger.info('Création du joueur avec paramètres initiaux...');
const player = new Player(scene, initialSettings);
logger.info('Joueur créé avec paramètres d\'escalade corrects', { 
    position: player.camera.position,
    flyMode: player.flyMode,
    autoClimb: player.autoClimb,
    autoClimbTrees: player.autoClimbTrees
});

logger.info('Création des contrôles...');
const controls = new Controls(player, document.body);
logger.info('Contrôles créés', { 
    keys: Object.keys(controls.keys || {}).length 
});

// Références globales pour le système de minage et les options
window.world = world;
window.player = player;
window.controls = controls;
window.optionsManager = optionsManager;

// Les paramètres d'escalade sont déjà appliqués via le constructeur
// Mais on applique quand même pour s'assurer de la cohérence
optionsManager.applyClimbSettings();
logger.info('Paramètres d\'escalade vérifiés après création du joueur');

// Éclairage
const light = new THREE.DirectionalLight(0xffffff, 1);
light.position.set(1, 1, 1);
scene.add(light);
scene.add(new THREE.AmbientLight(0x404040));

logger.info('Éclairage ajouté', { 
    sceneObjects: scene.children.length,
    lights: scene.children.filter(child => child.isLight).length
});

// ===== SYSTÈME DE REDIMENSIONNEMENT RESPONSIF =====

let resizeTimeout;
let lastWidth = window.innerWidth;
let lastHeight = window.innerHeight;

// Fonction de redimensionnement optimisée
function handleResize() {
    const newWidth = window.innerWidth;
    const newHeight = window.innerHeight;
    
    // Éviter les redimensionnements inutiles
    if (newWidth === lastWidth && newHeight === lastHeight) {
        return;
    }
    
    logger.info('Redimensionnement détecté', {
        oldSize: { width: lastWidth, height: lastHeight },
        newSize: { width: newWidth, height: newHeight },
        aspectRatio: (newWidth / newHeight).toFixed(3)
    });
    
    // Mettre à jour le renderer
    renderer.setSize(newWidth, newHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    
    // Mettre à jour la caméra du joueur via la méthode dédiée
    if (player && player.updateCameraAspect) {
        player.updateCameraAspect(newWidth, newHeight);
    }
    
    // Mettre à jour les éléments d'interface responsive
    updateResponsiveUI(newWidth, newHeight);
    
    // Sauvegarder les nouvelles dimensions
    lastWidth = newWidth;
    lastHeight = newHeight;
    
    // Notification pour les changements significatifs
    const aspectRatio = newWidth / newHeight;
    if (aspectRatio < 1.2) {
        showNotification('📱 Mode portrait détecté', 'info', 2000);
    } else if (aspectRatio > 2.5) {
        showNotification('🖥️ Mode ultra-large détecté', 'info', 2000);
    }
}

// Fonction pour mettre à jour l'interface responsive
function updateResponsiveUI(width, height) {
    const isSmallScreen = width < 768 || height < 600;
    const isMobile = width < 480;
    
    // Ajuster la taille des éléments d'interface
    const commands = document.getElementById('commands');
    const status = document.getElementById('status');
    const instructions = document.getElementById('instructions');
    
    if (commands) {
        if (isSmallScreen) {
            commands.style.fontSize = '10px';
            commands.style.padding = '10px';
            commands.style.maxWidth = '200px';
        } else {
            commands.style.fontSize = '12px';
            commands.style.padding = '15px';
            commands.style.maxWidth = '250px';
        }
    }
    
    if (status) {
        if (isSmallScreen) {
            status.style.fontSize = '10px';
            status.style.padding = '10px';
        } else {
            status.style.fontSize = '12px';
            status.style.padding = '15px';
        }
    }
    
    if (instructions) {
        if (isMobile) {
            instructions.style.fontSize = '12px';
            instructions.style.padding = '8px 15px';
            instructions.style.top = '10px';
        } else if (isSmallScreen) {
            instructions.style.fontSize = '13px';
            instructions.style.padding = '8px 18px';
            instructions.style.top = '15px';
        } else {
            instructions.style.fontSize = '14px';
            instructions.style.padding = '10px 20px';
            instructions.style.top = '20px';
        }
    }
    
    // Ajuster l'inventaire pour les petits écrans
    const inventory = document.getElementById('inventory');
    if (inventory) {
        if (isMobile) {
            inventory.style.gridTemplateColumns = 'repeat(6, 35px)';
            inventory.style.gap = '1px';
            inventory.style.padding = '8px';
        } else if (isSmallScreen) {
            inventory.style.gridTemplateColumns = 'repeat(7, 38px)';
            inventory.style.gap = '1px';
            inventory.style.padding = '8px';
        } else {
            inventory.style.gridTemplateColumns = 'repeat(9, 40px)';
            inventory.style.gap = '2px';
            inventory.style.padding = '10px';
        }
    }
    
    // Ajuster le crosshair
    const crosshair = document.getElementById('crosshair');
    if (crosshair) {
        if (isMobile) {
            crosshair.style.fontSize = '20px';
        } else if (isSmallScreen) {
            crosshair.style.fontSize = '22px';
        } else {
            crosshair.style.fontSize = '24px';
        }
    }
    
    logger.debug('Interface responsive mise à jour', {
        isSmallScreen,
        isMobile,
        screenType: isMobile ? 'mobile' : isSmallScreen ? 'small' : 'desktop'
    });
}

// Gestionnaire d'événements de redimensionnement avec debouncing
window.addEventListener('resize', () => {
    // Debouncing pour éviter trop d'appels
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(handleResize, 100);
});

// Gestionnaire pour les changements d'orientation sur mobile
window.addEventListener('orientationchange', () => {
    // Attendre que l'orientation soit complètement changée
    setTimeout(() => {
        handleResize();
        logger.info('Changement d\'orientation détecté');
    }, 500);
});

// Initialisation responsive au démarrage
updateResponsiveUI(window.innerWidth, window.innerHeight);

logger.info('Système de redimensionnement responsif initialisé', {
    initialSize: { width: window.innerWidth, height: window.innerHeight },
    pixelRatio: window.devicePixelRatio,
    responsive: true
});

let lastTime = 0;
let frameCount = 0;
let delta = 0.016; // Valeur par défaut pour le premier frame

function animate(currentTime = performance.now()) {
    requestAnimationFrame(animate);
    
    // Calculer le delta time réel
    delta = (currentTime - lastTime) / 1000;
    
    // Vérifier que delta est valide
    if (isNaN(delta) || delta <= 0 || delta > 0.1) {
        delta = 0.016; // Valeur par défaut
    }
    
    lastTime = currentTime;
    
    // Mettre à jour les contrôles et la physique (priorité haute)
    controls.update(delta);
    player.update(delta, world);
    
    // Mettre à jour le monde (priorité basse, ne bloque pas le rendu)
    if (frameCount % 3 === 0) { // Réduit à tous les 3 frames
        world.update(player.camera.position.x, player.camera.position.z);
    }
    
    // Mettre à jour l'interface utilisateur
    updateUI();
    
    // Debug: afficher le nombre d'objets dans la scène (réduit pour les performances)
    if (frameCount % 120 === 0) { // Toutes les 2 secondes
        logger.debug('État de la scène', {
            sceneObjects: scene.children.length,
            cameraPosition: {
                x: Math.round(player.camera.position.x * 100) / 100,
                y: Math.round(player.camera.position.y * 100) / 100,
                z: Math.round(player.camera.position.z * 100) / 100
            },
            fps: Math.round(1 / delta),
            frameCount: frameCount
        });
    }
    
    // Rendu (toujours prioritaire)
    renderer.render(scene, player.camera);
    frameCount++;
}

// Variables globales pour l'interface utilisateur
let lastFlyMode = false;
let lastBiome = '';
let notificationQueue = [];

// Système de notifications
function showNotification(message, type = 'info', duration = 3000) {
    const notificationsContainer = document.getElementById('notifications');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    notificationsContainer.appendChild(notification);
    
    // Animation d'entrée
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Animation de sortie et suppression
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// Mettre à jour l'interface utilisateur
function updateUI() {
    // Mettre à jour le statut du mode vol avec notification
    const flyModeElement = document.getElementById('fly-mode');
    const flyModeStatus = flyModeElement.querySelector('span');
    
    if (player.flyMode !== lastFlyMode) {
        if (player.flyMode) {
            flyModeElement.classList.add('active');
            flyModeStatus.textContent = 'Activé';
            showNotification('🚁 Mode vol activé', 'info');
        } else {
            flyModeElement.classList.remove('active');
            flyModeStatus.textContent = 'Désactivé';
            showNotification('🚶 Mode vol désactivé', 'info');
        }
        lastFlyMode = player.flyMode;
    }
    
    // Mettre à jour la position
    const positionElement = document.getElementById('position').querySelector('span');
    positionElement.textContent = `X: ${Math.floor(player.camera.position.x)}, Y: ${Math.floor(player.camera.position.y)}, Z: ${Math.floor(player.camera.position.z)}`;
    
    // Mettre à jour le biome si disponible avec notification de changement
    if (world && world.generator) {
        const biomeElement = document.getElementById('biome').querySelector('span');
        const biomeX = Math.floor(player.camera.position.x);
        const biomeZ = Math.floor(player.camera.position.z);
        const biomeValue = world.generator.getBiomeValue(biomeX, biomeZ);
        const biome = world.generator.getBiome(biomeValue);
        
        if (biome.name !== lastBiome && lastBiome !== '') {
            const biomeEmojis = {
                'Ocean': '🌊',
                'Beach': '🏖️',
                'Plains': '🌾',
                'Forest': '🌲',
                'Hills': '⛰️',
                'Mountains': '🏔️'
            };
            showNotification(`${biomeEmojis[biome.name] || '🌍'} Biome: ${biome.name}`, 'info');
        }
        
        biomeElement.textContent = biome.name;
        lastBiome = biome.name;
    }
    
    // Mettre à jour les FPS
    const fpsElement = document.getElementById('fps').querySelector('span');
    const fps = Math.round(1 / (delta || 0.016));
    fpsElement.textContent = fps;
    
    // Notification de performance si FPS trop bas
    if (frameCount % 300 === 0 && fps < 30) {
        showNotification('⚠️ Performances faibles détectées', 'warning');
    }
}

// Notification de bienvenue
setTimeout(() => {
    showNotification('🎮 Bienvenue dans Minecraft JS !', 'info', 4000);
    setTimeout(() => {
        showNotification('💡 Appuyez sur F pour activer le mode vol', 'info', 4000);
    }, 2000);
    setTimeout(() => {
        showNotification('⚙️ Appuyez sur F1 pour ouvrir les options', 'info', 4000);
    }, 4000);
}, 1000);

animate(performance.now()); 

// ===== PROTECTION CONTRE LA SÉLECTION DE TEXTE =====

// Empêcher la sélection de texte lors des clics multiples
document.addEventListener('selectstart', (e) => {
    // Permettre la sélection uniquement dans les champs de texte du menu d'options
    const target = e.target;
    const isOptionsInput = target.closest('#options-menu') && 
                          (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT');
    
    if (!isOptionsInput) {
        e.preventDefault();
        return false;
    }
});

// Empêcher le drag and drop qui peut causer des sélections
document.addEventListener('dragstart', (e) => {
    const target = e.target;
    const isOptionsInput = target.closest('#options-menu') && 
                          (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT');
    
    if (!isOptionsInput) {
        e.preventDefault();
        return false;
    }
});

// Empêcher la sélection lors des double/triple clics
document.addEventListener('mousedown', (e) => {
    if (e.detail > 1) { // Double clic ou plus
        const target = e.target;
        const isOptionsInput = target.closest('#options-menu') && 
                              (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT');
        
        if (!isOptionsInput) {
            e.preventDefault();
            return false;
        }
    }
});

// Nettoyer toute sélection existante lors des clics (mais pas interférer avec le minage)
document.addEventListener('click', (e) => {
    const target = e.target;
    const isOptionsInput = target.closest('#options-menu') &&
                          (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT');

    // Ne pas interférer avec les clics de minage (quand le pointeur est verrouillé)
    const isPointerLocked = document.pointerLockElement === document.body;

    if (!isOptionsInput && !isPointerLocked) {
        // Nettoyer toute sélection de texte seulement si le pointeur n'est pas verrouillé
        if (window.getSelection) {
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                selection.removeAllRanges();
            }
        }

        // Pour IE
        if (document.selection) {
            document.selection.empty();
        }
    }
});

// Protection supplémentaire pour les éléments d'inventaire
document.addEventListener('DOMContentLoaded', () => {
    // Observer pour les nouveaux éléments d'inventaire créés dynamiquement
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Protéger les slots d'inventaire et hotbar
                    if (node.classList && (node.classList.contains('inventory-slot') || node.classList.contains('hotbar-slot'))) {
                        protectElement(node);
                    }
                    
                    // Protéger les enfants aussi
                    const slots = node.querySelectorAll && node.querySelectorAll('.inventory-slot, .hotbar-slot');
                    if (slots) {
                        slots.forEach(protectElement);
                    }
                }
            });
        });
    });
    
    // Observer les changements dans le DOM
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // Protéger les éléments existants
    document.querySelectorAll('.inventory-slot, .hotbar-slot').forEach(protectElement);
});

// Fonction pour protéger un élément contre la sélection
function protectElement(element) {
    if (!element) return;
    
    // Empêcher tous les événements de sélection
    element.addEventListener('selectstart', (e) => {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });
    
    element.addEventListener('dragstart', (e) => {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });
    
    element.addEventListener('mousedown', (e) => {
        if (e.detail > 1) { // Double clic ou plus
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });
    
    // Empêcher le menu contextuel qui peut causer des sélections
    element.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        return false;
    });
}

logger.info('Protection contre la sélection de texte activée avec surveillance dynamique');

// ===== SYSTÈME DE BOUTONS DE SECOURS =====

// Fonction pour téléporter le joueur sur la surface
function emergencySpawnOnSurface() {
    logger.warn('Téléportation d\'urgence activée', {
        trigger: 'emergency_spawn',
        playerPosition: {
            x: player.camera.position.x,
            y: player.camera.position.y,
            z: player.camera.position.z
        }
    });
    
    const playerX = player.camera.position.x;
    const playerZ = player.camera.position.z;
    
    // Chercher le sol à la position actuelle du joueur
    const groundHeight = world.getGroundHeightAt(playerX, playerZ);
    
    if (groundHeight !== null) {
        const newY = groundHeight + 1 + player.eyeHeight;
        player.camera.position.y = newY;
        player.velocity.y = 0;
        player.onGround = true;
        
        logger.info('Téléportation d\'urgence réussie', {
            newPosition: { x: playerX, y: newY, z: playerZ },
            groundHeight: groundHeight,
            eyeHeight: player.eyeHeight
        });
        showNotification(`🚁 Téléportation réussie ! Nouvelle position Y: ${newY.toFixed(1)}`, 'success');
    } else {
        // Si pas de sol trouvé, utiliser une hauteur par défaut
        const defaultY = 70;
        player.camera.position.y = defaultY;
        player.velocity.y = 0;
        player.onGround = true;
        
        logger.warn('Sol non trouvé, téléportation à hauteur par défaut', {
            defaultY: defaultY,
            position: { x: playerX, y: defaultY, z: playerZ }
        });
        showNotification(`🚁 Téléportation à la hauteur par défaut Y: ${defaultY}`, 'warning');
    }
    
    // Réactiver la physique normale après la téléportation
    player.groundPhysicsDisabled = false;
    player.needsGroundCheck = false;
    
    logger.physics('Physique réactivée après téléportation', {
        groundPhysicsDisabled: player.groundPhysicsDisabled,
        needsGroundCheck: player.needsGroundCheck,
        onGround: player.onGround
    });
}

// Fonction pour réactiver la physique normale
function resetPhysics() {
    logger.warn('Réinitialisation de la physique demandée', {
        trigger: 'reset_physics',
        beforeState: {
            groundPhysicsDisabled: player.groundPhysicsDisabled,
            flyMode: player.flyMode,
            groundSearchDisabled: world.groundSearchDisabled
        }
    });
    
    // Réactiver toute la physique
    player.groundPhysicsDisabled = false;
    player.needsGroundCheck = false;
    player.flyMode = false;
    
    // Réactiver la recherche de sol dans World.js
    world.groundSearchDisabled = false;
    
    logger.physics('Physique réinitialisée', {
        afterState: {
            groundPhysicsDisabled: player.groundPhysicsDisabled,
            flyMode: player.flyMode,
            groundSearchDisabled: world.groundSearchDisabled,
            needsGroundCheck: player.needsGroundCheck
        }
    });
    
    showNotification(`⚙️ Physique normale réactivée !`, 'info');
}

// Les boutons de secours ont été remplacés par des raccourcis clavier
// Voir le menu d'aide (F9, F10, F11) pour les nouvelles commandes

// Raccourcis clavier pour les fonctions de secours (nouveaux raccourcis F9, F10, F11)
document.addEventListener('keydown', (event) => {
    // F9 pour spawn d'urgence
    if (event.key === 'F9') {
        event.preventDefault();
        logger.info('Raccourci F9 - Spawn d\'urgence', { trigger: 'keyboard_shortcut' });
        emergencySpawnOnSurface();
    }
    
    // F10 pour reset physique
    if (event.key === 'F10') {
        event.preventDefault();
        logger.info('Raccourci F10 - Reset physique', { trigger: 'keyboard_shortcut' });
        resetPhysics();
    }
    
    // F11 pour télécharger les logs
    if (event.key === 'F11') {
        event.preventDefault();
        logger.info('Raccourci F11 - Téléchargement logs', { trigger: 'keyboard_shortcut' });
        downloadLogs();
    }
});

// Fonction pour télécharger les logs manuellement
function downloadLogs() {
    logger.info('Téléchargement manuel des logs demandé', {
        trigger: 'manual_download',
        totalLogs: logger.getLogs().length
    });
    
    logger.saveNow();
    showNotification('📄 Logs téléchargés avec succès !', 'success');
}

logger.info('Système de secours initialisé', {
    shortcuts: {
        'F9': 'Spawn d\'urgence',
        'F10': 'Reset physique',
        'F11': 'Télécharger logs'
    }
});