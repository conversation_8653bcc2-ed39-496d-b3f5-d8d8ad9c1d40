// server/server.js
import express from 'express';
import http from 'http';
import path from 'path';
import { fileURLToPath } from 'url';
import { WebSocketServer } from './network/WebSocketServer.js';
import { GameManager } from './game/GameManager.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = http.createServer(app);

// Servir les fichiers du client
const clientPath = path.join(__dirname, '..', 'client');
app.use(express.static(clientPath));

// Servir les fichiers partagés
const sharedPath = path.join(__dirname, '..', 'shared');
app.use('/shared', express.static(sharedPath));

// Route par défaut pour servir index.html
app.get('/', (req, res) => {
    res.sendFile(path.join(clientPath, 'index.html'));
});

// Initialiser le gestionnaire de jeu
const gameManager = new GameManager();

// Initialiser le serveur WebSocket
const wss = new WebSocketServer(server, gameManager);

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`🚀 Serveur JScraft démarré sur http://localhost:${PORT}`);
    console.log(`📁 Fichiers client servis depuis: ${clientPath}`);
    console.log(`🔗 Fichiers partagés servis depuis: ${sharedPath}`);
    gameManager.start(); // Démarrer la boucle de jeu du serveur
});

// Gestion propre de l'arrêt du serveur
process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du serveur...');
    gameManager.stop();
    wss.close();
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Signal SIGTERM reçu, arrêt du serveur...');
    gameManager.stop();
    wss.close();
    server.close(() => {
        console.log('✅ Serveur arrêté proprement');
        process.exit(0);
    });
});
