// client/js/ui/UIManager.js
// Gestionnaire de l'interface utilisateur côté client

export class UIManager {
    constructor() {
        this.elements = {};
        this.notifications = [];
        this.isInitialized = false;
        
        this.initializeElements();
        this.setupEventListeners();
        
        console.log('🖥️ UIManager initialisé');
    }
    
    initializeElements() {
        // Récupérer tous les éléments de l'interface
        this.elements = {
            // Informations de debug
            position: document.getElementById('position'),
            fps: document.getElementById('fps'),
            chunks: document.getElementById('chunks'),
            biome: document.getElementById('biome'),
            connectionStatus: document.getElementById('connection-status'),
            playersCount: document.getElementById('players-count'),
            
            // Interface de jeu
            crosshair: document.getElementById('crosshair'),
            hotbar: document.getElementById('hotbar'),
            miningProgress: document.getElementById('mining-progress'),
            notifications: document.getElementById('notifications'),
            
            // Écrans
            connectionScreen: document.getElementById('connection-screen'),
            connectionError: document.getElementById('connection-error'),
            instructions: document.getElementById('instructions'),
            
            // Boutons
            retryConnection: document.getElementById('retry-connection'),
            
            // Conteneurs
            uiOverlay: document.getElementById('ui-overlay'),
            gameContainer: document.getElementById('game-container')
        };
        
        // Vérifier que tous les éléments essentiels sont présents
        const requiredElements = ['position', 'fps', 'connectionScreen', 'uiOverlay'];
        const missingElements = requiredElements.filter(id => !this.elements[id]);
        
        if (missingElements.length > 0) {
            console.warn('⚠️ Éléments UI manquants:', missingElements);
        }
        
        this.isInitialized = true;
    }
    
    setupEventListeners() {
        // Gestion du redimensionnement
        window.addEventListener('resize', () => this.handleResize());
        
        // Gestion de la visibilité de la page
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // Gestion des touches pour l'interface
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Clic sur les instructions pour commencer
        if (this.elements.instructions) {
            this.elements.instructions.addEventListener('click', () => {
                this.hideInstructions();
            });
        }
    }
    
    handleKeyDown(event) {
        switch (event.code) {
            case 'F3':
                event.preventDefault();
                this.toggleDebugInfo();
                break;
            case 'F11':
                event.preventDefault();
                this.toggleFullscreen();
                break;
        }
    }
    
    // Gestion des écrans
    showConnectionScreen() {
        if (this.elements.connectionScreen) {
            this.elements.connectionScreen.classList.remove('hidden');
        }
        if (this.elements.uiOverlay) {
            this.elements.uiOverlay.classList.add('hidden');
        }
    }
    
    hideConnectionScreen() {
        if (this.elements.connectionScreen) {
            this.elements.connectionScreen.classList.add('hidden');
        }
        if (this.elements.uiOverlay) {
            this.elements.uiOverlay.classList.remove('hidden');
        }
    }
    
    showConnectionError(message) {
        if (this.elements.connectionError) {
            this.elements.connectionError.classList.remove('hidden');
            const errorMessage = this.elements.connectionError.querySelector('.error-message');
            if (errorMessage) {
                errorMessage.textContent = message;
            }
        }
    }
    
    hideConnectionError() {
        if (this.elements.connectionError) {
            this.elements.connectionError.classList.add('hidden');
        }
    }
    
    showInstructions() {
        if (this.elements.instructions) {
            this.elements.instructions.classList.remove('hidden');
        }
    }
    
    hideInstructions() {
        if (this.elements.instructions) {
            this.elements.instructions.classList.add('hidden');
        }
    }
    
    // Mise à jour des informations
    updatePosition(position) {
        if (this.elements.position) {
            const span = this.elements.position.querySelector('span');
            if (span) {
                span.textContent = `${Math.round(position.x)}, ${Math.round(position.y)}, ${Math.round(position.z)}`;
            }
        }
    }
    
    updateFPS(fps) {
        if (this.elements.fps) {
            const span = this.elements.fps.querySelector('span');
            if (span) {
                span.textContent = fps.toString();
                
                // Changer la couleur selon les performances
                if (fps >= 50) {
                    span.style.color = '#00ff00'; // Vert
                } else if (fps >= 30) {
                    span.style.color = '#ffff00'; // Jaune
                } else {
                    span.style.color = '#ff0000'; // Rouge
                }
            }
        }
    }
    
    updateChunkCount(count) {
        if (this.elements.chunks) {
            const span = this.elements.chunks.querySelector('span');
            if (span) {
                span.textContent = count.toString();
            }
        }
    }
    
    updateBiome(biomeName) {
        if (this.elements.biome) {
            const span = this.elements.biome.querySelector('span');
            if (span) {
                span.textContent = biomeName;
            }
        }
    }
    
    updateConnectionStatus(isConnected) {
        if (this.elements.connectionStatus) {
            const span = this.elements.connectionStatus.querySelector('span');
            if (span) {
                span.textContent = isConnected ? 'Connecté' : 'Déconnecté';
                span.className = isConnected ? 'connected' : 'disconnected';
            }
        }
    }
    
    updatePlayerCount(count) {
        if (this.elements.playersCount) {
            const span = this.elements.playersCount.querySelector('span');
            if (span) {
                span.textContent = count.toString();
            }
        }
    }
    
    // Gestion du minage
    showMiningProgress(blockType, duration) {
        if (this.elements.miningProgress) {
            this.elements.miningProgress.classList.remove('hidden');
            
            const blockTypeElement = document.getElementById('mining-block-type');
            if (blockTypeElement) {
                blockTypeElement.textContent = blockType;
            }
            
            // Animer la barre de progression
            const progressFill = this.elements.miningProgress.querySelector('.mining-progress-fill');
            if (progressFill) {
                progressFill.style.transition = `width ${duration}ms linear`;
                progressFill.style.width = '100%';
            }
            
            // Mettre à jour le temps restant
            this.updateMiningTime(duration);
        }
    }
    
    hideMiningProgress() {
        if (this.elements.miningProgress) {
            this.elements.miningProgress.classList.add('hidden');
            
            const progressFill = this.elements.miningProgress.querySelector('.mining-progress-fill');
            if (progressFill) {
                progressFill.style.width = '0%';
                progressFill.style.transition = 'none';
            }
        }
    }
    
    updateMiningTime(timeLeft) {
        const timeElement = document.getElementById('mining-time-left');
        if (timeElement) {
            timeElement.textContent = `${(timeLeft / 1000).toFixed(1)}s`;
        }
    }
    
    // Système de notifications
    showNotification(message, type = 'info', duration = 3000) {
        if (!this.elements.notifications) return;
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Ajouter l'icône selon le type
        const icon = this.getNotificationIcon(type);
        if (icon) {
            notification.innerHTML = `${icon} ${message}`;
        }
        
        this.elements.notifications.appendChild(notification);
        
        // Animation d'apparition
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Suppression automatique
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
        
        // Stocker dans l'historique
        this.notifications.push({
            message,
            type,
            timestamp: Date.now()
        });
        
        // Limiter l'historique
        if (this.notifications.length > 50) {
            this.notifications.shift();
        }
    }
    
    getNotificationIcon(type) {
        const icons = {
            info: '💡',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            player: '👤',
            world: '🌍',
            mining: '⛏️'
        };
        
        return icons[type] || '';
    }
    
    // Gestion de l'inventaire (hotbar)
    updateHotbar(inventory, selectedSlot) {
        if (!this.elements.hotbar) return;
        
        const slots = this.elements.hotbar.querySelectorAll('.hotbar-slot');
        
        slots.forEach((slot, index) => {
            // Mettre à jour la sélection
            if (index === selectedSlot) {
                slot.classList.add('selected');
            } else {
                slot.classList.remove('selected');
            }
            
            // Mettre à jour le contenu
            const item = inventory[index];
            if (item) {
                slot.innerHTML = `
                    <div class="item-icon" style="background-color: ${this.getItemColor(item.type)}"></div>
                    <div class="item-count">${item.quantity}</div>
                `;
            } else {
                slot.innerHTML = '';
            }
        });
    }
    
    getItemColor(itemType) {
        // Couleurs simplifiées pour les items
        const colors = {
            1: '#808080', // Stone
            2: '#8B4513', // Dirt
            3: '#228B22', // Grass
            4: '#8B4513', // Wood
            5: '#228B22', // Leaves
            6: '#F4A460', // Sand
            8: '#2F2F2F', // Coal Ore
            9: '#CD853F', // Iron Ore
            10: '#FFD700', // Gold Ore
            11: '#00FFFF'  // Diamond Ore
        };
        
        return colors[itemType] || '#666666';
    }
    
    // Fonctionnalités avancées
    toggleDebugInfo() {
        const debugInfo = document.getElementById('debug-info');
        if (debugInfo) {
            debugInfo.classList.toggle('hidden');
        }
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('Impossible d\'activer le plein écran:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }
    
    // Gestion des événements
    handleResize() {
        // Ajuster l'interface selon la taille de l'écran
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        // Ajuster la taille des éléments UI si nécessaire
        if (width < 768) {
            // Mode mobile/tablette
            document.body.classList.add('mobile-ui');
        } else {
            document.body.classList.remove('mobile-ui');
        }
    }
    
    handleVisibilityChange() {
        if (document.hidden) {
            // Page cachée - réduire l'activité UI
            this.pauseAnimations();
        } else {
            // Page visible - reprendre l'activité UI
            this.resumeAnimations();
        }
    }
    
    pauseAnimations() {
        // Mettre en pause les animations CSS
        document.body.classList.add('paused');
    }
    
    resumeAnimations() {
        // Reprendre les animations CSS
        document.body.classList.remove('paused');
    }
    
    // Effets visuels
    showDamageEffect() {
        // Effet de dégâts (flash rouge sur l'écran)
        const overlay = document.createElement('div');
        overlay.className = 'damage-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 0, 0, 0.3);
            pointer-events: none;
            z-index: 10000;
            animation: damageFlash 0.3s ease-out;
        `;
        
        document.body.appendChild(overlay);
        
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 300);
    }
    
    showHealEffect() {
        // Effet de soin (flash vert)
        const overlay = document.createElement('div');
        overlay.className = 'heal-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 255, 0, 0.2);
            pointer-events: none;
            z-index: 10000;
            animation: healFlash 0.5s ease-out;
        `;
        
        document.body.appendChild(overlay);
        
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 500);
    }
    
    // Utilitaires
    createElement(tag, className, content) {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (content) element.textContent = content;
        return element;
    }
    
    // Informations de debug
    getDebugInfo() {
        return {
            isInitialized: this.isInitialized,
            notificationCount: this.notifications.length,
            elementsFound: Object.keys(this.elements).filter(key => this.elements[key]).length,
            elementsTotal: Object.keys(this.elements).length
        };
    }
    
    // Nettoyage
    dispose() {
        console.log('🧹 Nettoyage UIManager...');
        
        // Supprimer les event listeners
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        document.removeEventListener('keydown', this.handleKeyDown);
        
        // Nettoyer les notifications
        this.notifications = [];
        
        // Nettoyer les éléments
        this.elements = {};
    }
}
