# Project Architecture

This document provides a detailed overview of the JScraft project architecture, including the file structure, component descriptions, and their interactions.

## File Structure

```

## Core Components Analysis

This section breaks down the main JavaScript components of the application.

Le module `Player` est le cœur de la logique du joueur. Il est responsable de la gestion de la position, de la physique (gravité, sauts), des collisions avec le monde, et de l'état général du joueur (par exemple, `onGround`, `flyMode`). Ce fichier est un bon exemple de ce qu'on pourrait appeler un "God Object" dans ce projet, car il centralise de nombreuses responsabilités.

### `js/player/Controls.js`

Le module `Controls` est le gestionnaire d'entrées de l'application. Il capture les événements du clavier et de la souris pour contrôler le joueur et interagir avec l'environnement.

- **Responsabilités :**
  - **Gestion des entrées clavier :** Détecte les pressions sur les touches pour le mouvement (avancer, reculer, etc.), le saut, le sprint et d'autres actions comme l'activation du mode vol ou l'ouverture de l'inventaire.
  - **Gestion des entrées souris :**
    - **Rotation de la caméra :** Suit les mouvements de la souris pour faire pivoter la caméra du joueur, offrant une vue à la première personne.
    - **Minage :** Gère les clics de souris pour démarrer et arrêter l'action de minage.
    - **Verrouillage du pointeur (Pointer Lock) :** Implémente le mécanisme de verrouillage du pointeur pour une expérience de jeu immersive, où le curseur de la souris est caché et ses mouvements sont directement liés à la rotation de la caméra.
  - **Mise à jour de la vélocité :** Traduit les entrées de l'utilisateur en changements de la vélocité du joueur. Il ne modifie pas directement la position du joueur, mais plutôt sa vitesse, laissant au module `Player` le soin d'appliquer la physique et de gérer les collisions.
  - **Gestion des états :** Maintient l'état des touches (pressées/relâchées) et gère des logiques spécifiques comme les cooldowns de saut pour éviter les actions répétées non désirées.

- **Interaction avec les autres modules :**
  - **`Player` :** Le module `Controls` est étroitement lié au module `Player`. Il prend un objet `Player` dans son constructeur et modifie directement sa propriété `velocity` en fonction des actions de l'utilisateur.
  - **DOM :** Interagit avec le DOM pour capturer les événements et gérer le pointer lock.
  -   **`window.world` :** Accède à l'objet global `world` pour le passer à la fonction de minage du joueur.

### `js/world/World.js`

Le module `World` est le moteur de l'environnement de jeu. Il gère la création, la maintenance et l'affichage du monde voxel.

- **Responsabilités :**
  - **Génération procédurale :** Utilise `WorldGenerator` (via un `WorkerManager`) pour créer les données des chunks de manière asynchrone, évitant de bloquer le thread principal.
  - **Gestion des Chunks :**
    - **Système de file d'attente :** Maintient une file d'attente (`generationQueue`) pour générer les chunks de manière progressive. Les chunks les plus proches du joueur et ceux dans la distance de rendu sont prioritaires.
    - **Chargement/Déchargement :** Charge les chunks autour du joueur jusqu'à une `loadDistance` et ne les rend visibles que jusqu'à une `renderDistance` plus courte.
    - **Mise à jour de la visibilité :** Active ou désactive la visibilité des meshes des chunks en fonction de leur distance par rapport au joueur, optimisant ainsi les performances de rendu.
  - **Rendu :**
    - **Construction de Mesh :** Chaque `Chunk` est responsable de la construction de son propre `THREE.Mesh` à partir des données de bloc.
    - **Ajout à la scène :** Le module `World` ajoute les meshes des chunks visibles à la scène Three.js pour qu'ils soient rendus.
  - **Détection de collision et information du monde :**
    - **`getGroundHeightAt` :** Une fonction cruciale qui détermine la hauteur du sol à une coordonnée (x, z) donnée. Elle utilise un cache (`groundHeightCache`) et une valeur de secours (`lastKnownGoodGroundHeight`) pour assurer la stabilité et éviter que le joueur ne tombe à travers le monde lorsque les chunks ne sont pas encore chargés.
    - **`hasCollisionAt` :** Vérifie s'il y a un bloc solide à une position donnée, utilisé par le module `Player` pour la détection de collision.
    - **`getBlockTypeAt` / `setBlockAt` :** Permettent de lire et de modifier le type de bloc à des coordonnées spécifiques, ce qui est essentiel pour des interactions comme le minage et la construction.

- **Interaction avec les autres modules :**
  - **`Player` :** Le monde est mis à jour en fonction de la position du joueur (`update(playerX, playerZ)`). Le joueur, en retour, interroge constamment le monde pour la hauteur du sol et les collisions.
  - **`Chunk` :** Le monde gère une collection d'objets `Chunk`.
  - **`WorkerManager` :** Délègue la tâche de génération de chunk gourmande en CPU à des web workers pour maintenir la fluidité de l'application.
  -   **`Scene` (Three.js) :** Ajoute et retire les meshes des chunks de la scène.

### `js/world/Chunk.js`

Le module `Chunk` représente une section de 16x128x16 blocs du monde. C'est l'unité de base pour la génération, le rendu et la gestion des données du monde.

- **Responsabilités :**
  - **Stockage des données de bloc :** Contient un tableau (`blocks`) qui stocke le type de chaque bloc dans le chunk.
  - **Génération de terrain :**
    - **`generate` :** Remplit le tableau `blocks` avec des données de terrain en utilisant le `worldGenerator`. Il simule des biomes, des grottes, des minerais et de la végétation (arbres, herbe, etc.).
    - **Logique de biome :** La génération des blocs dépend du biome (Plaines, Forêt, Montagnes, etc.) déterminé par le `worldGenerator`.
  - **Construction de Mesh (`buildMesh`) :**
    - **Optimisation :** Ne crée de la géométrie que pour les blocs visibles (c'est-à-dire ceux qui ont au moins une face adjacente à un bloc d'air ou transparent).
    - **Instanced Rendering :** Utilise `THREE.InstancedMesh` pour des performances de rendu optimales. Au lieu de créer un objet `Mesh` distinct pour chaque bloc, il crée un seul `InstancedMesh` par type de bloc visible dans le chunk, et spécifie ensuite la position de chaque instance. Cela réduit considérablement le nombre d'appels de dessin (draw calls) au GPU.
    - **Matériaux partagés :** Les géométries et les matériaux des blocs sont stockés statiquement (`sharedGeometry`, `sharedMaterials`) et partagés entre tous les chunks pour économiser de la mémoire.
  - **Gestion du cycle de vie :**
    - **`generateMesh` :** Une méthode pour reconstruire le mesh du chunk, ce qui est essentiel lorsque des blocs sont ajoutés ou supprimés (par exemple, lors du minage). Elle nettoie correctement les anciennes ressources GPU avant de créer les nouvelles.

- **Interaction avec les autres modules :**
  - **`World` :** Le `World` crée et gère les instances de `Chunk`. Il appelle `buildMesh` lorsque le chunk doit devenir visible et `generateMesh` lorsque le chunk est modifié.
  - **`WorldGenerator` :** Le `Chunk` utilise le `worldGenerator` pour remplir ses données de bloc initiales.
  - **`TextureGenerator` :** Peut utiliser un `TextureGenerator` pour créer des matériaux avec des textures procédurales, sinon il utilise des couleurs unies comme solution de repli.

### `js/main.js` - Point d'entrée de l'application

Ce fichier est le cœur de l'application, responsable de l'initialisation de tous les systèmes principaux et de l'exécution de la boucle de jeu.

- **Responsabilités :**
  - **Initialisation :**
    - **Scène Three.js :** Crée la scène, le renderer WebGL et l'attache à l'élément canvas du DOM.
    - **Modules principaux :** Importe et instancie les modules clés : `World`, `Player`, `Controls`, et `OptionsManager`.
    - **Éclairage :** Configure l'éclairage de base de la scène (directionnel et ambiant).
  - **Boucle de jeu (`animate`) :**
    - **`requestAnimationFrame` :** Utilise cette méthode pour un rendu fluide et efficace.
    - **Calcul du Delta Time :** Calcule le temps écoulé entre chaque image pour garantir que la physique et les mouvements sont indépendants de la fréquence d'images.
    - **Ordre de mise à jour :** Appelle les méthodes `update` des différents modules dans un ordre logique :
      1.  `controls.update()` : Traite les entrées utilisateur en premier.
      2.  `player.update()` : Applique la physique et les mouvements au joueur.
      3.  `world.update()` : Met à jour les chunks autour du joueur (chargement/déchargement).
      4.  `renderer.render()` : Rend la scène.
  - **Gestion des événements :**
    - **Redimensionnement :** Gère le redimensionnement de la fenêtre du navigateur pour ajuster le ratio d'aspect de la caméra et la résolution du renderer.
    - **Interface utilisateur :** Met à jour les éléments de l'interface (position du joueur, FPS, etc.) et gère les notifications.

- **Interaction avec les autres modules :**
  - Il agit comme un **chef d'orchestre**, créant des instances des autres modules et les reliant entre eux. Par exemple, il passe l'instance de la `scene` au `World` et au `Player`, et l'instance du `player` aux `Controls`.

### `js/utils/Logger.js`

Le module `Logger` est un outil de débogage et de surveillance essentiel pour l'application.

- **Responsabilités :**
  - **Journalisation centralisée :** Fournit une interface unique pour enregistrer des messages de différents niveaux de sévérité (`info`, `warn`, `error`, `debug`).
  - **Catégorisation :** Permet de catégoriser les logs par contexte (par exemple, `mining`, `physics`, `chunk`) pour un filtrage et une analyse plus faciles.
  - **Interception d'erreurs globales :**
    - **`window.onerror` :** Capture les erreurs JavaScript non interceptées.
    - **`window.onunhandledrejection` :** Capture les rejets de promesses qui ne sont pas gérés.
    - **Surcharge de la console :** Intercepte les appels à `console.error` et `console.warn` pour les intégrer dans son propre système de logs.
  - **Collecte de métadonnées :** Chaque entrée de log inclut des informations contextuelles utiles comme un timestamp, l'URL, l'ID de session, et une trace de la pile (stack trace).
  - **Exportation de logs :**
    - **`saveToFile` :** Peut générer un fichier HTML complet et interactif contenant tous les logs de la session. Ce rapport inclut des statistiques, des filtres et une mise en forme colorée pour une lisibilité maximale.
    - **Sauvegarde automatique :** Est configuré pour sauvegarder les logs automatiquement avant que la page ne se ferme (`beforeunload`), garantissant que les données de la session ne sont pas perdues.

- **Interaction avec les autres modules :**
  - **Tous les modules :** Le `Logger` est conçu pour être une dépendance globale (souvent attachée à l'objet `window` sous le nom de `GameLogger`). N'importe quel module peut l'utiliser pour enregistrer des informations, ce qui en fait un outil de débogage omniprésent dans toute la base de code.

### `js/utils/TextureGenerator.js`

Ce module est responsable de la création procédurale des textures pour les blocs du jeu. Au lieu de charger des fichiers image, il génère les textures à la volée, ce qui réduit la taille de l'application et permet une plus grande flexibilité.

- **Responsabilités :**
  - **Génération de textures procédurales :**
    - **API Canvas :** Utilise l'API Canvas 2D pour dessiner des textures pixel par pixel sur un élément `<canvas>` en mémoire.
    - **Patterns de texture :** Contient une logique spécifique pour générer différents types de textures (herbe, pierre, bois, feuilles, etc.) en utilisant des algorithmes simples pour créer des motifs et des variations de couleur.
  - **Conversion en `THREE.Texture` :** Une fois qu'une texture est dessinée sur le canvas, elle est convertie en un objet `THREE.CanvasTexture` pour pouvoir être utilisée comme matériau dans la scène Three.js.
  - **Gestion du cache :**
    - **`textureCache` :** Maintient un `Map` des textures déjà générées. Lorsqu'une texture est demandée, il vérifie d'abord le cache pour éviter de la recréer, ce qui améliore les performances.
  - **Configuration des blocs :** Contient une définition (`blockTypes`) des différents blocs du jeu, y compris leurs couleurs et le type de pattern à utiliser pour leur texture.

- **Interaction avec les autres modules :**
  - **`Chunk` :** Le module `Chunk` utilise le `TextureGenerator` pour obtenir les matériaux nécessaires à la construction de ses meshes. Lorsque `Chunk.initializeSharedResources` est appelé, il demande au `TextureGenerator` de créer les textures pour chaque type de bloc.

### UI Modules

#### `js/ui/OptionsManager.js`

Le module `OptionsManager` est responsable de la gestion de toutes les options et de tous les parametres configurables par l'utilisateur. Il fournit une interface pour modifier les parametres graphiques, de gameplay et de performance, et assure la persistance de ces reglages entre les sessions de jeu.

**Fonctionnalites principales:**

- **Gestion des Parametres:** Definit une structure de parametres par defaut, incluant les options de rendu (FOV, distance d'affichage), les ajustements de couleur (contraste, luminosite, saturation), les performances (V-Sync) et le gameplay (sensibilite de la souris, escalade automatique).
- **Persistance des Donnees:** Charge les parametres utilisateur depuis le `localStorage` au demarrage et les y sauvegarde a chaque modification. Un systeme de versioning permet de gerer les migrations si la structure des parametres change.
- **Interface Utilisateur (UI):** Initialise les elements de l'interface du menu des options (sliders, checkboxes, selecteurs) avec les valeurs chargees et attache des ecouteurs d'evenements pour repondre aux actions de l'utilisateur.
- **Application des Parametres:** Contient des methodes pour appliquer dynamiquement chaque parametre au jeu. Par exemple:
  - `applyColorSettings()`: Modifie les filtres CSS du canvas du jeu pour ajuster l'aspect visuel.
  - `applyFOV()`: Met a jour le champ de vision de la camera du joueur.
  - `applyRenderDistance()`: Informe le module `World` de la nouvelle distance de rendu.
  - `applyMouseSensitivity()`: Ajuste la sensibilite des controles du joueur.
- **Gestion du Menu:** Controle l'ouverture et la fermeture du menu des options, en gerant le verrouillage du pointeur de la souris.

**Interactions avec les autres modules:**

- **`Player`**: Modifie directement les proprietes de la camera du joueur (FOV) et les parametres de controle (sensibilite, escalade automatique).
- **`World`**: Met a jour la distance de rendu pour le chargement et le dechargement des chunks.
- **`main.js`**: Est instancie dans le fichier principal pour etre accessible globalement.

### Player Modules

#### `js/player/Controls.js`

Le module `Controls` est responsable de la capture et de la gestion de toutes les entrees de l'utilisateur (clavier et souris) pour controler le joueur. Il agit comme un pont entre les actions du joueur et les mises a jour de l'etat du personnage dans le monde du jeu.

**Fonctionnalites principales:**

- **Gestion des Entrees Clavier:** Ecoute les evenements `keydown` et `keyup` pour suivre l'etat des touches (avancer, reculer, gauche, droite, sauter, sprinter, etc.).
- **Gestion des Entrees Souris:**
  - **Rotation de la Camera:** Utilise les evenements `mousemove` pour faire pivoter la camera du joueur. La rotation est basee sur les angles d'Euler pour un controle plus direct et stable, avec des limitations pour eviter les retournements.
  - **Verrouillage du Pointeur:** Implemente l'API Pointer Lock pour capturer le curseur de la souris, offrant une experience de type FPS. Il gere l'etat de verrouillage et libere le curseur lorsque necessaire (par exemple, en appuyant sur Echap).
  - **Actions (Minage):** Detecte les clics de souris (`mousedown`, `mouseup`) pour initier et arreter les actions du joueur, comme le minage.
- **Mise a Jour de la Velocite:** Dans sa methode `update`, il calcule un vecteur de direction base sur les touches de mouvement enfoncees. Ce vecteur est ensuite oriente en fonction de la rotation de la camera et utilise pour definir la velocite horizontale du joueur. La velocite verticale est geree pour le saut et le mode vol.
- **Gestion des Etats Specifiques:**
  - **Saut:** Implemente une logique de saut avec un cooldown pour eviter les sauts repetes et rapides.
  - **Mode Vol:** Permet d'activer/desactiver un mode de vol ou le joueur n'est plus soumis a la gravite.
  - **Inventaire:** Gere l'ouverture et la fermeture de l'inventaire.

**Interactions avec les autres modules:**

- **`Player`**: Le module `Controls` est etroitement couple au module `Player`. Il modifie directement la `velocity` du joueur, met a jour la rotation de sa `camera`, et appelle des methodes comme `startMining()`, `stopMining()`, et `toggleInventory()`.
- **`main.js`**: Le module `Controls` est instancie dans `main.js` et recoit l'instance du `Player` pour pouvoir le controler.
- **DOM**: Interagit avec le `domElement` du jeu pour capturer les evenements et demander le verrouillage du pointeur.

#### `js/player/Player.js`

Le module `Player` est au cœur de la simulation du personnage. Il encapsule l'etat, la physique et les interactions du joueur avec l'environnement du jeu. Il ne gere pas directement les entrees utilisateur, mais reagit aux changements de velocite et aux actions initiees par le module `Controls`.

**Fonctionnalites principales:**

- **Representation du Joueur:** Contient la `THREE.PerspectiveCamera` qui represente le point de vue du joueur. Definit les proprietes physiques du joueur comme la hauteur, la hauteur des yeux, et le rayon de collision.
- **Moteur Physique:**
  - **Mouvement et Gravite:** Dans sa boucle `update`, il applique la gravite a la velocite verticale du joueur lorsqu'il n'est pas en mode vol ou sur le sol. Il met a jour la position de la camera en fonction de la velocite.
  - **Detection du Sol:** Implemente une logique robuste pour determiner si le joueur est au sol. Il utilise une methode `getGroundHeightAt` du module `World` avec un systeme de cache et des methodes de fallback pour trouver la hauteur du sol directement sous le joueur.
  - **Stabilisation:** Une fois au sol, il stabilise la position verticale du joueur pour eviter les tremblements et arrete la velocite verticale.
  - **Saut:** Gere la mecanique du saut, en appliquant une impulsion verticale et en utilisant des flags pour gerer l'etat du saut.
- **Gestion des Collisions:**
  - **Collisions Horizontales:** Verifie les collisions autour du joueur dans la direction du mouvement. En cas de collision, il arrete le mouvement dans cette direction pour empecher le joueur de traverser les blocs.
  - **Collisions Verticales (Plafond):** Detecte les collisions au-dessus de la tete du joueur pour l'empecher de passer a travers le plafond.
  - **Escalade Automatique (Step-up):** Contient la logique pour permettre au joueur de monter automatiquement des blocs d'une certaine hauteur (`stepHeight`).
- **Interactions avec le Monde:**
  - **Minage:** Implemente la logique de minage. Lorsqu'elle est activee par `Controls`, la methode `updateMining` verifie a intervalles reguliers si le joueur vise un bloc a portee et, si c'est le cas, le detruit apres un certain temps.
  - **Positionnement Initial:** Au demarrage, il trouve une position de spawn valide en cherchant un sol solide pour y placer le joueur.
- **Gestion d'etat:** Maintient des etats comme `onGround`, `isJumping`, `flyMode`, `isMining` pour controler la logique de la physique et des actions.

**Interactions avec les autres modules:**

- **`World`**: Interroge constamment le module `World` pour obtenir des informations sur les blocs (`getBlockAt`, `getGroundHeightAt`) afin de gerer la physique et les collisions.
- **`Controls`**: Est controle par le module `Controls`, qui modifie sa `velocity` et declenche des actions comme le saut ou le minage.
- **`OptionsManager`**: Recoit les mises a jour des parametres (comme l'escalade automatique) pour ajuster son comportement.

### Joueur (`js/player/`)

#### `js/player/Player.js`

Ce module est le coeur de la logique du joueur. Il gere l'etat, le comportement et les interactions du joueur avec le monde.

**Fonctionnalites principales:**

- **Camera et Vue:** Utilise une `THREE.PerspectiveCamera` pour la vue a la premiere personne. La position de la camera represente la tete du joueur.
- **Physique et Mouvement:**
  - **Gravite:** Simule la gravite en appliquant une velocite vers le bas lorsque le joueur n'est pas au sol (`onGround`).
  - **Saut:** Gere la mecanique de saut, en s'assurant que le joueur ne peut sauter que lorsqu'il est au sol.
  - **Deplacements:** La logique de deplacement elle-meme est geree par `Controls.js`, mais `Player.js` applique les changements de position et de velocite qui en resultent.
  - **Mode Vol (`flyMode`):** Permet de desactiver la gravite et de se deplacer librement dans les trois dimensions.
- **Collisions:**
  - **Detection du Sol:** Verifie en permanence la hauteur du terrain sous le joueur pour determiner s'il est au sol.
  - **Collisions Laterales:** Empeche le joueur de traverser les blocs solides (murs, montagnes) en verifiant les collisions autour de lui.
  - **Escalade Automatique (`autoClimb`):** Permet au joueur de monter automatiquement des blocs d'une certaine hauteur (`stepHeight`), simulant une marche plus fluide sur un terrain accidente.
- **Interactions avec le Monde:**
  - **Minage:** Gere la logique de minage des blocs. Lorsque le joueur maintient le clic, un rayon est lance pour determiner le bloc cible. Un minuteur est alors active, et si le joueur continue de viser le meme bloc pendant la duree requise, le bloc est detruit.
  - **Placement de Blocs:** Gere la logique de placement de nouveaux blocs dans le monde.
- **Inventaire:** Contient une reference a l'inventaire du joueur, qui stocke les blocs et objets collectes.
- **Etat du Joueur:** Maintient des etats cles comme `isJumping`, `isMining`, `onGround`, etc.

**Interactions avec les autres modules:**

- **`World`**: Le module `Player` interroge constamment le `World` pour obtenir des informations sur les blocs a des positions specifiques (pour les collisions, la hauteur du sol, le minage, etc.).
- **`Controls`**: Le module `Controls` met a jour la direction de la camera et l'etat des entrees (ex: `moveForward`, `jump`). Le module `Player` utilise ces informations dans sa boucle `update` pour calculer le mouvement.
- **`Inventory`**: Le module `Player` ajoute des objets a l'inventaire lorsqu'un bloc est mine avec succes.
- **`main.js`**: Le fichier principal cree l'instance du joueur et l'ajoute a la boucle de rendu principale du jeu.

#### `js/player/Controls.js`

Ce module gere les entrees de l'utilisateur (clavier et souris) pour controler le joueur.

**Fonctionnalites principales:**

- **Gestion des Entrees Clavier:**
  - Ecoute les evenements `keydown` et `keyup` pour suivre l'etat des touches.
  - Associe les touches (par exemple, Z, Q, S, D) a des actions de mouvement (avancer, reculer, gauche, droite).
  - Gere les actions speciales comme le saut (barre d'espace) et le sprint (Maj).
- **Gestion des Entrees Souris:**
  - **Rotation de la Camera:** Utilise les evenements `mousemove` pour faire pivoter la camera du joueur. La sensibilite de la souris est configurable.
  - **Pointer Lock API:** Utilise l'API Pointer Lock pour capturer le curseur de la souris dans la fenetre de jeu, offrant une experience de type FPS ou le mouvement de la souris controle directement la vue sans que le curseur ne quitte la fenetre.
  - **Clics de Souris:** Detecte les clics de souris pour des actions comme le minage (clic gauche) ou le placement de blocs (clic droit).
- **Mise a Jour de l'Etat du Joueur:** Dans sa boucle `update`, ce module calcule la direction du mouvement en fonction des touches pressees et de l'orientation de la camera. Il met ensuite a jour la velocite du joueur en consequence.

**Interactions avec les autres modules:**

- **`Player`**: C'est le principal module avec lequel `Controls` interagit. Il modifie directement la velocite et la direction de la camera de l'objet `Player` en fonction des entrees de l'utilisateur.
- **`main.js`**: Le fichier principal cree une instance de `Controls` en lui passant l'instance du joueur et l'element DOM sur lequel ecouter les evenements.
- **`OptionsManager`**: Peut interagir avec ce module pour remapper les touches ou ajuster la sensibilite de la souris.

### Point d'entree (`js/main.js`)

Ce fichier est le point d'entree de l'application. Il orchestre l'initialisation de tous les systemes et lance la boucle de jeu.

**Fonctionnalites principales:**

- **Initialisation de Three.js:** Cree la scene (`THREE.Scene`), le renderer (`THREE.WebGLRenderer`), et la camera (qui est geree par le module `Player`).
- **Chargement des Modules:** Importe tous les modules essentiels du jeu (`Player`, `World`, `Controls`, `TextureGenerator`, `OptionsManager`, etc.).
- **Instanciation des Composants:** Cree les instances des classes principales dans le bon ordre. Par exemple, il cree le `TextureGenerator` avant le `World`, et le `Player` avant les `Controls`.
- **Configuration de la Scene:** Ajoute l'eclairage (lumiere directionnelle et ambiante) a la scene.
- **Boucle de Jeu (`animate`):** Contient la fonction `requestAnimationFrame` qui constitue la boucle de jeu principale. A chaque image, cette boucle :
  - Calcule le temps ecoule (`delta`).
  - Met a jour les controles (`controls.update`).
  - Met a jour le joueur (`player.update`).
  - Met a jour le monde (`world.update`), ce qui inclut le chargement/dechargement des chunks.
  - Rend la scene a l'ecran (`renderer.render`).
- **Gestion des Evenements Globaux:** Gere le redimensionnement de la fenetre pour ajuster la taille du renderer et le ratio d'aspect de la camera.

**Interactions avec les autres modules:**

- C'est le chef d'orchestre. Il ne repond pas aux autres modules, mais c'est lui qui les cree et les connecte entre eux. Il passe l'instance du `Player` au constructeur de `Controls`, l'instance de la `Scene` au `World`, etc.

### Monde (`js/world/`)

#### `js/world/World.js`

Le module `World` est le gestionnaire central de l'environnement de jeu. Il est responsable de la creation, de la gestion et du rendu du monde compose de `chunks`. Il orchestre la generation procedurale du terrain et optimise l'affichage pour maintenir des performances fluides.

**Fonctionnalites principales:**

- **Gestion des Chunks:**
  - **Systeme de Chunks:** Le monde est divise en `chunks` (16x128x16 blocs), qui sont les unites de base pour la generation et le rendu.
  - **Chargement Dynamique:** Charge et decharge les chunks en fonction de la position du joueur et de la `renderDistance`. Les chunks sont stockes dans une `Map` pour un acces rapide.
  - **File d'attente de Generation:** Utilise une file d'attente (`generationQueue`) pour gerer les chunks a generer. Les chunks les plus proches du joueur et ceux necessaires au rendu sont prioritaires.
- **Generation Procedurale Asynchrone:**
  - **Workers:** Delegue la generation des donnees des chunks a des `Web Workers` via un `WorkerManager`. Cela evite de bloquer le thread principal et assure une experience de jeu fluide pendant la creation du monde.
  - **`WorldGenerator`**: Utilise une instance de `WorldGenerator` (executee dans les workers) pour creer le terrain de maniere procedurale.
- **Optimisation du Rendu:**
  - **Gestion de la Visibilite:** Les chunks situes en dehors de la `renderDistance` ne sont pas rendus. Leurs `meshes` peuvent etre masques ou detruits pour economiser des ressources.
  - **Mise a jour basee sur le mouvement:** Le chargement de nouveaux chunks et la mise a jour de la visibilite sont principalement declenches lorsque le joueur se deplace vers un nouveau chunk.
- **Systemes de Cache:**
  - **Cache de Hauteur du Sol (`groundHeightCache`):** Met en cache les resultats de la recherche de la hauteur du sol pour accelerer les calculs physiques repetitifs du joueur.
  - **Cache de Visibilite (`visibilityCache`):** Pourrait etre utilise pour mettre en cache les etats de visibilite des chunks (bien que l'implementation actuelle semble recalculer a chaque fois).
- **Interface d'interaction:**
  - **`getBlockTypeAt(x, y, z)`:** Permet de recuperer le type de bloc a une coordonnee mondiale specifique.
  - **`setBlockAt(x, y, z, blockType)`:** Permet de modifier un bloc dans le monde, ce qui declenche la reconstruction du `mesh` du chunk concerne.
  - **`getGroundHeightAt(x, z)`:** Methode cruciale pour la physique du joueur, qui trouve la plus haute surface solide a une colonne (x, z) donnee.
  - **`hasCollisionAt(x, y, z)`:** Verifie s'il y a un bloc solide a proximite d'une position donnee, utilise pour la detection de collision.

**Interactions avec les autres modules:**

- **`Player`**: Le module `Player` interroge constamment `World` pour la detection des collisions, la hauteur du sol et les types de blocs environnants afin de mettre a jour sa physique.
- **`Chunk`**: `World` instancie et gere des objets `Chunk`. Il appelle la methode `buildMesh()` du chunk pour creer le maillage a afficher dans la scene.
- **`WorkerManager`**: Utilise le `WorkerManager` pour communiquer avec les workers de generation de chunks.
- **`main.js`**: Le module `World` est instancie dans `main.js` et ajoute a la boucle de jeu principale via sa methode `update()`.

#### `js/world/Chunk.js`

Le module `Chunk` represente une section verticale du monde du jeu (16 blocs de largeur, 16 de profondeur et 128 de hauteur). C'est l'unite de base pour la generation de terrain, le rendu et la gestion des donnees du monde.

**Fonctionnalites principales:**

- **Stockage des Blocs:**
  - **Tableau de Blocs:** Utilise un tableau unidimensionnel (`blocks`) pour stocker les types de blocs pour chaque coordonnee (x, y, z) a l'interieur du chunk. L'index est calcule a partir des coordonnees 3D.
- **Generation de Terrain:**
  - **Auto-generation:** Lors de son initialisation avec un `WorldGenerator`, le chunk peut remplir son propre tableau de `blocks` en appelant le generateur pour chaque colonne de blocs. Il simule differents biomes, genere des grottes, des couches de pierre, de terre, et des minerais.
  - **Generation de Caracteristiques:** Contient la logique pour generer des caracteristiques complexes comme les arbres (de differents types) et autre vegetation a la surface du terrain.
- **Optimisation du Rendu (Mesh Generation):**
  - **`buildMesh()`:** C'est la fonctionnalite la plus critique du module. Au lieu de creer un `mesh` pour chaque bloc, elle construit un `THREE.Group` contenant plusieurs `THREE.InstancedMesh`.
  - **Rendu Instancie:** Pour chaque type de bloc present dans le chunk (pierre, terre, etc.), un seul `InstancedMesh` est cree. Ce `mesh` est ensuite rendu plusieurs fois a differentes positions, ce qui reduit drasticament le nombre d'appels de dessin (draw calls) et ameliore considerablement les performances.
  - **Selection des Faces Visibles (`isBlockVisible`):** Avant de generer le `mesh`, le chunk parcourt ses blocs et determine quels blocs sont visibles (c'est-a-dire adjacents a un bloc d'air ou d'eau). Seuls les blocs visibles sont inclus dans les `InstancedMesh`, evitant ainsi de rendre des millions de blocs internes caches.
- **Gestion des Ressources Partagees:**
  - **Geometrie et Materiaux Statiques:** La geometrie de base du cube (`BoxGeometry`) et les materiaux (`MeshLambertMaterial`) sont stockes dans des proprietes `static`. Cela signifie que toutes les instances de `Chunk` partagent les memes ressources, ce qui economise de la memoire.
  - **Integration avec `TextureGenerator`:** Peut utiliser le `TextureGenerator` pour creer des materiaux a partir de textures procedurales, ou se rabattre sur des couleurs de base si le generateur n'est pas fourni.
- **Manipulation des Blocs:**
  - **`getBlockAt()` et `setBlockAt()`:** Fournit des methodes pour lire et modifier le type d'un bloc a une coordonnee locale. La modification d'un bloc entraine la regeneration de son `mesh` pour mettre a jour l'affichage.

**Interactions avec les autres modules:**

- **`World`**: Le module `World` cree et gere les instances de `Chunk`. Il appelle `chunk.buildMesh()` pour obtenir l'objet `THREE.Group` a ajouter a la scene. Il utilise egalement `getBlockAt` et `setBlockAt` pour interagir avec les donnees du monde.
- **`WorldGenerator`**: Le `Chunk` utilise une instance de `WorldGenerator` pour remplir ses donnees de blocs de maniere procedurale.
- **`TextureGenerator`**: Utilise le `TextureGenerator` pour obtenir les textures des blocs lors de la creation des materiaux partages.

#### `js/world/WorldGenerator.js`

Le module `WorldGenerator` est responsable de la generation procedurale de toutes les donnees du terrain. Il n'a pas d'etat propre lie au monde (comme les chunks), mais agit comme une bibliotheque de fonctions qui, a partir d'une coordonnee et d'une graine (seed), peut determiner la structure du monde a cet endroit. Il est principalement utilise a l'interieur des `Web Workers` pour ne pas bloquer le thread principal.

**Fonctionnalites principales:**

- **Generation basee sur le Bruit:**
  - **Bruit de Simplex:** Utilise plusieurs instances de `SimplexNoise` avec des graines (`seed`) et des parametres differents pour creer une variete de motifs naturels.
  - **Couches de Bruit:** Combine plusieurs couches de bruit pour creer le terrain final :
    - `baseNoise`: Definit la forme generale et l'elevation du terrain.
    - `detailNoise`: Ajoute des variations a plus petite echelle pour rendre le terrain moins lisse.
    - `biomeNoise`: Genere des valeurs sur une grande echelle pour determiner les zones de biomes.
    - `mountainNoise`: Ajoute un relief supplementaire specifique aux biomes de montagne.
    - `caveNoise`: Utilise un bruit 3D pour sculpter des grottes a l'interieur du terrain.
    - `oreNoise`: Utilise un bruit 3D pour distribuer les differents types de minerais a des profondeurs specifiques.
- **Systeme de Biomes:**
  - **Definition des Biomes:** Definit une liste de biomes (Ocean, Plaines, Foret, Montagnes, etc.) bases sur des plages de valeurs du `biomeNoise`.
  - **Influence sur le Terrain:** Le biome a une coordonnee (x, z) influence directement la hauteur de base du terrain, l'amplitude des variations, et le type de vegetation (arbres) qui y pousse.
- **Generation de Caracteristiques Specifiques:**
  - **Grottes:** Genere des grottes en "soustrayant" de la matiere si la valeur du `caveNoise` 3D depasse un certain seuil. La generation est limitee a certaines profondeurs pour eviter les trous en surface.
  - **Minerais:** Distribue les minerais (charbon, fer, or, diamant) a differentes profondeurs en fonction de la valeur du `oreNoise`.
  - **Arbres:** Determine le type d'arbre a generer en fonction du biome.
- **Configuration Flexible:** Les parametres du generateur (amplitudes, echelles de bruit, niveau de l'eau) sont configurables, permettant de modifier facilement l'apparence du monde genere.

**Interactions avec les autres modules:**

- **`Chunk`**: Le module `Chunk` utilise une instance de `WorldGenerator` pour remplir son tableau de `blocks`. Il appelle les methodes `getHeight`, `getBiome`, `shouldHaveCave`, `getOreType`, etc., pour chaque point de sa grille afin de determiner quel bloc placer.
- **`WorkerManager` / `world-worker.js`**: Le `WorldGenerator` est generalement instancie et utilise a l'interieur d'un `Web Worker` pour effectuer les calculs lourds de generation de terrain en arriere-plan.

### Utilitaires et Gestionnaires

#### `js/utils/WorkerManager.js`

Le `WorkerManager` est un module crucial pour les performances, car il orchestre la generation des chunks en arriere-plan sans geler le thread principal. Il gere un pool de `Web Workers` pour distribuer et executer les taches de generation de terrain de maniere asynchrone.

**Fonctionnalites principales:**

- **Pool de Workers:**
  - **Initialisation:** Cree un pool de `Web Workers` au demarrage, generalement base sur le nombre de coeurs logiques disponibles (`navigator.hardwareConcurrency`). Chaque worker charge le script `js/workers/ChunkWorker.js`.
  - **Reutilisation:** Les workers sont reutilises pour traiter plusieurs taches, evitant le cout de creation/destruction de workers pour chaque chunk.
- **Gestion de File d'Attente (Queue):**
  - **File d'attente de Taches:** Maintient une file d'attente (`taskQueue`) des demandes de generation de chunks.
  - **Distribution:** Lorsqu'un worker devient disponible, le `WorkerManager` lui assigne la prochaine tache de la file d'attente.
- **Gestion des Taches Asynchrones:**
  - **Promesses:** La methode `generateChunk` retourne une `Promise` qui sera resolue lorsque le worker aura termine la generation et renvoye les donnees du chunk.
  - **Prevention des Doublons:** Verifie si une tache pour un chunk specifique est deja en cours ou en attente. Si c'est le cas, il attache les nouveaux callbacks de la `Promise` a la tache existante au lieu d'en creer une nouvelle, optimisant ainsi les ressources.
- **Communication avec les Workers:**
  - **`postMessage`:** Envoie des messages aux workers avec les coordonnees du chunk a generer.
  - **`onmessage`:** Ecoute les messages des workers. Lorsqu'un message de type `chunkGenerated` est recu, il recupere les donnees de blocs, resout la `Promise` correspondante et libere le worker pour une nouvelle tache.
- **Cycle de Vie:** Possede une methode `dispose` pour terminer tous les workers lors de la fermeture de l'application, liberant ainsi les ressources.

**Interactions avec les autres modules:**

- **`World`**: Le module `World` est le principal consommateur du `WorkerManager`. Il appelle `workerManager.generateChunk(x, z)` pour chaque nouveau chunk qui doit etre genere. La `Promise` retournee est utilisee pour creer l'objet `Chunk` avec les donnees de blocs une fois qu'elles sont pretes.
- **`ChunkWorker.js`**: C'est le script execute par chaque worker du pool. Le `WorkerManager` communique avec ces scripts pour leur assigner des taches.

#### `js/workers/ChunkWorker.js`

Ce fichier est le script execute par chaque `Web Worker` gere par le `WorkerManager`. Il est concu pour etre completement autonome, car les workers s'executent dans un thread separe et n'ont pas acces au contexte du thread principal (comme le DOM ou les autres modules JavaScript).

**Fonctionnalites principales:**

- **Environnement Isole:** Le script est auto-suffisant. Il re-implemente ou inclut directement les dependances necessaires a la generation du monde, notamment :
  - **`Noise`:** Une implementation du bruit de Perlin/Simplex.
  - **`WorldGenerator`:** Une version du generateur de monde adaptee pour fonctionner a l'interieur du worker.
- **Reception de Taches:** Le worker ecoute les messages (`onmessage`) venant du `WorkerManager`. Ces messages contiennent le type de tache (ex: `'generate'`) et les donnees necessaires (ex: les coordonnees `chunkX`, `chunkZ`).
- **Generation de Chunk:**
  - A la reception d'une tache de generation, il instancie son propre `WorldGenerator`.
  - Il boucle sur toutes les coordonnees (x, y, z) a l'interieur du chunk demande.
  - Pour chaque coordonnee, il appelle les methodes du `WorldGenerator` pour determiner le type de bloc a placer (en tenant compte de la hauteur du terrain, des biomes, des grottes, des arbres, etc.).
  - Il stocke les types de blocs dans un `Uint8Array` pour une efficacite memoire maximale.
- **Envoi des Resultats:** Une fois le tableau de blocs rempli, le worker envoie le resultat au `WorkerManager` via `postMessage`. Le message contient les donnees generees (`blocks`) ainsi que les coordonnees du chunk pour identification.

**Interactions avec les autres modules:**

- **`WorkerManager`**: C'est le seul module avec lequel le `ChunkWorker` interagit, via le systeme de messagerie `postMessage`/`onmessage`. Il recoit des ordres du `WorkerManager` et lui renvoie les resultats de son travail.

### Interface Utilisateur (UI)

#### `js/ui/OptionsManager.js`

Le `OptionsManager` est le module central pour la gestion de toutes les options et parametres configurables par l'utilisateur. Il fournit une interface pour modifier les reglages du jeu et assure leur persistance entre les sessions.

**Fonctionnalites principales:**

- **Persistance des Parametres:**
  - **Chargement/Sauvegarde:** Utilise le `localStorage` du navigateur pour sauvegarder et charger les preferences de l'utilisateur.
  - **Versioning:** Integre un systeme de versioning pour les objets de parametres. Si un utilisateur a des parametres d'une ancienne version, le `OptionsManager` tente de les migrer vers la nouvelle structure pour assurer la compatibilite ascendante.
  - **Parametres par Defaut:** Definit un ensemble complet de parametres par defaut qui sont utilises si aucune sauvegarde n'existe ou si la sauvegarde est corrompue.
- **Gestion de l'Interface des Options:**
  - **Initialisation de l'UI:** Lie les evenements (ex: `click`, `input`, `change`) aux elements HTML du panneau d'options (sliders, boutons, selecteurs).
  - **Affichage/Masquage:** Gere l'ouverture et la fermeture du menu des options (accessible via la touche F1).
  - **Mise a Jour des Valeurs:** Met a jour l'affichage des valeurs dans l'interface pour qu'elles correspondent aux parametres actuellement charges.
- **Application des Parametres:**
  - **Application en Temps Reel:** Lorsqu'un parametre est modifie dans l'interface, il est immediatement applique dans le jeu.
    - **Effets Visuels:** Modifie directement les filtres CSS (`brightness`, `contrast`, etc.) sur le canvas du jeu.
    - **Parametres de Gameplay:** Met a jour les proprietes des autres modules (ex: `mouseSensitivity` dans `Controls`, `renderDistance` dans `World`, `fov` sur la camera).
  - **Notification d'Autres Modules:** Utilise un systeme d'appel direct ou d'evenements pour informer les autres modules qu'un parametre pertinent a change (ex: `player.onSettingsUpdated()`).
- **Fonctionnalites Avancees:**
  - **Raccourcis Clavier:** Gere la personnalisation des raccourcis clavier pour des actions specifiques.
  - **Reinitialisation:** Fournit une fonction pour reinitialiser tous les parametres a leurs valeurs par defaut.

**Interactions avec les autres modules:**

- **`Player`**: Le `Player` est notifie lorsque des parametres le concernant (ex: `autoClimb`) sont modifies, afin qu'il puisse ajuster son comportement.
- **`World`**: Le `World` est notifie des changements de `renderDistance` pour ajuster le nombre de chunks a charger et a afficher.
- **`Controls`**: Le module `Controls` recupere la `mouseSensitivity` pour ajuster la vitesse de rotation de la camera.
- **`main.js` (ou module principal):** Le `OptionsManager` est generalement instancie une seule fois au demarrage de l'application.

#### `js/ui/Inventory.js`

Le module `Inventory` gere l'inventaire du joueur, y compris le stockage des objets et leur representation visuelle dans l'interface utilisateur.

**Fonctionnalites principales:**

- **Gestion des Objets:**
  - **Stockage:** Maintient un tableau (`items`) representant les objets possedes par le joueur.
  - **Manipulation:** Fournit des methodes pour ajouter (`addItem`) et supprimer (`removeItem`) des objets de l'inventaire.
- **Interface Utilisateur (UI):**
  - **Creation de la Grille:** Genere dynamiquement les emplacements (`slots`) de l'inventaire dans le DOM au demarrage.
  - **Mise a Jour Visuelle:** La methode `updateUI` redessine le contenu des emplacements de l'inventaire pour afficher les objets actuels. Dans cette version, il affiche simplement le nom de l'objet.
  - **Selection d'Emplacement:** Gere la selection de l'emplacement actif (`selectedSlot`), en le mettant visuellement en evidence dans l'interface.

**Interactions avec les autres modules:**

- **`Player`**: Le module `Player` possede une instance de `Inventory`. Il interagit avec l'inventaire pour ajouter des blocs mines ou pour utiliser l'objet actuellement selectionne.
- **`Controls`**: Les entrees de l'utilisateur (par exemple, la molette de la souris ou les touches numeriques) sont gerees par `Controls` pour changer le `selectedSlot` de l'inventaire.

#### `js/utils/TextureGenerator.js`

Le `TextureGenerator` est un module utilitaire responsable de la creation procedurale des textures pour les blocs du jeu. Au lieu de charger des fichiers d'images, il genere les textures a la volee en utilisant l'API `Canvas` du navigateur.

**Fonctionnalites principales:**

- **Generation Procedurale:**
  - **API Canvas:** Utilise un element `<canvas>` HTML pour dessiner les textures pixel par pixel ou avec des formes simples.
  - **Patterns par Type de Bloc:** Possede une logique de dessin specifique pour chaque type de bloc (herbe, pierre, bois, feuilles, etc.). Chaque `pattern` utilise une palette de couleurs et des algorithmes simples pour creer une apparence qui rappelle le bloc original de Minecraft.
  - **Flexibilite:** Permet de creer une infinite de variations de textures sans avoir besoin de ressources graphiques externes.
- **Mise en Cache:**
  - **`textureCache`:** Stocke les textures deja generees dans une `Map`. Lorsqu'une texture est demandee, le generateur verifie d'abord si elle est dans le cache pour eviter de la regenerer, optimisant ainsi les performances.
- **Integration avec Three.js:**
  - **`THREE.CanvasTexture`:** Une fois qu'une texture est dessinee sur le canvas, elle est convertie en une `THREE.CanvasTexture`.
  - **Filtrage:** Applique le filtrage `NearestFilter` pour conserver l'aspect pixelise et net des textures, fidele au style de Minecraft.

**Interactions avec les autres modules:**

- **`Chunk`**: Le module `Chunk` utilise le `TextureGenerator` pour obtenir les materiaux (`THREE.Material`) necessaires a la construction des `InstancedMesh`. Il demande une texture pour chaque type de bloc visible et l'applique au materiau correspondant.

#### `js/utils/SimplexNoise.js`

Ce module fournit une implementation de l'algorithme de bruit de Simplex. Le bruit de Simplex est une fonction de bruit coherent (gradient noise) qui est essentielle pour la generation de contenu procedural a l'aspect naturel, comme le terrain, les textures, etc.

**Fonctionnalites principales:**

- **Generation de Bruit Coherent:** Produit des valeurs de bruit pseudo-aleatoires qui varient de maniere continue et lisse dans l'espace. Contrairement a un bruit purement aleatoire, des points proches en entree produiront des valeurs de sortie proches.
- **Bruit 2D et 3D:**
  - **`noise2D(x, y)`:** Genere une valeur de bruit pour une coordonnee 2D. Utilise principalement pour la generation de cartes de hauteur (heightmaps) et de biomes.
  - **`noise3D(x, y, z)`:** Genere une valeur de bruit pour une coordonnee 3D. Utilise pour des structures volumetriques comme les grottes ou la distribution de minerais.
- **Initialisation par Graine (`seed`):** La classe peut etre initialisee avec une `seed`. Cela garantit que la sequence de valeurs de bruit generee est deterministe et reproductible. Deux instances de `SimplexNoise` avec la meme graine produiront exactement le meme resultat pour les memes entrees, ce qui est fondamental pour pouvoir regenerer un monde a l'identique.
- **Interpolation Lisse:** Utilise une fonction `smootherstep` pour interpoler entre les gradients de bruit, ce qui elimine les artefacts visuels et produit des transitions douces.

**Interactions avec les autres modules:**

- **`WorldGenerator`**: C'est le principal consommateur de ce module. Le `WorldGenerator` cree plusieurs instances de `SimplexNoise` avec des graines et des parametres differents pour generer les differentes couches de bruit (terrain de base, details, biomes, montagnes, grottes, etc.).
- **`ChunkWorker.js`**: Le script du worker inclut sa propre implementation ou une copie de cet algorithme pour pouvoir generer le terrain de maniere autonome.

#### `js/utils/SimplexNoise.js`

Ce module fournit une implementation de l'algorithme de bruit de Simplex. Le bruit de Simplex est une fonction de bruit coherent (gradient noise) qui est essentielle pour la generation de contenu procedural a l'aspect naturel, comme le terrain, les textures, etc.

**Fonctionnalites principales:**

- **Generation de Bruit Coherent:** Produit des valeurs de bruit pseudo-aleatoires qui varient de maniere continue et lisse dans l'espace. Contrairement a un bruit purement aleatoire, des points proches en entree produiront des valeurs de sortie proches.
- **Bruit 2D et 3D:**
  - **`noise2D(x, y)`:** Genere une valeur de bruit pour une coordonnee 2D. Utilise principalement pour la generation de cartes de hauteur (heightmaps) et de biomes.
  - **`noise3D(x, y, z)`:** Genere une valeur de bruit pour une coordonnee 3D. Utilise pour des structures volumetriques comme les grottes ou la distribution de minerais.
- **Initialisation par Graine (`seed`):** La classe peut etre initialisee avec une `seed`. Cela garantit que la sequence de valeurs de bruit generee est deterministe et reproductible. Deux instances de `SimplexNoise` avec la meme graine produiront exactement le meme resultat pour les memes entrees, ce qui est fondamental pour pouvoir regenerer un monde a l'identique.
- **Interpolation Lisse:** Utilise une fonction `smootherstep` pour interpoler entre les gradients de bruit, ce qui elimine les artefacts visuels et produit des transitions douces.

**Interactions avec les autres modules:**

- **`WorldGenerator`**: C'est le principal consommateur de ce module. Le `WorldGenerator` cree plusieurs instances de `SimplexNoise` avec des graines et des parametres differents pour generer les differentes couches de bruit (terrain de base, details, biomes, montagnes, grottes, etc.).
- **`ChunkWorker.js`**: Le script du worker inclut sa propre implementation ou une copie de cet algorithme pour pouvoir generer le terrain de maniere autonome.

**Role:**
`main.js` is the primary script that bootstraps the entire application. It is responsible for initializing the game environment, loading essential modules, and starting the main game loop.

**Key Responsibilities:**

1.  **Module Loading:**
    *   It uses a dynamic import system with a cache-busting mechanism (`importWithCacheBuster`) to ensure the latest versions of modules are loaded. This is controlled by `js/version.js`.
    *   It loads critical components such as `Logger`, `TextureGenerator`, `World`, `Player`, `Controls`, and `OptionsManager`.

2.  **Three.js Initialization:**
    *   It initializes the core Three.js components: `Scene`, `WebGLRenderer`, and attaches the renderer to the HTML canvas.
    *   It sets up basic scene properties like the background color and lighting (`DirectionalLight`, `AmbientLight`).

3.  **Game Object Instantiation:**
    *   It creates instances of all major game objects:
        *   `TextureGenerator`: For creating block textures.
        *   `OptionsManager`: To manage user settings.
        *   `World`: The container for all game chunks.
        *   `Player`: The user's in-game avatar and camera.
        *   `Controls`: The input handler for player movement and actions.
    *   It establishes global references (`window.world`, `window.player`, etc.) for easy access from debug scripts and other modules, although this is a practice that could be improved with dependency injection.

4.  **Game Loop (`animate` function):**
    *   It contains the main `requestAnimationFrame` loop that drives the game.
    *   Inside the loop, it calculates the `delta` time for frame-rate independent physics and updates.
    *   It orchestrates the updates of different components in a specific order:
        1.  `controls.update()`: Processes player input.
        2.  `player.update()`: Applies physics and updates the player's state.
        3.  `world.update()`: Loads/unloads chunks based on player position (throttled for performance).
        4.  `updateUI()`: Refreshes on-screen display elements (position, FPS, etc.).
        5.  `renderer.render()`: Renders the final scene to the canvas.

5.  **UI Management and Responsiveness:**
    *   It includes a sophisticated responsive design system (`handleResize`, `updateResponsiveUI`) that adjusts the UI layout based on screen size and orientation.
    *   It manages UI notifications (`showNotification`) to provide feedback to the user (e.g., fly mode enabled, biome changes).
    *   It handles the logic for updating dynamic UI elements like FPS, player coordinates, and current biome.

6.  **Event Handling:**
    *   It sets up event listeners for window resizing and orientation changes.
    *   It includes protections against unwanted text selection during gameplay.
.qodo/
.trae/
  rules/
    project_rules.md
.vscode/
  settings.json
CHANGELOG.md
CORRECTIONS_APPLIQUEES.md
CORRECTIONS_DEFINITIVES.md
CORRECTIONS_MINAGE_LOGS.md
CORRECTIONS_TERRAIN.md
CORRECTIONS_TERRAIN_DEFINITIVES.md
CORRECTION_ENVOLS_DEFINITIVE.md
GUIDE_UTILISATION.md
RAPPORT_IA_COMPLET.md
README-kill-servers.md
RESOLUTION_FINALE_COMPLETE.md
RESUME_FINAL_CORRECTIONS.md
SOLUTION_FINALE.md
corrections.txt
css/
  mining-ui.css
  style.css
debug-immediate-mining.js
debug-simple-mining.js
debug.html
diagnostic-logs.js
diagnostic-temps-reel.js
diagnostic-terrain.js
erreurs.txt
index.html
js/
  main.js
  player/
    Controls.js
    Player.js
  ui/
    Inventory.js
    OptionsManager.js
  utils/
    Logger.js
    Noise.js
    PreloadManager.js
    SimplexNoise.js
    SmartLogger.js
    Storage.js
    TextureGenerator.js
    WorkerManager.js
  version.js
  workers/
    ChunkWorker.js
  world/
    Chunk.js
    World.js
    WorldGenerator.js
kill-servers.bat
kill-servers.ps1
minage-correctif.js
package-lock.json
package.json
server.js
test-corrections-immediat.html
test-corrections.html
test-envols-correction.js
test-logs-analysis.js
test-mining-fix.html
test-terrain-fix.html
test.html
verification-complete.js
verification-corrections.js
verification-finale.js
```