// client/js/player/RemotePlayer.js
// Représentation des autres joueurs côté client
import { GAME_CONFIG, PLAYER_STATES } from '../../../shared/constants.js';

export class RemotePlayer {
    constructor(playerId, scene) {
        this.playerId = playerId;
        this.scene = scene;
        
        // État du joueur
        this.position = { x: 0, y: 0, z: 0 };
        this.velocity = { x: 0, y: 0, z: 0 };
        this.rotation = { x: 0, y: 0 };
        this.state = PLAYER_STATES.IDLE;
        this.health = 100;
        
        // Interpolation pour un mouvement fluide
        this.targetPosition = { x: 0, y: 0, z: 0 };
        this.targetRotation = { x: 0, y: 0 };
        this.interpolationSpeed = 0.15;
        
        // Représentation visuelle
        this.mesh = null;
        this.nameTag = null;
        this.healthBar = null;
        
        // Animation
        this.animationMixer = null;
        this.animations = new Map();
        this.currentAnimation = null;
        
        // Timestamps
        this.lastUpdate = Date.now();
        this.lastStateChange = Date.now();
        
        this.createVisualRepresentation();
        console.log(`👤 RemotePlayer créé: ${playerId}`);
    }
    
    createVisualRepresentation() {
        // Créer un groupe pour le joueur
        this.mesh = new THREE.Group();
        
        // Corps du joueur (cube simple pour commencer)
        const bodyGeometry = new THREE.BoxGeometry(0.6, 1.8, 0.3);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.9; // Centrer le corps
        body.castShadow = true;
        this.mesh.add(body);
        
        // Tête du joueur
        const headGeometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB3 });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.55; // Au-dessus du corps
        head.castShadow = true;
        this.mesh.add(head);
        
        // Bras (optionnel pour plus de détail)
        this.createLimbs();
        
        // Tag avec le nom du joueur
        this.createNameTag();
        
        // Barre de vie
        this.createHealthBar();
        
        // Ajouter à la scène
        this.scene.add(this.mesh);
        
        console.log(`🎨 Représentation visuelle créée pour ${this.playerId}`);
    }
    
    createLimbs() {
        // Bras gauche
        const leftArmGeometry = new THREE.BoxGeometry(0.25, 1.2, 0.25);
        const armMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB3 });
        const leftArm = new THREE.Mesh(leftArmGeometry, armMaterial);
        leftArm.position.set(-0.425, 0.9, 0);
        leftArm.castShadow = true;
        this.mesh.add(leftArm);
        
        // Bras droit
        const rightArm = new THREE.Mesh(leftArmGeometry, armMaterial);
        rightArm.position.set(0.425, 0.9, 0);
        rightArm.castShadow = true;
        this.mesh.add(rightArm);
        
        // Jambes
        const legGeometry = new THREE.BoxGeometry(0.25, 1.2, 0.25);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        
        const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
        leftLeg.position.set(-0.15, 0.1, 0);
        leftLeg.castShadow = true;
        this.mesh.add(leftLeg);
        
        const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
        rightLeg.position.set(0.15, 0.1, 0);
        rightLeg.castShadow = true;
        this.mesh.add(rightLeg);
        
        // Stocker les références pour l'animation
        this.limbs = {
            leftArm,
            rightArm,
            leftLeg,
            rightLeg
        };
    }
    
    createNameTag() {
        // Créer un canvas pour le texte
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;
        
        // Style du texte
        context.fillStyle = 'rgba(0, 0, 0, 0.8)';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = 'white';
        context.font = 'bold 24px Arial';
        context.textAlign = 'center';
        context.fillText(this.playerId, canvas.width / 2, canvas.height / 2 + 8);
        
        // Créer la texture et le matériau
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.MeshBasicMaterial({
            map: texture,
            transparent: true,
            alphaTest: 0.1
        });
        
        // Créer le mesh du tag
        const geometry = new THREE.PlaneGeometry(2, 0.5);
        this.nameTag = new THREE.Mesh(geometry, material);
        this.nameTag.position.y = 2.5; // Au-dessus de la tête
        this.nameTag.renderOrder = 1000; // Toujours au premier plan
        
        this.mesh.add(this.nameTag);
    }
    
    createHealthBar() {
        // Barre de vie simple
        const barWidth = 1.5;
        const barHeight = 0.1;
        
        // Fond de la barre
        const backgroundGeometry = new THREE.PlaneGeometry(barWidth, barHeight);
        const backgroundMaterial = new THREE.MeshBasicMaterial({ color: 0x333333 });
        const background = new THREE.Mesh(backgroundGeometry, backgroundMaterial);
        
        // Barre de vie
        const healthGeometry = new THREE.PlaneGeometry(barWidth, barHeight);
        const healthMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
        const healthBar = new THREE.Mesh(healthGeometry, healthMaterial);
        
        // Grouper les barres
        this.healthBar = new THREE.Group();
        this.healthBar.add(background);
        this.healthBar.add(healthBar);
        this.healthBar.position.y = 2.2; // Sous le nom
        this.healthBar.renderOrder = 999;
        
        // Stocker la référence à la barre de vie pour la mise à jour
        this.healthBarMesh = healthBar;
        
        this.mesh.add(this.healthBar);
    }
    
    updateState(serverState) {
        // Mettre à jour l'état cible pour l'interpolation
        this.targetPosition = { ...serverState.position };
        this.targetRotation = { ...serverState.rotation };
        this.state = serverState.state;
        this.health = serverState.health || 100;
        
        // Mettre à jour la barre de vie
        this.updateHealthBar();
        
        // Changer d'animation si nécessaire
        this.updateAnimation();
        
        this.lastUpdate = Date.now();
    }
    
    update(deltaTime) {
        // Interpolation fluide vers la position cible
        this.interpolatePosition(deltaTime);
        this.interpolateRotation(deltaTime);
        
        // Mettre à jour les animations
        this.updateAnimations(deltaTime);
        
        // Faire face à la caméra pour le name tag
        this.updateNameTagOrientation();
        
        // Vérifier si le joueur est inactif
        this.checkInactivity();
    }
    
    interpolatePosition(deltaTime) {
        const speed = this.interpolationSpeed;
        
        // Interpolation linéaire vers la position cible
        this.position.x += (this.targetPosition.x - this.position.x) * speed;
        this.position.y += (this.targetPosition.y - this.position.y) * speed;
        this.position.z += (this.targetPosition.z - this.position.z) * speed;
        
        // Appliquer la position au mesh
        this.mesh.position.set(this.position.x, this.position.y, this.position.z);
    }
    
    interpolateRotation(deltaTime) {
        const speed = this.interpolationSpeed;
        
        // Interpolation de la rotation (attention à la rotation circulaire)
        let deltaYaw = this.targetRotation.y - this.rotation.y;
        if (deltaYaw > Math.PI) deltaYaw -= 2 * Math.PI;
        if (deltaYaw < -Math.PI) deltaYaw += 2 * Math.PI;
        
        this.rotation.x += (this.targetRotation.x - this.rotation.x) * speed;
        this.rotation.y += deltaYaw * speed;
        
        // Appliquer la rotation au mesh (seulement Y pour le corps)
        this.mesh.rotation.y = this.rotation.y;
    }
    
    updateAnimation() {
        let targetAnimation = PLAYER_STATES.IDLE;
        
        switch (this.state) {
            case PLAYER_STATES.WALKING:
                targetAnimation = PLAYER_STATES.WALKING;
                break;
            case PLAYER_STATES.RUNNING:
                targetAnimation = PLAYER_STATES.RUNNING;
                break;
            case PLAYER_STATES.JUMPING:
                targetAnimation = PLAYER_STATES.JUMPING;
                break;
            case PLAYER_STATES.MINING:
                targetAnimation = PLAYER_STATES.MINING;
                break;
        }
        
        if (targetAnimation !== this.currentAnimation) {
            this.playAnimation(targetAnimation);
        }
    }
    
    playAnimation(animationName) {
        this.currentAnimation = animationName;
        this.lastStateChange = Date.now();
        
        // Animation simple des membres
        if (this.limbs) {
            switch (animationName) {
                case PLAYER_STATES.WALKING:
                    this.startWalkAnimation();
                    break;
                case PLAYER_STATES.RUNNING:
                    this.startRunAnimation();
                    break;
                case PLAYER_STATES.MINING:
                    this.startMineAnimation();
                    break;
                default:
                    this.stopAllAnimations();
            }
        }
    }
    
    startWalkAnimation() {
        // Animation de marche simple
        this.walkAnimationTime = 0;
    }
    
    startRunAnimation() {
        // Animation de course
        this.runAnimationTime = 0;
    }
    
    startMineAnimation() {
        // Animation de minage
        this.mineAnimationTime = 0;
    }
    
    stopAllAnimations() {
        // Remettre les membres en position neutre
        if (this.limbs) {
            this.limbs.leftArm.rotation.x = 0;
            this.limbs.rightArm.rotation.x = 0;
            this.limbs.leftLeg.rotation.x = 0;
            this.limbs.rightLeg.rotation.x = 0;
        }
    }
    
    updateAnimations(deltaTime) {
        const time = Date.now() * 0.005; // Facteur de vitesse d'animation
        
        if (!this.limbs) return;
        
        switch (this.currentAnimation) {
            case PLAYER_STATES.WALKING:
                // Balancement des bras et jambes
                this.limbs.leftArm.rotation.x = Math.sin(time) * 0.5;
                this.limbs.rightArm.rotation.x = -Math.sin(time) * 0.5;
                this.limbs.leftLeg.rotation.x = -Math.sin(time) * 0.3;
                this.limbs.rightLeg.rotation.x = Math.sin(time) * 0.3;
                break;
                
            case PLAYER_STATES.RUNNING:
                // Animation plus rapide et plus ample
                this.limbs.leftArm.rotation.x = Math.sin(time * 1.5) * 0.8;
                this.limbs.rightArm.rotation.x = -Math.sin(time * 1.5) * 0.8;
                this.limbs.leftLeg.rotation.x = -Math.sin(time * 1.5) * 0.5;
                this.limbs.rightLeg.rotation.x = Math.sin(time * 1.5) * 0.5;
                break;
                
            case PLAYER_STATES.MINING:
                // Mouvement de minage avec le bras droit
                this.limbs.rightArm.rotation.x = Math.sin(time * 3) * 0.8 - 0.5;
                break;
        }
    }
    
    updateHealthBar() {
        if (this.healthBarMesh) {
            // Mettre à jour la largeur de la barre de vie
            const healthPercent = this.health / 100;
            this.healthBarMesh.scale.x = healthPercent;
            
            // Changer la couleur selon la vie
            if (healthPercent > 0.6) {
                this.healthBarMesh.material.color.setHex(0x00ff00); // Vert
            } else if (healthPercent > 0.3) {
                this.healthBarMesh.material.color.setHex(0xffff00); // Jaune
            } else {
                this.healthBarMesh.material.color.setHex(0xff0000); // Rouge
            }
        }
    }
    
    updateNameTagOrientation() {
        // Faire face à la caméra du joueur local
        if (this.nameTag && window.game?.playerController) {
            const camera = window.game.playerController.getCamera();
            if (camera) {
                this.nameTag.lookAt(camera.position);
                this.healthBar.lookAt(camera.position);
            }
        }
    }
    
    checkInactivity() {
        const now = Date.now();
        const timeSinceUpdate = now - this.lastUpdate;
        
        // Si pas de mise à jour depuis 5 secondes, considérer comme inactif
        if (timeSinceUpdate > 5000) {
            this.setInactive(true);
        } else {
            this.setInactive(false);
        }
    }
    
    setInactive(inactive) {
        if (this.mesh) {
            // Réduire l'opacité si inactif
            this.mesh.traverse((child) => {
                if (child.material) {
                    child.material.opacity = inactive ? 0.5 : 1.0;
                    child.material.transparent = inactive;
                }
            });
        }
    }
    
    // Effets visuels
    showDamageEffect() {
        // Effet de dégâts (flash rouge)
        if (this.mesh) {
            this.mesh.traverse((child) => {
                if (child.material && child.material.color) {
                    const originalColor = child.material.color.clone();
                    child.material.color.setHex(0xff0000);
                    
                    setTimeout(() => {
                        child.material.color.copy(originalColor);
                    }, 200);
                }
            });
        }
    }
    
    showHealEffect() {
        // Effet de soin (flash vert)
        if (this.mesh) {
            this.mesh.traverse((child) => {
                if (child.material && child.material.color) {
                    const originalColor = child.material.color.clone();
                    child.material.color.setHex(0x00ff00);
                    
                    setTimeout(() => {
                        child.material.color.copy(originalColor);
                    }, 200);
                }
            });
        }
    }
    
    // Informations de debug
    getDebugInfo() {
        return {
            playerId: this.playerId,
            position: this.position,
            targetPosition: this.targetPosition,
            rotation: this.rotation,
            state: this.state,
            health: this.health,
            currentAnimation: this.currentAnimation,
            lastUpdate: this.lastUpdate
        };
    }
    
    // Nettoyage
    dispose() {
        console.log(`🗑️ Nettoyage RemotePlayer: ${this.playerId}`);
        
        if (this.mesh) {
            // Supprimer de la scène
            this.scene.remove(this.mesh);
            
            // Nettoyer les matériaux et géométries
            this.mesh.traverse((child) => {
                if (child.geometry) {
                    child.geometry.dispose();
                }
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => material.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            });
            
            this.mesh = null;
        }
        
        // Nettoyer les animations
        if (this.animationMixer) {
            this.animationMixer.stopAllAction();
            this.animationMixer = null;
        }
    }
}
