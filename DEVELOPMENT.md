# 🛠️ Guide de Développement JScraft

## 🚀 Démarrage Rapide

### **Installation**
```bash
# Cloner le projet
git clone <repository-url>
cd JScraft

# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev
```

### **Accès au Jeu**
- **URL** : http://localhost:3000
- **WebSocket** : ws://localhost:3000/ws

## 📁 Structure du Projet

```
JScraft/
├── shared/                 # Code partagé
│   └── constants.js       # Constantes communes
├── server/                # Serveur Node.js
│   ├── server.js         # Point d'entrée
│   ├── network/          # WebSocket
│   ├── game/             # Logique de jeu
│   └── utils/            # Utilitaires
├── client/               # Client navigateur
│   ├── index.html        # Interface
│   ├── css/              # Styles
│   └── js/               # Code client
└── config.json           # Configuration
```

## 🔧 Scripts NPM

```bash
npm start          # Démarrer le serveur
npm run dev        # Mode développement avec auto-reload
npm test           # Lancer les tests (à implémenter)
npm run lint       # Vérifier le code (à implémenter)
npm run build      # Build de production (à implémenter)
```

## 🐛 Debug et Logs

### **Logs Serveur**
```bash
# Logs détaillés
DEBUG=* npm start

# Logs par catégorie
DEBUG=game:* npm start      # Logique de jeu
DEBUG=network:* npm start   # Réseau
DEBUG=world:* npm start     # Génération monde
```

### **Debug Client**
- **F3** : Informations de debug
- **F12** : Console développeur
- **window.game** : Accès global au jeu

### **Commandes Chat de Debug**
```
/help              # Liste des commandes
/players           # Joueurs connectés
/time              # Heure actuelle
/clear             # Effacer le chat
/tp <x> <y> <z>    # Téléportation (à implémenter)
/give <item> <qty> # Donner des items (à implémenter)
```

## 🔌 API WebSocket

### **Messages Client → Serveur**
```javascript
// Entrées joueur
{
  type: 'client:input',
  payload: {
    keys: ['KeyW', 'Space'],
    rotation: { x: 0.1, y: 1.5 },
    sprint: false,
    timestamp: **********
  }
}

// Démarrer le minage
{
  type: 'client:mineStart',
  payload: {
    position: { x: 10, y: 64, z: 5 }
  }
}

// Message de chat
{
  type: 'client:chat',
  payload: {
    message: "Salut tout le monde!"
  }
}
```

### **Messages Serveur → Client**
```javascript
// État du monde
{
  type: 'server:worldUpdate',
  payload: {
    players: [
      {
        id: 'player123',
        position: { x: 0, y: 100, z: 0 },
        velocity: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0 },
        state: 'walking',
        health: 100
      }
    ],
    serverTime: **********,
    tick: 12345
  }
}

// Données de chunk
{
  type: 'server:chunkData',
  payload: {
    x: 0,
    z: 0,
    blocks: [/* array de 32768 blocs */],
    generated: true,
    timestamp: **********
  }
}
```

## 🎮 Ajout de Fonctionnalités

### **Nouveau Type de Bloc**

1. **Ajouter la constante** (`shared/constants.js`)
```javascript
export const BLOCK_TYPES = {
  // ...
  NEW_BLOCK: 21
};
```

2. **Configurer la génération** (`server/utils/WorldGenerator.js`)
```javascript
getBlockType(x, y, z) {
  // Logique de génération
  if (condition) return BLOCK_TYPES.NEW_BLOCK;
}
```

3. **Ajouter le rendu** (`client/js/world/ClientChunk.js`)
```javascript
static initializeSharedResources() {
  // Ajouter le matériau
  ClientChunk.sharedMaterials.set(BLOCK_TYPES.NEW_BLOCK, material);
}
```

### **Nouvelle Commande Chat**

1. **Côté Client** (`client/js/ui/ChatManager.js`)
```javascript
processCommand(message) {
  switch (command) {
    case 'newcommand':
      // Traitement côté client
      break;
  }
}
```

2. **Côté Serveur** (`server/game/GameManager.js`)
```javascript
handleChat(playerId, data) {
  if (data.message.startsWith('/newcommand')) {
    // Traitement côté serveur
  }
}
```

### **Nouveau Message Réseau**

1. **Définir le type** (`shared/constants.js`)
```javascript
export const MESSAGE_TYPES = {
  // ...
  NEW_MESSAGE: 'new:message'
};
```

2. **Traitement serveur** (`server/network/WebSocketServer.js`)
```javascript
handleMessage(playerId, message) {
  switch (type) {
    case MESSAGE_TYPES.NEW_MESSAGE:
      // Traitement
      break;
  }
}
```

3. **Traitement client** (`client/js/main.js`)
```javascript
onServerMessage(message) {
  switch (type) {
    case 'new:message':
      // Traitement
      break;
  }
}
```

## 🧪 Tests et Validation

### **Tests Manuels**
- [ ] Connexion/déconnexion
- [ ] Mouvement multijoueur
- [ ] Minage et placement
- [ ] Chat fonctionnel
- [ ] Génération de chunks
- [ ] Performance réseau

### **Métriques à Surveiller**
- **Serveur** : TPS, mémoire, CPU
- **Client** : FPS, latence, chunks chargés
- **Réseau** : Bande passante, perte de paquets

## 🔧 Configuration Avancée

### **Variables d'Environnement**
```bash
PORT=3000                    # Port du serveur
NODE_ENV=development         # Mode de développement
DEBUG=game:*,network:*       # Logs de debug
MAX_PLAYERS=100              # Nombre max de joueurs
WORLD_SEED=12345            # Graine du monde
```

### **Fichier config.json**
Modifier `config.json` pour ajuster :
- Paramètres de jeu
- Limites de performance
- Options de debug
- Configuration réseau

## 🚀 Déploiement

### **Production**
```bash
# Build optimisé (à implémenter)
npm run build

# Démarrage production
NODE_ENV=production npm start
```

### **Docker** (à implémenter)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 📊 Monitoring

### **Logs Importants**
- Connexions/déconnexions joueurs
- Erreurs de génération de chunks
- Problèmes de synchronisation
- Performance dégradée

### **Métriques Clés**
- **TPS Serveur** : Doit rester proche de 20
- **Latence** : < 100ms idéal
- **Mémoire** : Surveiller les fuites
- **CPU** : Optimiser si > 80%

## 🤝 Contribution

### **Standards de Code**
- ES6+ modules
- Commentaires JSDoc
- Nommage descriptif
- Gestion d'erreurs

### **Workflow Git**
```bash
git checkout -b feature/nouvelle-fonctionnalite
# Développement
git commit -m "feat: ajouter nouvelle fonctionnalité"
git push origin feature/nouvelle-fonctionnalite
# Pull Request
```

### **Checklist PR**
- [ ] Tests passent
- [ ] Code documenté
- [ ] Performance vérifiée
- [ ] Pas de régression
- [ ] Logs appropriés

---

**🎯 Objectif** : Maintenir un code propre, performant et facilement extensible pour JScraft multijoueur.
