# Architecture Detaille du Projet JScraft

Ce document fournit une analyse detaillee de chaque fichier et module du projet JScraft, en expliquant leurs mecanismes internes, leurs responsabilites et leurs interactions.

## 1. Point d'entree (`js/main.js`)

**Responsabilite generale:** Orchestrateur principal et point de depart de l'application.

**Mecanismes et Fonctionnalites:**

- **Initialisation de l'environnement:**
  - **Importation des modules:** Utilise des importations dynamiques (`import()`) avec un mecanisme de *cache-busting* pour s'assurer que les dernieres versions des fichiers sont toujours chargees. Le *cache-busting* est realise en ajoutant un parametre de version (`?v=...`) a l'URL du module, force le navigateur a re-telecharger le fichier si la version a change.
  - **Mise en place de la scene 3D:** Cree une instance de `THREE.Scene`, configure la couleur de fond et initialise le `THREE.WebGLRenderer` avec le canvas HTML. Des optimisations sont appliquees, comme la desactivation de l'anticrenelage natif (si gere autrement) et la preference pour la haute performance.
  - **Configuration de l'eclairage:** Ajoute un eclairage directionnel pour simuler le soleil et un eclairage ambiant pour deboucher les ombres, creant ainsi une scene visuellement coherente.

- **Instanciation des modules de base:**
  - **`OptionsManager`:** Est instancie en premier pour que les parametres du joueur (comme le champ de vision ou l'escalade automatique) soient disponibles pour les autres modules des leur creation.
  - **`TextureGenerator`:** Cree le generateur de textures qui sera utilise par le monde pour creer les materiaux des blocs.
  - **`World`:** Initialise le monde en lui passant la scene et le generateur de textures.
  - **`Player`:** Cree le joueur en lui injectant les parametres recuperes de l'`OptionsManager`.
  - **`Controls`:** Initialise les controles en les liant au joueur et a l'element DOM du jeu.

- **Exposition globale:**
  - Attribue des instances cles (`world`, `player`, `controls`, `optionsManager`) a l'objet `window`. Bien que ce ne soit pas toujours une bonne pratique, cela est souvent utilise dans les projets de jeu pour faciliter le debogage depuis la console du navigateur.

- **Boucle de jeu (`animate`):**
  - Utilise `requestAnimationFrame` pour creer une boucle de rendu performante qui se synchronise avec le rafraichissement de l'ecran.
  - A chaque image, la boucle met a jour l'etat des controles, la physique du joueur, l'etat du monde (chargement/dechargement des chunks) et enfin, rend la scene a l'ecran.

- **Gestionnaire d'evenements:**
  - Implemente une fonction `handleResize` pour rendre l'application responsive. Elle met a jour la taille du renderer et le ratio d'aspect de la camera lorsque la fenetre du navigateur est redimensionnee.

**Interactions:**

- **`version.js`:** Importe la configuration de version pour le *cache-busting*.
- **Tous les modules principaux:** `main.js` est le seul module qui a une connaissance directe de tous les autres composants majeurs. Il agit comme un **injecteur de dependances** manuel, en passant les instances necessaires aux constructeurs des autres modules (par exemple, en passant l'instance `Player` a `Controls`).

## 2. Gestion de Version (`js/version.js`)

**Responsabilite generale:** Centraliser la gestion des versions et le mecanisme de *cache-busting*.

**Mecanismes et Fonctionnalites:**

- **Versioning Dynamique:**
  - Utilise `Date.now()` pour generer un identifiant de version unique a chaque rechargement de l'application (`GAME_VERSION`, `CACHE_BUSTER`, et versions des modules). Cette strategie agressive de *cache-busting* garantit que le navigateur ne sert jamais une version en cache des fichiers JavaScript, ce qui est extremement utile en developpement pour voir les changements immediatement.
  - Bien qu'efficace, cette methode est a utiliser avec prudence en production, car elle empeche toute mise en cache cote client, augmentant potentiellement les temps de chargement pour les utilisateurs recurrents.

- **Configuration Centralisee:**
  - Exporte un objet unique `VERSION_CONFIG` qui contient toutes les informations de version. Cela permet a d'autres modules (comme `main.js`) d'importer cette configuration pour construire les URLs des scripts a charger.

- **Debogage et Information:**
  - Inclut une methode `displayVersionInfo()` qui s'execute automatiquement au chargement du module et affiche des informations detaillees dans la console (version du jeu, date de mise a jour, versions des modules). C'est un outil de diagnostic precieux pour verifier rapidement quelle version du code est en cours d'execution.
  - La methode `detectCacheConflicts()` fournit des conseils specifiques au navigateur pour aider les developpeurs a vider manuellement le cache, reconnaissant que le *cache-busting* programmatique peut parfois echouer.

- **Mecanismes Desactives:**
  - Le fichier contient des fonctions (`shouldPurgeCache`, `forceReloadIfNeeded`) qui tentaient de forcer un rechargement de la page si une incoherence de cache etait detectee. Ces fonctions ont ete explicitement desactivees car elles pouvaient entrainer des boucles de rechargement infinies, un probleme courant avec de telles approches automatiques.

**Interactions:**

- **`js/main.js`:** C'est le principal consommateur de ce module. Il importe `VERSION_CONFIG` pour construire les chemins d'acces aux autres modules JavaScript, en ajoutant le `CACHE_BUSTER` comme parametre de requete.

## 3. Utilitaire de Journalisation (`js/utils/Logger.js`)

**Responsabilite generale:** Fournir un systeme de journalisation (logging) robuste et centralise pour le debogage et l'analyse post-mortem des erreurs.

**Mecanismes et Fonctionnalites:**

- **Capture d'erreurs globale:**
  - **Erreurs non capturees:** Utilise `window.addEventListener('error', ...)` pour intercepter toutes les erreurs JavaScript qui ne sont pas gerees dans un bloc `try...catch`.
  - **Promesses rejetees:** Utilise `window.addEventListener('unhandledrejection', ...)` pour capturer les erreurs provenant de promesses qui echouent sans avoir de `.catch()`.
  - **Surcharge de la console:** Remplace les methodes `console.error` et `console.warn` par ses propres fonctions. Cela lui permet d'enregistrer ces messages comme des evenements structures tout en conservant leur affichage normal dans la console (en appelant la methode originale apres traitement).

- **Journalisation structuree:**
  - Chaque entree de log est un objet structure contenant un index, un ID de session, un timestamp, un niveau de severite (`INFO`, `WARN`, `ERROR`, `DEBUG`, etc.), le message, des donnees contextuelles, l'URL de la page et une trace de la pile d'appels (`stack trace`).
  - Propose des methodes de haut niveau (`info`, `warn`, `error`, `debug`) et des methodes specialisees pour des domaines fonctionnels specifiques (`mining`, `chunk`, `player`, etc.), ce qui permet de filtrer les logs plus facilement.

- **Gestion de session:**
  - Genere un `sessionId` unique a chaque initialisation. Cet identifiant permet de regrouper tous les logs d'une meme session de jeu, ce qui est crucial pour analyser le deroulement d'evenements ayant conduit a une erreur.

- **Mecanismes de protection:**
  - **Anti-recursion:** Implemente un drapeau (`_intercepting`) pour eviter les boucles infinies. Par exemple, si une erreur se produit a l'interieur de la fonction de logging elle-meme, ce drapeau empeche le logger de tenter de logger sa propre erreur, ce qui provoquerait une nouvelle erreur, et ainsi de suite.
  - **Limite de logs:** Le systeme ne conserve en memoire qu'un nombre maximal de logs (`maxLogs`) pour eviter de saturer la memoire du navigateur lors de longues sessions de jeu.

- **Persistance (simulee):**
  - Inclut une logique de sauvegarde automatique (`autoSaveInterval`) qui, dans une implementation complete, pourrait envoyer les logs a un serveur distant ou les sauvegarder dans le `localStorage`. Cela garantit que les logs ne sont pas perdus si le navigateur se bloque.

**Interactions:**

- **`js/main.js`:** Initialise le `Logger` et l'expose globalement (via `window.logger`) pour qu'il soit accessible depuis n'importe quel autre module.
- **Tous les autres modules:** Peuvent acceder a l'instance globale du logger pour enregistrer des evenements specifiques a leur contexte, fournissant ainsi une vue d'ensemble detaillee de l'etat de l'application a tout moment.

## 4. Gestionnaire d'Options (`js/ui/OptionsManager.js`)

**Responsabilite generale:** Gerer tous les parametres configurables par l'utilisateur, leur persistance et leur application en temps reel.

**Mecanismes et Fonctionnalites:**

- **Structure de parametres par defaut:** Definit un objet `defaultSettings` qui sert de modele et de fallback. Il inclut des options pour le rendu (FOV, distance d'affichage), les ajustements de couleur (contraste, luminosite), les performances (V-Sync) et le gameplay (sensibilite de la souris, escalade automatique).

- **Persistance avec `localStorage`:**
  - **Chargement:** Au demarrage, tente de charger les parametres depuis le `localStorage`. Il utilise une cle versionnee (`minecraft-js-settings-v2`) pour faciliter les migrations futures.
  - **Sauvegarde:** Sauvegarde automatiquement l'objet de parametres dans le `localStorage` a chaque fois qu'une option est modifiee par l'utilisateur.
  - **Gestion de version et migration:** Implemente un systeme de versioning pour les parametres. Si un utilisateur a des parametres d'une ancienne version, le `OptionsManager` les migre intelligemment vers la nouvelle structure, preservant les choix de l'utilisateur tout en s'adaptant aux nouvelles options.

- **Liaison avec l'Interface Utilisateur (UI):**
  - **Initialisation:** Lit les valeurs des parametres et met a jour les elements HTML correspondants (sliders, checkboxes, menus deroulants) dans le panneau d'options.
  - **Ecouteurs d'evenements:** Attache des ecouteurs (`input`, `change`) a chaque element de l'interface. Lorsqu'un utilisateur modifie un reglage, l'ecouteur met a jour la valeur dans l'objet `settings`, appelle la methode d'application correspondante et declenche la sauvegarde.

- **Application dynamique des parametres:**
  - Possede des methodes dediees pour appliquer chaque type de parametre. Par exemple:
    - `applyColorSettings()`: Modifie les filtres CSS (`filter`) appliques au canvas du jeu pour ajuster le contraste, la luminosite et la saturation en temps reel.
    - `applyFOV()`: Met a jour la propriete `fov` de la camera du joueur et appelle `updateProjectionMatrix()`.
    - `applyRenderDistance()`: Communique la nouvelle distance de rendu au module `World`.
    - `applyMouseSensitivity()`: Met a jour la sensibilite dans le module `Controls`.

- **Gestion du menu:** Gere l'affichage et la fermeture du menu des options, en s'assurant de liberer le pointeur de la souris lorsque le menu est ouvert pour permettre l'interaction.

**Interactions:**

- **`main.js`:** Est instancie tres tot dans le processus de demarrage pour que les autres modules puissent acceder aux options des leur creation.
- **`Player`:** Recoit les mises a jour pour le champ de vision (FOV) et les options de gameplay comme l'escalade automatique.
- **`Controls`:** Recoit la mise a jour de la sensibilite de la souris.
- **`World`:** Est informe des changements de la distance de rendu.
- **DOM:** Interagit fortement avec les elements HTML du menu des options pour lire et afficher les valeurs.

## 5. Utilitaire de Generation de Textures (`js/utils/TextureGenerator.js`)

**Responsabilite generale:** Creer proceduralement les textures des blocs du jeu, eliminant ainsi le besoin de charger des fichiers d'images.

**Mecanismes et Fonctionnalites:**

- **Generation via Canvas 2D:**
  - Utilise l'API Canvas 2D de HTML5 pour dessiner les textures. Pour chaque type de bloc, il cree un element `<canvas>` en memoire, obtient son contexte 2D, et y dessine des pixels pour former une image.
  - Cette approche est legere et permet une grande flexibilite, car les textures peuvent etre modifiees par programmation.

- **Logique de Patterns Specifiques:**
  - Contient des fonctions de dessin distinctes pour chaque type de materiau (`generateGrassTexture`, `generateStoneTexture`, `generateWoodTexture`, etc.).
  - Ces fonctions utilisent des techniques simples mais efficaces pour creer des motifs reconnaissables : variations de couleur aleatoires pour le grain, lignes pour les fissures de la pierre ou les ecorces de bois, formes pour les feuilles, etc.

- **Configuration des Blocs:**
  - Definit un objet `blockTypes` qui agit comme une base de donnees de blocs. Chaque entree associe un nom de bloc a ses proprietes, notamment un tableau de couleurs et le nom du `pattern` de generation a utiliser.

- **Mise en Cache des Textures:**
  - Implemente un `textureCache` (un `Map`) pour stocker les textures deja generees. Lorsqu'une texture est demandee, le generateur verifie d'abord si elle existe dans le cache. Si c'est le cas, il la renvoie immediatement, evitant ainsi de la recreer inutilement. C'est une optimisation cruciale pour les performances, en particulier lors de la generation de nombreux chunks.

- **Integration avec Three.js:**
  - Une fois qu'un canvas est dessine, il est converti en une `THREE.CanvasTexture`.
  - Des parametres importants sont appliques a la texture, comme `magFilter = THREE.NearestFilter`, pour obtenir l'aspect pixelise et retro caracteristique de Minecraft, sans flou d'interpolation.

**Interactions:**

- **`Chunk`:** C'est le principal consommateur de ce module. Lors de la construction de son mesh, le `Chunk` demande au `TextureGenerator` de lui fournir les textures (ou plutot les materiaux qui les utilisent) pour chaque type de bloc present dans le chunk.
- **`main.js`:** Le `TextureGenerator` est instancie dans `main.js` et passe en reference au `World`, qui le transmet ensuite aux `Chunks`.

## 6. Utilitaire de Bruit Procedural (`js/utils/SimplexNoise.js`)

**Responsabilite generale:** Generer des valeurs de bruit pseudo-aleatoires et coherentes en 2D et 3D, utilisees pour creer le terrain du monde de maniere naturelle et realiste.

**Mecanismes et Fonctionnalites:**

- **Implementation du Bruit de Perlin/Simplex:**
  - La classe implemente l'algorithme de bruit de Perlin (conceptuellement similaire au bruit Simplex, bien que l'implementation utilise une interpolation lineaire et une fonction `smootherstep` typiques du bruit de Perlin classique).
  - Le bruit est deterministe pour une `seed` donnee, ce qui signifie que le meme terrain sera toujours genere a partir de la meme seed.

- **Generation de Gradients:**
  - Utilise une fonction de `hash` basee sur le sinus pour generer des vecteurs de gradient pseudo-aleatoires a chaque point entier de la grille (en 2D et 3D).
  - Ces gradients sont mis en cache dans un objet `gradients` pour eviter de les recalculer, ce qui ameliore les performances.

- **Interpolation Lisse:**
  - Calcule le produit scalaire entre les vecteurs de gradient et les vecteurs de distance pour les coins de la cellule de la grille contenant le point d'evaluation.
  - Utilise une interpolation lineaire (`lerp`) combinee a une fonction de lissage `smootherstep` (une courbe en S de degre 5) pour interpoler en douceur les influences des gradients des coins, creant ainsi un bruit continu et non anguleux.

- **Fonctions `noise2D` et `noise3D`:**
  - Expose deux methodes principales pour obtenir des valeurs de bruit a des coordonnees specifiques dans l'espace 2D ou 3D.
  - Le resultat est une valeur flottante generalement comprise entre -1 et 1, qui peut ensuite etre mappee a des hauteurs de terrain, des densites de grottes, etc.

- **Initialisation avec une Seed:**
  - Le constructeur accepte une `seed` (graine) qui initialise le generateur. Si aucune seed n'est fournie, une seed aleatoire est utilisee. Cela garantit la reproductibilite des mondes.

**Interactions:**

- **`World`:** C'est le principal utilisateur de `SimplexNoise`. Le `World` instancie cette classe avec une seed specifique et utilise les methodes `noise2D` et `noise3D` pour determiner :
  - La hauteur du terrain a n'importe quelle coordonnee (x, z).
  - La presence de grottes ou de surplombs en utilisant le bruit 3D.
  - Potentiellement, la distribution des biomes, des minerais ou d'autres caracteristiques du terrain.

## 7. Gestionnaire du Monde (`js/world/World.js`)

**Responsabilite generale:** Orchestrer la creation, la gestion et l'affichage du monde du jeu. C'est le module central qui maintient l'etat du terrain et gere son interaction avec le joueur.

**Mecanismes et Fonctionnalites:**

- **Gestion des Chunks:**
  - Le monde est divise en `Chunks` (morceaux de 16x256x16 blocs). Le `World` stocke tous les chunks actifs dans une `Map` (`this.chunks`) pour un acces rapide par coordonnees.

- **Generation Procedurale Asynchrone:**
  - Utilise un `WorkerManager` pour deleguer la generation des donnees des chunks a des Web Workers. Cela evite de bloquer le thread principal (qui gere le rendu et les interactions), assurant une experience de jeu fluide meme lors de la generation de nouveaux terrains.
  - Une file d'attente (`generationQueue`) est utilisee pour prioriser les chunks a generer. Les chunks les plus proches du joueur et dans sa zone de rendu sont prioritaires.

- **Systeme de Chargement/Dechargement Dynamique:**
  - Surveille en permanence la position du joueur. Lorsque le joueur se deplace dans un nouveau chunk, le `World` met a jour les zones a charger et a decharger.
  - Deux distances sont definies : `renderDistance` (les chunks dans ce rayon sont visibles) et `loadDistance` (un rayon plus grand pour precharger les chunks avant qu'ils ne deviennent visibles).
  - Les chunks qui sortent de la `loadDistance` sont supprimes de la scene et de la memoire pour preserver les ressources.

- **Optimisation du Rendu (Visibilite):**
  - Implemente une fonction `updateVisibility` qui parcourt les chunks et decide s'ils doivent etre visibles ou non en fonction de la `renderDistance`.
  - Un cache de visibilite (`visibilityCache`) est utilise pour eviter de recalculer la visibilite de chaque chunk a chaque frame, ce qui serait tres couteux.

- **Recherche de Hauteur de Sol (`getGroundHeightAt`):**
  - Fournit une methode pour trouver la hauteur du premier bloc non-aerien a une position (x, z) donnee. C'est essentiel pour le positionnement initial du joueur et pour la physique de base.
  - Inclut un cache (`groundHeightCache`) et des mecanismes de secours pour eviter des recherches couteuses et fournir une hauteur plausible meme si le chunk n'est pas encore totalement genere.

- **Integration avec le Generateur de Monde:**
  - Utilise une instance de `WorldGenerator` (qui lui-meme utilise `SimplexNoise`) pour obtenir les donnees brutes des blocs pour un chunk donne.

**Interactions:**

- **`main.js`:** Instancie le `World` et l'appelle dans la boucle de jeu principale (`animate`) pour mettre a jour l'etat du monde en fonction de la position du joueur.
- **`Player`:** Le `World` recoit la position du joueur pour determiner quels chunks charger/decharger. Le `Player` interroge le `World` (via `getGroundHeightAt`) pour connaitre la hauteur du sol et pour la detection de collisions.
- **`Chunk`:** Le `World` cree, stocke et detruit les instances de `Chunk`.
- **`WorldGenerator`:** Le `World` delegue la logique de generation de terrain a ce module.
- **`WorkerManager`:** Utilise pour executer la generation de chunks en arriere-plan.
- **`TextureGenerator`:** Le `World` passe la reference du `TextureGenerator` aux `Chunks` pour qu'ils puissent construire leurs meshes avec les bonnes textures.

## 8. Entite de Morceau de Monde (`js/world/Chunk.js`)

**Responsabilite generale:** Representer une section de 16x128x16 du monde, gerer les donnees des blocs qu'elle contient, et creer le maillage (mesh) 3D correspondant pour le rendu.

**Mecanismes et Fonctionnalites:**

- **Stockage des Blocs:**
  - Un `Chunk` contient un tableau unidimensionnel (`this.blocks`) qui stocke l'ID de chaque bloc dans son volume. L'acces a un bloc specifique se fait en convertissant ses coordonnees 3D (x, y, z) en un index de tableau.

- **Generation de Donnees:**
  - Au moment de sa creation, si un `worldGenerator` est fourni, le `Chunk` l'utilise pour remplir son tableau `blocks` avec les types de blocs appropries, en se basant sur le bruit procedural.

- **Construction de Maillage (Mesh Building):**
  - La fonctionnalite la plus critique du `Chunk` est sa methode `buildMesh` (ou une variante comme `createInstancedMesh`).
  - **Optimisation des Faces Cachees (Face Culling):** Pour chaque bloc, le `Chunk` verifie ses six voisins. Si un voisin est un bloc solide (non transparent), la face partagee entre les deux blocs est cachee et n'est pas ajoutee a la geometrie. Seules les faces exposees a l'air ou a des blocs transparents sont rendues. C'est l'optimisation la plus importante pour les performances.

- **Rendu Instance (Instanced Rendering):**
  - Pour optimiser davantage, le `Chunk` utilise le rendu instance (`THREE.InstancedMesh`). Au lieu de creer un objet `Mesh` distinct pour chaque bloc, il cree un seul `InstancedMesh` par type de bloc (par exemple, un pour toute la pierre, un pour toute la terre).
  - Il calcule ensuite la position de chaque bloc de ce type et ajoute une instance a l'`InstancedMesh` correspondant. Cela reduit considerablement le nombre d'appels de dessin (draw calls) a la carte graphique, ameliorant massivement les performances.

- **Ressources Partagees (Geometrie et Materiaux):**
  - Pour economiser la memoire, la geometrie de base du cube (`THREE.BoxGeometry`) et les materiaux (`THREE.MeshLambertMaterial`) sont partages entre toutes les instances de `Chunk` via des proprietes statiques (`Chunk.sharedGeometry`, `Chunk.sharedMaterials`).
  - Les materiaux sont initialises une seule fois et peuvent etre crees soit a partir de couleurs de base, soit a partir de textures generees par le `TextureGenerator`.

**Interactions:**

- **`World`:** Le `World` cree et gere le cycle de vie des `Chunks`. Il leur fournit le `worldGenerator` et le `textureGenerator`.
- **`WorldGenerator`:** Le `Chunk` l'appelle pour remplir ses donnees de blocs.
- **`TextureGenerator`:** Le `Chunk` l'utilise (via la methode statique `initializeSharedResources`) pour obtenir les textures necessaires a la creation de ses materiaux.
- **Three.js:** Le `Chunk` depend fortement de Three.js pour creer des `InstancedMesh`, des `BoxGeometry`, et des `MeshLambertMaterial`.

## 9. Generateur de Monde (`js/world/WorldGenerator.js`)

**Responsabilite generale:** Definir la logique procedurale pour la creation du terrain, y compris la forme du paysage, la distribution des biomes, la generation de grottes et la presence de minerais.

**Mecanismes et Fonctionnalites:**

- **Combinaison de Bruits Proceduraux:**
  - Le generateur utilise plusieurs instances de `SimplexNoise`, chacune avec une `seed` et une echelle differentes, pour creer differentes couches de details.
  - **`baseNoise`:** Definit la forme generale du terrain (collines, vallees).
  - **`detailNoise`:** Ajoute des variations plus fines a la surface pour un aspect plus naturel.
  - **`biomeNoise`:** Genere des valeurs a grande echelle pour determiner les zones de biomes (plaines, forets, montagnes).
  - **`caveNoise`:** Utilise un bruit 3D pour sculpter des tunnels et des cavites souterraines.
  - **`oreNoise`:** Utilise un autre bruit 3D pour placer des poches de minerais a des profondeurs specifiques.

- **Systeme de Biomes:**
  - Definit une structure de donnees (`this.biomes`) qui mappe des plages de valeurs de bruit a des types de biomes specifiques (Ocean, Plaines, Montagnes, etc.).
  - La hauteur et l'amplitude du terrain, ainsi que le type de vegetation, sont ajustes en fonction du biome a une position donnee, creant des paysages varies et coherents.

- **Generation de Caracteristiques Specifiques:**
  - **Grottes:** La methode `shouldHaveCave` utilise le `caveNoise` 3D. Si la valeur de bruit a une position (x, y, z) depasse un certain seuil, le bloc a cet endroit est defini comme de l'air, creant ainsi une grotte. Des protections sont en place pour eviter de percer la surface.
  - **Minerais:** La methode `getOreType` utilise l'`oreNoise` 3D pour placer differents types de minerais en fonction de la profondeur et d'un seuil de bruit.
  - **Arbres:** La methode `getTreeType` determine quel type d'arbre (chene, pin, etc.) doit pousser en fonction du biome.

- **Calcul de la Hauteur du Terrain (`getHeight`):**
  - C'est la fonction centrale qui combine les differentes couches de bruit pour calculer la hauteur finale du sol a une coordonnee (x, z). Elle prend en compte le biome pour moduler l'amplitude et la hauteur de base du terrain.

**Interactions:**

- **`World`:** Le `World` instancie le `WorldGenerator` et le passe aux `Chunks` (ou l'utilise via les workers) pour la generation de terrain.
- **`Chunk` (indirectement via Worker):** Le processus de generation de chunk (execute dans un worker) utilise une instance du `WorldGenerator` pour determiner le type de chaque bloc dans le volume du chunk.
- **`SimplexNoise`:** Le `WorldGenerator` est le principal consommateur des classes `SimplexNoise`, en creant plusieurs instances pour differents usages.

## 10. Gestionnaire de Workers (`js/utils/WorkerManager.js`)

**Responsabilite generale:** Gerer un pool de Web Workers pour executer des taches lourdes, comme la generation de chunks, en arriere-plan, sans bloquer le thread principal de l'application.

**Mecanismes et Fonctionnalites:**

- **Pool de Workers:**
  - Au demarrage, le `WorkerManager` cree un pool de Web Workers (le nombre est generalement base sur le nombre de coeurs logiques du processeur, via `navigator.hardwareConcurrency`).
  - Chaque worker est une instance de `js/workers/ChunkWorker.js`.

- **File d'Attente de Taches (Task Queue):**
  - Lorsqu'une nouvelle tache de generation de chunk est demandee (via la methode `generateChunk`), elle n'est pas executee immediatement. Elle est ajoutee a une file d'attente (`taskQueue`).
  - Cela permet de gerer un grand nombre de requetes de generation sans surcharger le systeme.

- **Distribution des Taches:**
  - La methode `processQueue` verifie s'il y a des workers disponibles et des taches en attente.
  - Si c'est le cas, elle prend la tache suivante de la file et l'assigne a un worker libre. La tache est alors deplacee dans une `Map` de taches actives (`activeTasks`).

- **Communication Asynchrone avec les Workers:**
  - La communication se fait via `postMessage` (pour envoyer des ordres au worker) et des ecouteurs d'evenements `onmessage` (pour recevoir les resultats).
  - La methode `generateChunk` renvoie une `Promise`. Cette promesse ne sera resolue que lorsque le worker aura termine son calcul et renvoye les donnees du chunk.

- **Gestion des Resultats:**
  - Lorsque le `WorkerManager` recoit un message d'un worker indiquant qu'un chunk a ete genere (`type: 'chunkGenerated'`), il recupere la tache correspondante dans `activeTasks`.
  - Il utilise alors la fonction `resolve` de la `Promise` associee pour renvoyer les donnees du bloc au code qui l'avait demande (generalement le `World`).
  - Le worker est alors libere et `processQueue` est appele a nouveau pour traiter la tache suivante.

- **Deduplication des Taches:**
  - Le manager verifie si une tache pour un chunk specifique est deja en cours ou en file d'attente avant d'en ajouter une nouvelle. Si c'est le cas, il attache simplement les nouvelles callbacks de `Promise` a la tache existante, evitant ainsi un travail redondant.

**Interactions:**

- **`World`:** Le `World` est le seul utilisateur du `WorkerManager`. Il l'instancie et appelle `generateChunk` pour chaque nouveau chunk a creer.
- **`ChunkWorker.js`:** C'est le script execute par chaque worker du pool. Le `WorkerManager` communique directement avec ces scripts.

## 11. Worker de Generation de Chunk (`js/workers/ChunkWorker.js`)

**Responsabilite generale:** Executer la logique de generation d'un unique chunk dans un thread separe pour ne pas bloquer l'interface utilisateur.

**Mecanismes et Fonctionnalites:**

- **Environnement Isole:**
  - Le script s'execute dans un contexte de Web Worker, ce qui signifie qu'il n'a pas acces au DOM, a l'objet `window` ou a d'autres API du thread principal. Il ne peut communiquer qu'a travers `postMessage` et `onmessage`.

- **Dependances Incluses:**
  - Comme les workers ne peuvent pas utiliser les modules ES6 (`import`/`export`) de maniere native dans tous les navigateurs et contextes, ce fichier re-implemente ou inclut directement les dependances necessaires. On y retrouve souvent une copie de la logique de `SimplexNoise` et de `WorldGenerator`.

- **Point d'Entree `onmessage`:**
  - Le worker est a l'ecoute d'evenements `message`. Lorsqu'il recoit un message du `WorkerManager` (par exemple, `{ type: 'generate', chunkX, chunkZ }`), il declenche le processus de generation.

- **Processus de Generation:**
  - Il instancie son propre `WorldGenerator`.
  - Il itere a travers chaque position (x, y, z) dans le volume du chunk qui lui a ete assigne.
  - Pour chaque position, il appelle les methodes du `WorldGenerator` pour determiner le type de bloc (en fonction de la hauteur du terrain, des grottes, des biomes, etc.).
  - Il stocke les resultats dans un tableau (`blocks`).

- **Renvoi des Donnees:**
  - Une fois le tableau `blocks` entierement rempli, le worker renvoie les donnees au thread principal en utilisant `postMessage({ type: 'chunkGenerated', ..., blocks })`.
  - Pour des raisons de performance, le tableau `blocks` peut etre un `TypedArray` (comme `Uint8Array`) et peut etre transfere en utilisant la technique des `Transferable Objects` pour eviter une copie couteuse en memoire.

**Interactions:**

- **`WorkerManager`:** Le `ChunkWorker` recoit ses ordres du `WorkerManager` et lui renvoie les donnees de chunk une fois le travail termine.

## 12. Entite Joueur (`js/player/Player.js`)

**Responsabilite generale:** Gerer l'etat, les interactions et la physique du joueur.

Ce module est au cœur de l'experience utilisateur, car il simule la presence du joueur dans le monde 3D. Il est responsable de la camera, des deplacements, des collisions et des interactions avec l'environnement.

**Mecanismes et Fonctionnalites:**

- **Camera et Vue:**
  - Utilise une `THREE.PerspectiveCamera` pour la vue a la premiere personne.
  - La position de la camera est directement liee a la position de la "tete" du joueur.

- **Physique et Mouvement:**
  - **Gravite:** Applique une force constante vers le bas pour simuler la gravite lorsque le joueur n'est pas en mode vol (`flyMode`).
  - **Deplacements:** Gere les entrees du clavier pour deplacer le joueur en avant, en arriere, a gauche et a droite, en tenant compte de l'orientation de la camera.
  - **Sauts:** Permet au joueur de sauter s'il est au sol (`onGround`).
  - **Mode Vol (`flyMode`):** Permet au joueur de se deplacer librement dans les trois dimensions, sans etre affecte par la gravite.

- **Collisions:**
  - **Detection de collision:** Simule un cylindre de collision autour du joueur pour detecter les intersections avec les blocs du monde.
  - **Reponse a la collision:** Empeche le joueur de traverser les blocs solides en ajustant sa position apres chaque mouvement.
  - **Escalade automatique (`autoClimb`):** Permet au joueur de monter automatiquement des blocs d'une certaine hauteur (`stepHeight`), simulant une marche plus fluide sur un terrain accidente.

- **Interactions avec le monde:**
  - **Positionnement initial:** A l'initialisation, le joueur est place au-dessus du sol a sa position de depart.
  - **Detection du sol (`getGroundHeightAt`):** Utilise les donnees du monde pour determiner l'altitude du sol sous le joueur, ce qui est crucial pour la gravite et les sauts.
  - **Minage:** Gere la logique de minage des blocs, y compris la detection du bloc cible, la duree du minage et l'ajout du bloc a l'inventaire.

- **Inventaire:**
  - Implemente un systeme d'inventaire simple pour stocker les blocs mines par le joueur.

**Interactions:**

- **`main.js`**: Cree l'instance du `Player` et appelle sa methode `update()` a chaque frame de la boucle de jeu.
- **`World.js`**: Le joueur interroge constamment le `World` pour obtenir des informations sur les blocs environnants afin de gerer les collisions et la physique.
- **`Controls.js`**: Le module `Controls` capture les entrées du clavier et de la souris et met à jour l'état du joueur en conséquence (par exemple, en changeant sa vélocité ou en initiant un saut).

### `js/player/Controls.js`

**Responsabilité principale :** Capturer les entrées de l'utilisateur (clavier et souris) et les traduire en actions pour le joueur.

Ce module fait le pont entre le joueur et l'utilisateur. Il utilise l'API Pointer Lock pour offrir un contrôle de la caméra à la première personne, similaire à celui des jeux FPS.

**Fonctionnalités clés :**

- **Gestion du Pointer Lock :**
  - Demande le verrouillage du pointeur de la souris lorsque l'utilisateur clique sur la zone de jeu.
  - Une fois le pointeur verrouillé, les mouvements de la souris sont utilisés pour faire pivoter la caméra du joueur sans être limités par les bords de l'écran.
  - La touche `Échap` permet de libérer le pointeur.

- **Gestion des entrées clavier :**
  - **Mouvement :**
    - Les touches `Z`, `Q`, `S`, `D` (ou `W`, `A`, `S`, `D`) sont mappées pour déplacer le joueur en avant, à gauche, en arrière et à droite.
    - La touche `Maj` est utilisée pour le sprint, augmentant la vitesse de déplacement.
  - **Actions :**
    - La touche `Espace` déclenche le saut du joueur.
    - D'autres touches pourraient être utilisées pour des actions comme ouvrir l'inventaire.

- **Gestion des entrées souris :**
  - **Rotation de la caméra :** Les mouvements de la souris sont traduits en rotation de la caméra du joueur.
  - **Actions :**
    - Le clic gauche est utilisé pour initier le minage d'un bloc.
    - Le clic droit pourrait être utilisé pour placer un bloc.

- **Mise à jour de l'état du joueur :**
  - À chaque frame, la méthode `update()` lit l'état des touches et met à jour la vélocité du joueur en conséquence.
  - Elle applique les changements de rotation à la caméra du joueur.

**Interactions avec les autres modules :**

- **`Player.js`**: Le module `Controls` modifie directement les propriétés du `Player`, comme sa vélocité et la rotation de sa caméra, pour le faire bouger et regarder autour de lui.
- **`main.js`**: Crée l'instance de `Controls` en lui passant le joueur et l'élément DOM de la zone de jeu. La méthode `update()` des contrôles est appelée à chaque frame.

### `js/ui/Inventory.js`

**Responsabilité principale :** Gérer l'interface utilisateur de l'inventaire du joueur.

Ce module est responsable de l'affichage visuel de l'inventaire et de la barre d'accès rapide, permettant au joueur de voir et de sélectionner les objets qu'il a collectés.

**Fonctionnalités clés :**

- **Création de l'interface :**
  - Génère dynamiquement les `div` HTML qui représentent les slots de l'inventaire et de la barre d'accès rapide.

- **Affichage des objets :**
  - Met à jour le contenu des slots pour afficher les icônes et les quantités des objets présents dans l'inventaire du joueur.
  - Les slots vides sont affichés différemment des slots occupés.

- **Sélection d'objet :**
  - Met en surbrillance le slot actuellement sélectionné dans la barre d'accès rapide.
  - Le joueur peut changer l'objet sélectionné (par exemple, avec la molette de la souris ou les touches numériques).

- **Mise à jour de l'UI :**
  - La méthode `updateUI()` est appelée chaque fois que l'inventaire du joueur change (ajout ou suppression d'un objet) pour rafraîchir l'affichage.

**Interactions avec les autres modules :**

- **`Player.js`**: L'inventaire est généralement une propriété du `Player`. Le module `Inventory.js` lit les données de l'inventaire du joueur pour afficher les bons objets.
- **`main.js`**: Peut être responsable de l'initialisation de l'interface de l'inventaire au démarrage du jeu.

### `js/ui/OptionsManager.js`

**Responsabilité principale :** Gérer le menu des options et la persistance des paramètres du jeu.

Ce module permet au joueur de personnaliser son expérience de jeu en modifiant divers paramètres, qui sont ensuite sauvegardés pour les sessions futures.

**Fonctionnalités clés :**

- **Chargement et Sauvegarde :**
  - Utilise le `localStorage` du navigateur pour sauvegarder les paramètres du joueur.
  - Au démarrage, il charge les paramètres sauvegardés. S'il n'y en a pas, il utilise un ensemble de paramètres par défaut.
  - Gère la version des paramètres pour assurer la compatibilité et permettre des migrations si la structure des données change.

- **Interface Utilisateur :**
  - Crée et gère le panneau des options, qui peut être ouvert et fermé par l'utilisateur (par exemple, avec une touche dédiée).
  - Contient des éléments d'interface (sliders, checkboxes, boutons) pour chaque paramètre configurable (ex: distance d'affichage, sensibilité de la souris, etc.).

- **Application des paramètres :**
  - Lorsqu'un paramètre est modifié, le `OptionsManager` applique immédiatement le changement au module concerné.
  - Par exemple, changer la distance d'affichage appellera une méthode sur le module `World` pour mettre à jour la distance de rendu.
  - Changer la sensibilité de la souris mettra à jour une propriété sur le module `Controls`.

- **Réinitialisation :**
  - Offre une option pour réinitialiser tous les paramètres à leurs valeurs par défaut.

**Interactions avec les autres modules :**

- **`main.js`**: Initialise le `OptionsManager` au démarrage.
- **`World.js`**: Le `OptionsManager` modifie des propriétés du `World`, comme la `renderDistance`.
- **`Player.js` / `Controls.js`**: Le `OptionsManager` modifie des propriétés liées au joueur, comme la sensibilité de la souris ou le champ de vision (FOV).
- **CSS/DOM**: Modifie les styles CSS (par exemple, pour des filtres d'image comme la luminosité ou le contraste) et interagit avec les éléments HTML du menu d'options.

### `js/utils/Logger.js`

**Responsabilité principale :** Fournir un système de journalisation (logging) robuste et centralisé pour le débogage et le suivi des événements.

Ce module est essentiel pour le développement et la maintenance, car il permet de tracer le déroulement de l'application, de capturer les erreurs et de diagnostiquer les problèmes.

**Fonctionnalités clés :**

- **Journalisation à plusieurs niveaux :**
  - Propose différentes méthodes de log en fonction de la sévérité : `info()`, `warn()`, `error()`, `debug()`.
  - Permet de créer des logs spécialisés pour des contextes précis (ex: `mining()`, `physics()`).

- **Capture d'erreurs globales :**
  - Intercepte automatiquement les erreurs JavaScript non capturées (`window.onerror`) et les rejets de promesses non gérés (`unhandledrejection`).
  - Cela garantit qu'aucune erreur ne passe inaperçue.

- **Stockage et Session :**
  - Assigne un identifiant de session unique (`sessionId`) à chaque chargement de la page pour regrouper les logs d'une même session utilisateur.
  - Stocke les logs en mémoire dans un tableau, avec une limite pour éviter de consommer trop de ressources.

- **Formatage et Contexte :**
  - Chaque entrée de log est enrichie avec des informations contextuelles : timestamp, niveau, message, données associées, et une partie de la pile d'appels (stack trace).

- **Sauvegarde et Export :**
  - Peut inclure des fonctionnalités pour sauvegarder automatiquement les logs (par exemple, dans `localStorage`) ou pour permettre à l'utilisateur de les télécharger sous forme de fichier.

**Interactions avec les autres modules :**

- **Tous les modules :** Pratiquement tous les modules du projet peuvent utiliser le `Logger` (souvent exposé globalement via `window.GameLogger`) pour enregistrer des informations sur leurs opérations, leurs états ou les erreurs qu'ils rencontrent.

### `js/utils/PreloadManager.js`

**Responsabilité principale :** Gérer le préchargement proactif des chunks autour du joueur pour assurer une exploration fluide.

Ce module a pour but d'anticiper les déplacements du joueur et de lancer la génération des chunks avant même qu'ils n'entrent dans la distance d'affichage. Cela permet de réduire, voire d'éliminer, l'attente lors de l'exploration du monde.

**Fonctionnalités clés :**

- **Préchargement basé sur la position :**
  - Surveille la position du joueur.
  - Calcule les coordonnées des chunks situés dans un rayon de préchargement (`preloadDistance`) autour du joueur.

- **Gestion des chunks à précharger :**
  - Maintient un ensemble (`Set`) des chunks déjà préchargés ou en cours de préchargement pour éviter les demandes redondantes.
  - Pour chaque chunk dans la zone de préchargement qui n'a pas encore été traité, il demande sa génération.

- **Nettoyage :**
  - Peut inclure une logique pour nettoyer la liste des chunks préchargés qui sont maintenant très éloignés du joueur, afin de libérer de la mémoire.

**Interactions avec les autres modules :**

- **`World.js`**: Le `PreloadManager` est souvent utilisé par le `World` pour gérer la logique de chargement. Il a besoin d'une référence au `World` pour accéder au `WorkerManager`.
- **`WorkerManager.js`**: Le `PreloadManager` utilise le `WorkerManager` pour envoyer les requêtes de génération de chunk aux workers, sans surcharger le thread principal.
- **`Player.js`**: Il suit la position du `Player` pour déterminer quels chunks précharger.

### `js/utils/Storage.js`

**Responsabilité principale :** Fournir une interface simple et sécurisée pour interagir avec le `localStorage` du navigateur.

Ce module abstrait la complexité de la sauvegarde et du chargement de données, en gérant la sérialisation (en JSON) et la gestion des erreurs.

**Fonctionnalités clés :**

- **Sauvegarde (`save`) :**
  - Prend une clé et un objet de données.
  - Sérialise l'objet en une chaîne JSON.
  - Stocke la chaîne dans `localStorage` sous la clé spécifiée.
  - Inclut une gestion d'erreurs (`try...catch`) pour les cas où le stockage échoue (par exemple, si le quota est dépassé).

- **Chargement (`load`) :**
  - Prend une clé et une valeur par défaut optionnelle.
  - Récupère la chaîne de données depuis `localStorage`.
  - Désérialise la chaîne JSON en un objet JavaScript.
  - Retourne l'objet, ou la valeur par défaut si la clé n'existe pas ou si une erreur de parsing se produit.

- **Suppression (`clear`) :**
  - Prend une clé et supprime l'entrée correspondante de `localStorage`.

**Interactions avec les autres modules :**

- **`OptionsManager.js`**: Utilise `Storage.js` pour sauvegarder et charger les paramètres du jeu.
- **D'autres modules** pourraient l'utiliser pour sauvegarder l'état du joueur (position, inventaire) ou d'autres données de jeu qui doivent persister entre les sessions.