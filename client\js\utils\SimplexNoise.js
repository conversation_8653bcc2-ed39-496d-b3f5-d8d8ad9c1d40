// SimplexNoise.js - Implémentation du bruit de Perlin/Simplex
class SimplexNoise {
    constructor(seed = Math.random()) {
        this.seed = seed;
        this.gradients = {};
    }
    
    hash(x, y, z = 0) {
        return ((Math.sin(x * 12.9898 + y * 78.233 + z * 37.719 + this.seed) * 43758.5453) % 1);
    }
    
    getGradient2D(x, y) {
        const key = `${x},${y}`;
        if (!this.gradients[key]) {
            const theta = this.hash(x, y) * Math.PI * 2;
            this.gradients[key] = { x: Math.cos(theta), y: Math.sin(theta) };
        }
        return this.gradients[key];
    }
    
    getGradient3D(x, y, z) {
        const key = `${x},${y},${z}`;
        if (!this.gradients[key]) {
            const theta = this.hash(x, y, z) * Math.PI * 2;
            const phi = this.hash(x + 1, y + 1, z + 1) * Math.PI;
            this.gradients[key] = {
                x: Math.sin(phi) * Math.cos(theta),
                y: Math.sin(phi) * Math.sin(theta),
                z: Math.cos(phi)
            };
        }
        return this.gradients[key];
    }
    
    dotGridGradient2D(ix, iy, x, y) {
        const gradient = this.getGradient2D(ix, iy);
        const dx = x - ix;
        const dy = y - iy;
        return dx * gradient.x + dy * gradient.y;
    }
    
    dotGridGradient3D(ix, iy, iz, x, y, z) {
        const gradient = this.getGradient3D(ix, iy, iz);
        const dx = x - ix;
        const dy = y - iy;
        const dz = z - iz;
        return dx * gradient.x + dy * gradient.y + dz * gradient.z;
    }
    
    smootherstep(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    lerp(a, b, t) {
        return a + t * (b - a);
    }
    
    noise2D(x, y) {
        const x0 = Math.floor(x);
        const y0 = Math.floor(y);
        const x1 = x0 + 1;
        const y1 = y0 + 1;
        
        const sx = this.smootherstep(x - x0);
        const sy = this.smootherstep(y - y0);
        
        const n0 = this.dotGridGradient2D(x0, y0, x, y);
        const n1 = this.dotGridGradient2D(x1, y0, x, y);
        const ix0 = this.lerp(n0, n1, sx);
        
        const n2 = this.dotGridGradient2D(x0, y1, x, y);
        const n3 = this.dotGridGradient2D(x1, y1, x, y);
        const ix1 = this.lerp(n2, n3, sx);
        
        return this.lerp(ix0, ix1, sy);
    }
    
    noise3D(x, y, z) {
        const x0 = Math.floor(x);
        const y0 = Math.floor(y);
        const z0 = Math.floor(z);
        const x1 = x0 + 1;
        const y1 = y0 + 1;
        const z1 = z0 + 1;
        
        const sx = this.smootherstep(x - x0);
        const sy = this.smootherstep(y - y0);
        const sz = this.smootherstep(z - z0);
        
        const n000 = this.dotGridGradient3D(x0, y0, z0, x, y, z);
        const n100 = this.dotGridGradient3D(x1, y0, z0, x, y, z);
        const n010 = this.dotGridGradient3D(x0, y1, z0, x, y, z);
        const n110 = this.dotGridGradient3D(x1, y1, z0, x, y, z);
        const n001 = this.dotGridGradient3D(x0, y0, z1, x, y, z);
        const n101 = this.dotGridGradient3D(x1, y0, z1, x, y, z);
        const n011 = this.dotGridGradient3D(x0, y1, z1, x, y, z);
        const n111 = this.dotGridGradient3D(x1, y1, z1, x, y, z);
        
        const ix00 = this.lerp(n000, n100, sx);
        const ix10 = this.lerp(n010, n110, sx);
        const ix01 = this.lerp(n001, n101, sx);
        const ix11 = this.lerp(n011, n111, sx);
        
        const iy0 = this.lerp(ix00, ix10, sy);
        const iy1 = this.lerp(ix01, ix11, sy);
        
        return this.lerp(iy0, iy1, sz);
    }
}

// Rendre disponible globalement
window.SimplexNoise = SimplexNoise;