// shared/constants.js
// Constantes partagées entre le client et le serveur

export const BLOCK_TYPES = {
    AIR: 0,
    STONE: 1,
    DIRT: 2,
    GRASS: 3,
    WOOD: 4,
    LEAVES: 5,
    SAND: 6,
    WATER: 7,
    COAL_ORE: 8,
    IRON_ORE: 9,
    GOLD_ORE: 10,
    DIAMOND_ORE: 11,
    BEDROCK: 12,
    TALL_GRASS: 13,
    FLOWERS: 14,
    MUSHROOM: 15,
    CACTUS: 16,
    SNOW: 17,
    ICE: 18,
    CLAY: 19,
    GRAVEL: 20
};

export const GAME_CONFIG = {
    CHUNK_WIDTH: 16,
    CHUNK_HEIGHT: 128,
    CHUNK_DEPTH: 16,
    TICK_RATE: 20, // 20 mises à jour par seconde pour le serveur
    CLIENT_PREDICTION_RATE: 60, // 60 FPS pour la prédiction client
    NETWORK_UPDATE_RATE: 10, // 10 mises à jour réseau par seconde
    
    // Distances de rendu et de chargement
    DEFAULT_RENDER_DISTANCE: 6,
    DEFAULT_LOAD_DISTANCE: 10,
    MAX_RENDER_DISTANCE: 16,
    
    // Physique du joueur
    PLAYER_HEIGHT: 1.8,
    PLAYER_EYE_HEIGHT: 1.7,
    PLAYER_COLLISION_RADIUS: 0.3,
    PLAYER_STEP_HEIGHT: 0.5,
    GRAVITY: 18,
    JUMP_FORCE: 8,
    MOVEMENT_SPEED: 5,
    SPRINT_MULTIPLIER: 1.5,
    
    // Minage
    MINING_REACH: 5,
    MINING_TIME: 1000, // ms pour miner un bloc
    
    // Limites du monde
    WORLD_HEIGHT_MIN: 0,
    WORLD_HEIGHT_MAX: 128,
    WORLD_BORDER: 1000000 // Limite en blocs depuis le centre
};

export const MESSAGE_TYPES = {
    // Messages Client -> Serveur
    CLIENT_INPUT: 'client:input',
    CLIENT_MINE_START: 'client:mineStart',
    CLIENT_MINE_STOP: 'client:mineStop',
    CLIENT_PLACE_BLOCK: 'client:placeBlock',
    CLIENT_CHAT: 'client:chat',
    CLIENT_DISCONNECT: 'client:disconnect',
    CLIENT_PING: 'client:ping',
    CLIENT_PONG: 'client:pong',

    // Messages Serveur -> Client
    SERVER_WORLD_UPDATE: 'server:worldUpdate',
    SERVER_CHUNK_DATA: 'server:chunkData',
    SERVER_BLOCK_UPDATE: 'server:blockUpdate',
    SERVER_PLAYER_JOIN: 'server:playerJoin',
    SERVER_PLAYER_LEAVE: 'server:playerLeave',
    SERVER_CHAT: 'server:chat',
    SERVER_ERROR: 'server:error',
    SERVER_INIT: 'server:init',
    SERVER_MINE_START: 'server:mineStart',
    SERVER_MINE_COMPLETE: 'server:mineComplete',
    SERVER_PING: 'server:ping',
    SERVER_PONG: 'server:pong'
};

export const BIOMES = {
    OCEAN: { id: 0, name: 'Ocean', color: 0x0066cc },
    PLAINS: { id: 1, name: 'Plains', color: 0x90ee90 },
    FOREST: { id: 2, name: 'Forest', color: 0x228b22 },
    MOUNTAINS: { id: 3, name: 'Mountains', color: 0x696969 },
    DESERT: { id: 4, name: 'Desert', color: 0xffd700 },
    TUNDRA: { id: 5, name: 'Tundra', color: 0xf0f8ff }
};

export const PLAYER_STATES = {
    IDLE: 'idle',
    WALKING: 'walking',
    RUNNING: 'running',
    JUMPING: 'jumping',
    FALLING: 'falling',
    FLYING: 'flying',
    MINING: 'mining'
};

// Utilitaires partagés
export const Utils = {
    // Conversion coordonnées monde -> chunk
    worldToChunk(worldX, worldZ) {
        return {
            x: Math.floor(worldX / GAME_CONFIG.CHUNK_WIDTH),
            z: Math.floor(worldZ / GAME_CONFIG.CHUNK_DEPTH)
        };
    },
    
    // Conversion chunk -> coordonnées monde
    chunkToWorld(chunkX, chunkZ) {
        return {
            x: chunkX * GAME_CONFIG.CHUNK_WIDTH,
            z: chunkZ * GAME_CONFIG.CHUNK_DEPTH
        };
    },
    
    // Calcul de distance entre deux points
    distance2D(x1, z1, x2, z2) {
        const dx = x2 - x1;
        const dz = z2 - z1;
        return Math.sqrt(dx * dx + dz * dz);
    },
    
    // Calcul de distance 3D
    distance3D(x1, y1, z1, x2, y2, z2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        const dz = z2 - z1;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    },
    
    // Clamp une valeur entre min et max
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },
    
    // Interpolation linéaire
    lerp(a, b, t) {
        return a + (b - a) * t;
    },
    
    // Génération d'ID unique
    generateId() {
        return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    }
};
