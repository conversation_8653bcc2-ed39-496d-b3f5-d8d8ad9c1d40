🎮 Game client initialisé main.js:37:17
🚀 Initialisation du client... main.js:42:21
🖥️ UIManager initialisé UIManager.js:13:17
⚙️ OptionsManager initialisé OptionsManager.js:47:17
⚙️ Options par défaut utilisées OptionsManager.js:374:25
⚙️ Options chargées et appliquées OptionsManager.js:57:17
🎨 Renderer initialisé Renderer.js:24:17
💡 Éclairage configuré Renderer.js:99:17
🌫️ Brouillard configuré Renderer.js:109:17
✅ Renderer initialisé avec succès Renderer.js:61:21
📊 WebGL: WebGL2 Renderer.js:62:21
📊 Max textures: 16 Renderer.js:63:21
🌍 ClientWorld initialisé ClientWorld.js:33:17
📷 PlayerCamera initialisée PlayerCamera.js:26:17
🎒 Inventory initialisé Inventory.js:24:17
🎮 PlayerController initialisé PlayerController.js:43:17
💬 ChatManager initialisé ChatManager.js:25:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:99:25
✅ Client initialisé avec succès main.js:72:21
🎮 Démarrage de la boucle de jeu main.js:176:17
📦 Réception chunk: (-8, -3) ClientWorld.js:90:17
🎨 Ressources partagées initialisées ClientChunk.js:67:17
📦 ClientChunk créé: (-8, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, -3): 7 types de blocs, 4437 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-8, -3), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (-7, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -7): 8 types de blocs, 4741 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, -7), distance: 9.90 ClientWorld.js:112:17
📦 Réception chunk: (-7, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -6): 8 types de blocs, 4671 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, -6), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-7, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -5): 8 types de blocs, 4673 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, -5), distance: 8.60 ClientWorld.js:112:17
📐 Renderer redimensionné: 1568x947 Renderer.js:153:17
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
📦 Réception chunk: (-7, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -4): 8 types de blocs, 4609 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, -4), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-7, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -3): 8 types de blocs, 4625 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, -3), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-7, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -2): 8 types de blocs, 4543 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, -2), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (-7, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -1): 8 types de blocs, 4501 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, -1), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-7, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 0): 6 types de blocs, 4447 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, 0), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (-7, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 1): 6 types de blocs, 4404 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-7, 1), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-6, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -9): 9 types de blocs, 4933 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -9), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (-6, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -8): 7 types de blocs, 4792 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -8), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (-6, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -7): 9 types de blocs, 4856 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -7), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-6, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -6): 6 types de blocs, 4751 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -6), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (-6, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -5): 8 types de blocs, 4855 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -5), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (-6, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -4): 8 types de blocs, 4829 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -4), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-6, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -3): 8 types de blocs, 4683 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -3), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -2): 8 types de blocs, 4600 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -1): 8 types de blocs, 4607 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, -1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 0): 9 types de blocs, 4511 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, 0), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (-6, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 1): 7 types de blocs, 4403 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, 1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 2): 6 types de blocs, 4342 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, 2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-6, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 3): 6 types de blocs, 4296 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-6, 3), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-5, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -10): 9 types de blocs, 4958 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -10), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (-5, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -9): 9 types de blocs, 5011 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -9), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (-5, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -8): 8 types de blocs, 5026 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -8), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (-5, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -7): 8 types de blocs, 4987 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (-5, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -6): 6 types de blocs, 4875 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -6), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (-5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -5): 8 types de blocs, 4852 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -5), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -4): 8 types de blocs, 4821 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -3): 9 types de blocs, 4717 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -2): 8 types de blocs, 4711 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -1): 8 types de blocs, 4605 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, -1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 0): 7 types de blocs, 4461 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, 0), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 1): 9 types de blocs, 4510 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, 1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 2): 8 types de blocs, 4352 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, 2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 3): 8 types de blocs, 4403 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, 3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-5, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 4): 8 types de blocs, 4648 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-5, 4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-4, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -11): 6 types de blocs, 4935 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -11), distance: 11.70 ClientWorld.js:112:17
📦 Réception chunk: (-4, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -10): 9 types de blocs, 5066 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -10), distance: 10.77 ClientWorld.js:112:17
📦 Réception chunk: (-4, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -9): 9 types de blocs, 5057 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -9), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (-4, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -8): 8 types de blocs, 5097 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -8), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (-4, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -7): 8 types de blocs, 5048 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -7), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -6): 8 types de blocs, 5058 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -5): 8 types de blocs, 4926 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -4): 8 types de blocs, 4911 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (-4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -3): 8 types de blocs, 4782 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -2): 8 types de blocs, 4672 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -1): 8 types de blocs, 4582 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, -1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 0): 8 types de blocs, 4510 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, 0), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (-4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 1): 8 types de blocs, 4478 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, 1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 2): 9 types de blocs, 4604 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, 2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 3): 9 types de blocs, 4701 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, 3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-4, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 4): 9 types de blocs, 4603 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-4, 4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (-3, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -11): 8 types de blocs, 4964 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -11), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (-3, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -10): 9 types de blocs, 5043 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -10), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (-3, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -9): 7 types de blocs, 5042 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -9), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (-3, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -8): 9 types de blocs, 5057 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -8), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (-3, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -7): 8 types de blocs, 5152 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -7), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -6): 6 types de blocs, 4986 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -5): 9 types de blocs, 4942 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -4): 9 types de blocs, 4894 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -3): 6 types de blocs, 4735 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (-3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -2): 8 types de blocs, 4668 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -1): 8 types de blocs, 4613 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 0): 8 types de blocs, 4711 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, 0), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (-3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 1): 9 types de blocs, 4951 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 2): 7 types de blocs, 4710 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, 2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 3): 7 types de blocs, 4534 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, 3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (-3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 4): 7 types de blocs, 4379 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-3, 4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-2, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -11): 9 types de blocs, 4960 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -11), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (-2, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -10): 9 types de blocs, 5038 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -10), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (-2, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -9): 9 types de blocs, 5048 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -9), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-2, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -8): 8 types de blocs, 5069 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -8), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (-2, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -7): 8 types de blocs, 5086 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -7), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (-2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -6): 8 types de blocs, 5005 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -5): 9 types de blocs, 4929 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -4): 6 types de blocs, 4814 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -3): 9 types de blocs, 4785 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -2): 9 types de blocs, 4890 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (-2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -1): 9 types de blocs, 5017 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, -1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 0): 6 types de blocs, 5028 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, 0), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (-2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 1): 7 types de blocs, 4888 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, 1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 2): 9 types de blocs, 4703 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, 2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (-2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 3): 7 types de blocs, 4515 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, 3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 4): 7 types de blocs, 4397 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, 4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-2, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 5): 7 types de blocs, 4313 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-2, 5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-1, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -11): 8 types de blocs, 4877 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -11), distance: 11.05 ClientWorld.js:112:17
📦 Réception chunk: (-1, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -10): 8 types de blocs, 4954 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -10), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (-1, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -9): 9 types de blocs, 4991 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -9), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (-1, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -8): 7 types de blocs, 5002 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -8), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-1, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -7): 10 types de blocs, 5030 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -7), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-1, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -6): 7 types de blocs, 4929 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -6), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -5): 10 types de blocs, 5001 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -4): 10 types de blocs, 5164 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -3): 10 types de blocs, 5309 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -2): 7 types de blocs, 5283 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -1): 8 types de blocs, 5109 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, -1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (-1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 0): 7 types de blocs, 4981 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, 0), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 1): 7 types de blocs, 4789 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (-1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 2): 9 types de blocs, 4604 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, 2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 3): 8 types de blocs, 4521 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, 3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 4): 7 types de blocs, 4408 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, 4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-1, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 5): 8 types de blocs, 4128 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (-1, 5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (0, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -11): 8 types de blocs, 4780 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -11), distance: 11.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -10): 8 types de blocs, 4887 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -10), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -9): 10 types de blocs, 4930 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -9), distance: 9.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -8): 9 types de blocs, 5196 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -8), distance: 8.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -7): 10 types de blocs, 5328 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -7), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -6): 10 types de blocs, 5386 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -6), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -5): 9 types de blocs, 5565 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -5), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -4): 7 types de blocs, 5490 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -4), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -3): 8 types de blocs, 5267 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -3), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -2): 8 types de blocs, 5121 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -2), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -1): 7 types de blocs, 5033 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, -1), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 0): 7 types de blocs, 4886 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, 0), distance: 0.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 1): 8 types de blocs, 4771 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, 1), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 2): 7 types de blocs, 4564 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, 2), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 3): 7 types de blocs, 4346 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, 3), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 4): 8 types de blocs, 4327 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, 4), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 5): 7 types de blocs, 4128 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, 5), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 6): 7 types de blocs, 4066 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (0, 6), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (1, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -10): 9 types de blocs, 5372 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -10), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (1, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -9): 11 types de blocs, 5548 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -9), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (1, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -8): 7 types de blocs, 5547 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -8), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (1, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -7): 7 types de blocs, 5614 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -7), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (1, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -6): 8 types de blocs, 5483 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -6), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -5): 7 types de blocs, 5406 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -4): 8 types de blocs, 5402 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -3): 8 types de blocs, 5170 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -2): 8 types de blocs, 5067 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -1): 7 types de blocs, 4919 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, -1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 0): 7 types de blocs, 4808 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, 0), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 1): 8 types de blocs, 4639 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 2): 7 types de blocs, 4500 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, 2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 3): 7 types de blocs, 4303 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, 3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 4): 8 types de blocs, 4191 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, 4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (1, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 5): 8 types de blocs, 3919 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, 5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (1, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 6): 8 types de blocs, 4037 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (1, 6), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (2, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -10): 7 types de blocs, 5311 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -10), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (2, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -9): 7 types de blocs, 5451 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -9), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (2, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -8): 7 types de blocs, 5487 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -8), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (2, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -7): 7 types de blocs, 5495 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -7), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -6): 8 types de blocs, 5447 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -5): 7 types de blocs, 5388 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -4): 7 types de blocs, 5305 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -3): 7 types de blocs, 5094 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -2): 7 types de blocs, 4942 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -1): 7 types de blocs, 4885 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, -1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 0): 7 types de blocs, 4735 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, 0), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 1): 7 types de blocs, 4464 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, 1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 2): 7 types de blocs, 4431 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, 2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 3): 7 types de blocs, 4224 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, 3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 4): 7 types de blocs, 4091 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, 4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (2, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 5): 7 types de blocs, 3938 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, 5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (2, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 6): 7 types de blocs, 3867 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (2, 6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (3, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -9): 7 types de blocs, 5258 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -9), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (3, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -8): 7 types de blocs, 5377 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -8), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (3, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -7): 7 types de blocs, 5374 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -7), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -6): 7 types de blocs, 5333 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -5): 7 types de blocs, 5229 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -4): 7 types de blocs, 5106 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -3): 7 types de blocs, 4933 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -2): 7 types de blocs, 4874 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -1): 7 types de blocs, 4668 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 0): 7 types de blocs, 4721 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, 0), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 1): 7 types de blocs, 4461 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 2): 7 types de blocs, 4322 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, 2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 3): 7 types de blocs, 4081 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, 3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 4): 7 types de blocs, 3935 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, 4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 5): 7 types de blocs, 3785 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (3, 5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (4, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -8): 7 types de blocs, 5251 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, -8), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (4, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -7): 9 types de blocs, 5342 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, -7), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -6): 10 types de blocs, 5230 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, -6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -5): 7 types de blocs, 4990 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, -5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -4): 7 types de blocs, 4984 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, -4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -3): 7 types de blocs, 4978 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -2): 7 types de blocs, 4679 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, -2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -1): 7 types de blocs, 4623 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, -1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 0): 9 types de blocs, 4486 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, 0), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 1): 9 types de blocs, 4444 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, 1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 2): 7 types de blocs, 4130 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, 2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 3): 7 types de blocs, 4051 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, 3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (4, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 4): 7 types de blocs, 3700 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (4, 4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (5, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -6): 10 types de blocs, 5141 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, -6), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -5): 8 types de blocs, 5039 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, -5), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -4): 7 types de blocs, 5061 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, -4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -3): 7 types de blocs, 4808 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, -3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -2): 7 types de blocs, 4763 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, -2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -1): 7 types de blocs, 4588 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, -1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 0): 8 types de blocs, 4431 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, 0), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 1): 8 types de blocs, 4321 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, 1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 2): 7 types de blocs, 4154 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (5, 2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -2): 7 types de blocs, 4578 blocs visibles ClientChunk.js:173:17
✅ Chunk ajouté: (6, -2), distance: 6.32 ClientWorld.js:112:17
👤 ID joueur reçu: dv214e9u5mdhj0gmm main.js:285:17
👤 ID joueur défini: dv214e9u5mdhj0gmm PlayerController.js:363:17
🌱 Seed du monde défini: 13526 ClientWorld.js:38:17
