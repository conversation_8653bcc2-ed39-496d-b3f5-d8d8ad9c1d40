🎮 Game client initialisé main.js:37:17
🚀 Initialisation du client... main.js:42:21
🖥️ UIManager initialisé UIManager.js:13:17
⚙️ OptionsManager initialisé OptionsManager.js:47:17
⚙️ Options par défaut utilisées OptionsManager.js:374:25
⚙️ Options chargées et appliquées OptionsManager.js:57:17
🎨 Renderer initialisé Renderer.js:24:17
💡 Éclairage configuré Renderer.js:99:17
🌫️ Brouillard configuré Renderer.js:109:17
✅ Renderer initialisé avec succès Renderer.js:61:21
📊 WebGL: WebGL2 Renderer.js:62:21
📊 Max textures: 16 Renderer.js:63:21
🌍 ClientWorld initialisé ClientWorld.js:33:17
📷 PlayerCamera initialisée à la position: 
Object { x: -85.99999999999996, y: 76.40000000000005, z: 34.99999999999998 }
PlayerCamera.js:29:17
🎒 Inventory initialisé Inventory.js:24:17
🎮 PlayerController initialisé PlayerController.js:43:17
💬 ChatManager initialisé ChatManager.js:25:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:99:25
✅ Client initialisé avec succès main.js:72:21
🎮 Démarrage de la boucle de jeu main.js:176:17
🎨 Infos de rendu: main.js:229:25
- Scène enfants: 2 main.js:230:25
- Position caméra: 
Object { x: -85.99999999999996, y: 76.40000000000005, z: 34.99999999999998 }
main.js:231:25
- Rotation caméra: 
Object { _x: 0, _y: 0, _z: 0, _order: "XYZ", _onChangeCallback: Ce() }
main.js:232:25
📦 Réception chunk: (-7, -4) ClientWorld.js:90:17
🎨 Initialisation des ressources partagées... ClientChunk.js:30:17
✅ Géométrie partagée créée ClientChunk.js:40:17
🎨 Ressources partagées initialisées ClientChunk.js:76:17
📦 ClientChunk créé: (-7, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -4): 4 types de blocs, 3767 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, -4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, -4), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-6, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -8): 6 types de blocs, 4331 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -8) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -8), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (-6, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -7): 6 types de blocs, 4117 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -7) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -7), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-6, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -6): 5 types de blocs, 3955 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -6) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -6), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (-6, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -5): 5 types de blocs, 3860 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -5) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -5), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (-6, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -4): 4 types de blocs, 3841 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -4), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-6, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -3): 4 types de blocs, 3933 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -3) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -3), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -2): 4 types de blocs, 4004 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -2) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -1): 7 types de blocs, 4376 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 0): 8 types de blocs, 4515 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 0), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (-6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 2): 8 types de blocs, 4892 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-5, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -7): 4 types de blocs, 4217 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -7) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (-5, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -6): 4 types de blocs, 4055 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -6) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -6), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (-5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -5): 4 types de blocs, 3959 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -5) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -5), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -4): 7 types de blocs, 3970 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -3): 7 types de blocs, 4208 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -2): 7 types de blocs, 4358 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -1): 7 types de blocs, 4468 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 0): 7 types de blocs, 4691 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 0), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 1): 7 types de blocs, 4867 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 2): 9 types de blocs, 4993 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-4, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -7): 4 types de blocs, 4302 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -7) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -7), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -6): 4 types de blocs, 4128 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -6) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -5): 7 types de blocs, 4306 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -4): 7 types de blocs, 4314 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (-4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -3): 7 types de blocs, 4211 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -2): 7 types de blocs, 4464 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -1): 7 types de blocs, 4598 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 0): 8 types de blocs, 4606 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 0), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (-4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 1): 7 types de blocs, 4893 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 2): 7 types de blocs, 5088 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 3): 7 types de blocs, 5260 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-3, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -7): 7 types de blocs, 4502 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -7), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -6): 7 types de blocs, 4504 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -5): 7 types de blocs, 4364 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -4): 7 types de blocs, 4381 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -3): 7 types de blocs, 4410 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (-3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -2): 7 types de blocs, 4529 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -1): 7 types de blocs, 4723 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 0): 8 types de blocs, 4831 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 0), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (-3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 1): 8 types de blocs, 4901 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 2): 7 types de blocs, 5173 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 3): 7 types de blocs, 5343 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (-3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 4): 7 types de blocs, 5512 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-2, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -7): 7 types de blocs, 4562 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -7), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (-2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -6): 8 types de blocs, 4484 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -5): 9 types de blocs, 4481 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -4): 9 types de blocs, 4513 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -3): 7 types de blocs, 4553 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -2): 7 types de blocs, 4696 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (-2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -1): 7 types de blocs, 4746 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 0): 7 types de blocs, 4861 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 0), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (-2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 1): 7 types de blocs, 5143 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 2): 7 types de blocs, 5272 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (-2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 3): 7 types de blocs, 5394 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 4): 7 types de blocs, 5572 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-1, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -6): 7 types de blocs, 4540 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -6), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -5): 8 types de blocs, 4523 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -4): 8 types de blocs, 4578 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -3): 7 types de blocs, 4557 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -2): 7 types de blocs, 4824 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -1): 7 types de blocs, 4852 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (-1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 0): 7 types de blocs, 5040 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 0), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 1): 7 types de blocs, 5129 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (-1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 2): 7 types de blocs, 5342 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 3): 7 types de blocs, 5382 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 4): 8 types de blocs, 5631 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-1, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 5): 9 types de blocs, 5716 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (0, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -6): 7 types de blocs, 4551 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -6), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -5): 7 types de blocs, 4530 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -5), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -4): 7 types de blocs, 4652 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -4), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -3): 8 types de blocs, 4736 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -3), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -2): 7 types de blocs, 4683 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -2), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -1): 7 types de blocs, 4863 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -1), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 0): 7 types de blocs, 4983 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 0), distance: 0.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 1): 7 types de blocs, 5191 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 1), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 2): 9 types de blocs, 5349 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 2), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 3): 8 types de blocs, 5430 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 3), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 4): 8 types de blocs, 5059 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 4), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 5): 8 types de blocs, 5052 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 5), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -5): 7 types de blocs, 4617 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -4): 7 types de blocs, 4561 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -3): 7 types de blocs, 4764 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -2): 7 types de blocs, 4876 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -1): 7 types de blocs, 4998 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 0): 9 types de blocs, 5092 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 0), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 1): 9 types de blocs, 4940 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 2): 9 types de blocs, 4833 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 3): 8 types de blocs, 4902 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 4): 9 types de blocs, 5010 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (1, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 5): 8 types de blocs, 5123 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -4): 8 types de blocs, 4625 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -3): 8 types de blocs, 4606 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -2): 9 types de blocs, 4854 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -1): 8 types de blocs, 4630 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 0): 10 types de blocs, 4541 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 0) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 0), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 1): 6 types de blocs, 4630 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 2): 6 types de blocs, 4732 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 3): 8 types de blocs, 4942 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 4): 8 types de blocs, 5016 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (2, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 5): 8 types de blocs, 5036 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -2): 9 types de blocs, 4366 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -1): 8 types de blocs, 4487 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 0): 8 types de blocs, 4617 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 0), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 1): 8 types de blocs, 4640 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 2): 8 types de blocs, 4755 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 3): 6 types de blocs, 4809 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 3) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 4): 8 types de blocs, 4930 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 5): 8 types de blocs, 5037 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (3, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 6): 9 types de blocs, 5081 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 6) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 2): 8 types de blocs, 4708 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 2), distance: 4.47 ClientWorld.js:112:17
👤 ID joueur reçu: yk0gn9hf2mdhj7aaw main.js:304:17
👤 ID joueur défini: yk0gn9hf2mdhj7aaw PlayerController.js:377:17
🌱 Seed du monde défini: 624481 ClientWorld.js:38:17
🎨 Représentation visuelle créée pour 6svpxwnymmdhj6za2 RemotePlayer.js:72:17
👤 RemotePlayer créé: 6svpxwnymmdhj6za2 RemotePlayer.js:37:17
👤 Joueur ajouté: 6svpxwnymmdhj6za2 ClientWorld.js:150:17
🎯 Première position reçue du serveur: 
Object { x: -86, y: 74.7, z: 35 }
PlayerController.js:282:25
📐 Renderer redimensionné: 506x624 Renderer.js:153:17
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
