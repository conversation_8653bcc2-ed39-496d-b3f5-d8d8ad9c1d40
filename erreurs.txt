INDEX : Logs du terminal console du serveur ci bas a partir de la ligne : 4
INDEX : Logs du navigateur a partir de la ligne : 525

📦 Réception chunk: (1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -3): 7 types de blocs, 4592 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -2): 7 types de blocs, 4628 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -1): 8 types de blocs, 4669 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 0): 9 types de blocs, 4809 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 0), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 1): 9 types de blocs, 4574 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 2): 8 types de blocs, 4458 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 3): 8 types de blocs, 4571 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 4): 6 types de blocs, 4652 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 4) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (1, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 5): 7 types de blocs, 4702 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (1, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 6): 8 types de blocs, 4856 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 6), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (1, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 7): 8 types de blocs, 4866 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 7), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (1, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 8): 8 types de blocs, 4901 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 8) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 8), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (2, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -10): 7 types de blocs, 4623 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -10) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -10), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (2, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -9): 7 types de blocs, 4595 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -9), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (2, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -8): 8 types de blocs, 4621 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -8) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -8), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (2, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -7): 7 types de blocs, 4552 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -7), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -6): 7 types de blocs, 4452 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -5): 7 types de blocs, 4415 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -4): 7 types de blocs, 4488 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -3): 7 types de blocs, 4515 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -2): 9 types de blocs, 4507 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -1): 9 types de blocs, 4312 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 0): 10 types de blocs, 4279 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 0) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 0), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 1): 9 types de blocs, 4327 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 2): 8 types de blocs, 4413 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 3): 8 types de blocs, 4459 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 4): 8 types de blocs, 4549 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (2, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 5): 6 types de blocs, 4622 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 5) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (2, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 6): 8 types de blocs, 4813 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (2, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 7): 8 types de blocs, 4888 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 7), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (2, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 8): 6 types de blocs, 4810 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 8) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 8), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (3, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -10): 7 types de blocs, 4588 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -10) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -10), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (3, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -9): 7 types de blocs, 4537 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -9), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (3, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -8): 7 types de blocs, 4523 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -8), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (3, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -7): 7 types de blocs, 4361 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -7), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -6): 7 types de blocs, 4347 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -5): 7 types de blocs, 4296 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -4): 7 types de blocs, 4306 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -3): 9 types de blocs, 4190 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -2): 8 types de blocs, 4172 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -1): 8 types de blocs, 4098 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 0): 8 types de blocs, 4191 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 0), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 1): 8 types de blocs, 4191 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 2): 8 types de blocs, 4265 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 3): 8 types de blocs, 4379 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 4): 8 types de blocs, 4491 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 5): 6 types de blocs, 4572 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 5) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (3, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 6): 9 types de blocs, 4707 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 6) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (3, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 7): 8 types de blocs, 4740 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 7), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (3, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 8): 8 types de blocs, 4807 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 8) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 8), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (4, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -9): 7 types de blocs, 4700 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -9), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (4, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -8): 7 types de blocs, 4362 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -8), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (4, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -7): 7 types de blocs, 4306 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -7), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -6): 7 types de blocs, 4181 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -5): 9 types de blocs, 4086 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -4): 9 types de blocs, 3968 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -3): 8 types de blocs, 3902 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -2): 8 types de blocs, 3934 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -1): 8 types de blocs, 3990 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 0): 8 types de blocs, 4123 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 0), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 1): 8 types de blocs, 4116 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 2): 7 types de blocs, 4156 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 3): 10 types de blocs, 4369 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 3) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (4, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 4): 11 types de blocs, 4449 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 4) a 11 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (4, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 5): 11 types de blocs, 4587 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 5) a 11 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (4, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 6): 9 types de blocs, 4757 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 6) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (4, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 7): 8 types de blocs, 4771 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 7), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (4, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 8): 9 types de blocs, 4775 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 8) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 8), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (5, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -9): 9 types de blocs, 4323 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -9) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -9), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (5, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -8): 7 types de blocs, 4272 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -8), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (5, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -7): 7 types de blocs, 4126 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (5, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -6): 10 types de blocs, 3985 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -6) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -6), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -5): 9 types de blocs, 3867 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -5), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -4): 8 types de blocs, 3939 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -3): 8 types de blocs, 3826 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -2): 8 types de blocs, 3921 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -1): 6 types de blocs, 3882 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 0): 10 types de blocs, 3987 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 0) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 0), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 1): 8 types de blocs, 4040 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 2): 6 types de blocs, 4108 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 3): 8 types de blocs, 4268 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (5, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 4): 8 types de blocs, 4409 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (5, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 5): 8 types de blocs, 4590 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 5), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (5, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 6): 8 types de blocs, 4638 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 6), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (5, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 7): 8 types de blocs, 4746 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (5, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 8): 8 types de blocs, 4523 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 8) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 8), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (5, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 9): 5 types de blocs, 3992 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 9) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 9), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (6, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -9): 8 types de blocs, 4295 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -9) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -9), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (6, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -8): 8 types de blocs, 4244 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -8) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -8), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (6, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -7): 8 types de blocs, 4101 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -7), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (6, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -6): 10 types de blocs, 3862 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -6) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -6), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (6, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -5): 6 types de blocs, 3725 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -5) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -5), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (6, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -4): 8 types de blocs, 3763 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -4), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (6, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -3): 6 types de blocs, 3744 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -3) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -3), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -2): 8 types de blocs, 3846 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -1): 8 types de blocs, 3918 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 0): 7 types de blocs, 3912 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 0), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (6, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 1): 8 types de blocs, 4058 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 2): 8 types de blocs, 4088 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (6, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 3): 8 types de blocs, 4289 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 3), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (6, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 4): 6 types de blocs, 4329 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 4) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 4), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (6, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 5): 9 types de blocs, 4484 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 5), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (6, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 6): 8 types de blocs, 4646 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 6), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (6, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 7): 9 types de blocs, 4665 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 7) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 7), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (6, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 8): 7 types de blocs, 4038 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 8), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (7, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -9): 7 types de blocs, 4257 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -9), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (7, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -8): 7 types de blocs, 4047 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -8), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (7, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -7): 9 types de blocs, 3961 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -7) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -7), distance: 9.90 ClientWorld.js:112:17
📦 Réception chunk: (7, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -6): 6 types de blocs, 3718 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -6) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -6), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (7, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -5): 8 types de blocs, 3735 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -5), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (7, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -4): 8 types de blocs, 3793 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -4), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (7, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -3): 8 types de blocs, 3742 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -3), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (7, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -2): 8 types de blocs, 3803 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -2), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (7, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -1): 9 types de blocs, 3858 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -1), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (7, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 0): 7 types de blocs, 3934 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 0), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (7, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 1): 9 types de blocs, 4025 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 1), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (7, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 2): 8 types de blocs, 4155 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 2), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (7, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 3): 7 types de blocs, 4207 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 3), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (7, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 4): 9 types de blocs, 4357 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 4), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (7, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 5): 9 types de blocs, 4494 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 5), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (7, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 6): 6 types de blocs, 4652 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 6) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 6), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (7, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 7): 9 types de blocs, 4498 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 7) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 7), distance: 9.90 ClientWorld.js:112:17
📦 Réception chunk: (7, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 8): 4 types de blocs, 3992 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 8), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (8, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -8): 9 types de blocs, 4084 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -8) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -8), distance: 11.31 ClientWorld.js:112:17
📦 Réception chunk: (8, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -7): 9 types de blocs, 3815 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -7) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -7), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (8, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -6): 8 types de blocs, 3778 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -6), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (8, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -5): 8 types de blocs, 3742 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -5), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (8, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -4): 8 types de blocs, 3803 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -4), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (8, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -3): 9 types de blocs, 3850 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -3), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (8, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -2): 9 types de blocs, 3824 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -2), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (8, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -1): 9 types de blocs, 3883 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -1), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (8, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 0): 8 types de blocs, 3962 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 0), distance: 8.00 ClientWorld.js:112:17
📦 Réception chunk: (8, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 1): 8 types de blocs, 4147 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 1), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (8, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 2): 8 types de blocs, 4165 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 2), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (8, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 3): 8 types de blocs, 4296 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 3), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (8, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 4): 8 types de blocs, 4455 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 4), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (8, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 5): 8 types de blocs, 4587 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 5), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (8, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 6): 8 types de blocs, 4699 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 6), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (8, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 7): 6 types de blocs, 4335 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 7) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 7), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (8, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 8): 4 types de blocs, 3993 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 8), distance: 11.31 ClientWorld.js:112:17
📦 Réception chunk: (9, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -8): 10 types de blocs, 3999 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -8) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -8), distance: 12.04 ClientWorld.js:112:17
📦 Réception chunk: (9, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -7): 8 types de blocs, 3799 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -7), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (9, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -6): 8 types de blocs, 3819 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -6), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (9, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -5): 8 types de blocs, 3762 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -5), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (9, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -4): 9 types de blocs, 3772 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -4), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (9, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -3): 10 types de blocs, 3811 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -3) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -3), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (9, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -2): 9 types de blocs, 3886 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -2), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (9, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -1): 9 types de blocs, 3962 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -1), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (9, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 0): 8 types de blocs, 4026 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 0), distance: 9.00 ClientWorld.js:112:17
📦 Réception chunk: (9, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 1): 6 types de blocs, 4075 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 1), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (9, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 2): 8 types de blocs, 4240 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 2), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (9, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 3): 8 types de blocs, 4375 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 3), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (9, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 4): 8 types de blocs, 4543 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 4), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (9, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 5): 8 types de blocs, 4628 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 5), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (9, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 6): 8 types de blocs, 4737 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 6), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (9, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 7): 7 types de blocs, 4185 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 7), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (9, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 8): 4 types de blocs, 4022 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 8), distance: 12.04 ClientWorld.js:112:17
📦 Réception chunk: (10, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -7): 9 types de blocs, 3840 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, -7) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, -7), distance: 12.21 ClientWorld.js:112:17
📦 Réception chunk: (10, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -6): 6 types de blocs, 3751 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, -6) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, -6), distance: 11.66 ClientWorld.js:112:17
📦 Réception chunk: (10, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -5): 8 types de blocs, 3815 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, -5), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (10, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -4): 8 types de blocs, 3872 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, -4), distance: 10.77 ClientWorld.js:112:17
📦 Réception chunk: (10, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -3): 8 types de blocs, 3962 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, -3), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (10, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -2): 8 types de blocs, 3929 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, -2), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (10, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -1): 8 types de blocs, 4086 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, -1), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (10, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 0): 7 types de blocs, 4062 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, 0), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (10, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 1): 8 types de blocs, 4176 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, 1), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (10, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 2): 6 types de blocs, 4270 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, 2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, 2), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (10, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 3): 6 types de blocs, 4393 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, 3) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, 3), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (10, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 4): 9 types de blocs, 4566 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, 4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, 4), distance: 10.77 ClientWorld.js:112:17
📦 Réception chunk: (10, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 5): 9 types de blocs, 4719 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, 5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, 5), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (10, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 6): 6 types de blocs, 4775 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, 6) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, 6), distance: 11.66 ClientWorld.js:112:17
📦 Réception chunk: (10, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 7): 7 types de blocs, 4349 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (10, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (10, 7), distance: 12.21 ClientWorld.js:112:17
📦 Réception chunk: (11, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (11, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, -6): 8 types de blocs, 3916 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, -6), distance: 12.53 ClientWorld.js:112:17
📦 Réception chunk: (11, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (11, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, -5): 6 types de blocs, 3809 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, -5) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, -5), distance: 12.08 ClientWorld.js:112:17
📦 Réception chunk: (11, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (11, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, -4): 8 types de blocs, 3928 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, -4), distance: 11.70 ClientWorld.js:112:17
📦 Réception chunk: (11, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (11, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, -3): 6 types de blocs, 3908 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, -3) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, -3), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (11, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (11, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, -2): 8 types de blocs, 4024 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, -2), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (11, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (11, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, -1): 8 types de blocs, 4125 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, -1), distance: 11.05 ClientWorld.js:112:17
📦 Réception chunk: (11, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (11, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, 0): 9 types de blocs, 4178 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, 0), distance: 11.00 ClientWorld.js:112:17
📦 Réception chunk: (11, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (11, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, 1): 9 types de blocs, 4282 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, 1), distance: 11.05 ClientWorld.js:112:17
📦 Réception chunk: (11, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (11, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, 2): 8 types de blocs, 4391 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, 2), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (11, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (11, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, 3): 8 types de blocs, 4560 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, 3), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (11, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (11, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, 4): 7 types de blocs, 4600 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, 4), distance: 11.70 ClientWorld.js:112:17
📦 Réception chunk: (11, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (11, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, 5): 8 types de blocs, 4790 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, 5), distance: 12.08 ClientWorld.js:112:17
📦 Réception chunk: (11, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (11, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, 6): 8 types de blocs, 4886 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (11, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (11, 6), distance: 12.53 ClientWorld.js:112:17
📦 Réception chunk: (12, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (12, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, -4): 9 types de blocs, 3945 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, -4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, -4), distance: 12.65 ClientWorld.js:112:17
📦 Réception chunk: (12, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (12, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, -3): 8 types de blocs, 4078 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, -3), distance: 12.37 ClientWorld.js:112:17
📦 Réception chunk: (12, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (12, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, -2): 9 types de blocs, 4139 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, -2), distance: 12.17 ClientWorld.js:112:17
📦 Réception chunk: (12, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (12, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, -1): 6 types de blocs, 4170 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, -1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, -1), distance: 12.04 ClientWorld.js:112:17
📦 Réception chunk: (12, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (12, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, 0): 9 types de blocs, 4280 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, 0), distance: 12.00 ClientWorld.js:112:17
📦 Réception chunk: (12, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (12, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, 1): 9 types de blocs, 4375 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, 1), distance: 12.04 ClientWorld.js:112:17
📦 Réception chunk: (12, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (12, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, 2): 8 types de blocs, 4498 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, 2), distance: 12.17 ClientWorld.js:112:17
📦 Réception chunk: (12, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (12, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, 3): 8 types de blocs, 4601 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, 3), distance: 12.37 ClientWorld.js:112:17
📦 Réception chunk: (12, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (12, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (12, 4): 7 types de blocs, 4700 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (12, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (12, 4), distance: 12.65 ClientWorld.js:112:17
📦 Réception chunk: (13, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (13, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (13, 0): 9 types de blocs, 4338 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (13, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (13, 0), distance: 13.00 ClientWorld.js:112:17
👤 ID joueur reçu: 00bgkf0b6mdhk9gd9 main.js:312:17
👤 ID joueur défini: 00bgkf0b6mdhk9gd9 PlayerController.js:383:17
🌱 Seed du monde défini: 178567 ClientWorld.js:38:17
🎯 Première position reçue du serveur: 
Object { x: 91, y: 57.7, z: -5 }
PlayerController.js:288:25
🔍 Appel de hideConnectionScreen et showInstructions... main.js:100:29
🔍 UIManager.hideConnectionScreen() appelée UIManager.js:99:17
🔍 Element connectionScreen: true UIManager.js:100:17
🔍 Element uiOverlay: true UIManager.js:101:17
🔍 Classes connectionScreen avant: <empty string> UIManager.js:104:21
🔍 Classes connectionScreen après: hidden UIManager.js:106:21
🔍 Classes uiOverlay avant: hidden UIManager.js:112:21
🔍 Classes uiOverlay après: <empty string> UIManager.js:114:21
✅ hideConnectionScreen terminée UIManager.js:119:17
🔍 UIManager.showInstructions() appelée UIManager.js:139:17
🔍 Element instructions: true UIManager.js:140:17
🔍 Classes instructions avant: hidden UIManager.js:143:21
🔍 Classes instructions après: <empty string> UIManager.js:145:21
✅ Instructions affichées UIManager.js:146:21
✅ Connecté au serveur et prêt à jouer main.js:103:29
📐 Renderer redimensionné: 920x580 Renderer.js:153:17
📐 Renderer redimensionné: 920x624 Renderer.js:153:17
🔓 Pointer lock désactivé PlayerController.js:154:21
🔍 Pointer lock désactivé après avoir été actif - vérifier la cause PlayerController.js:157:25
console.trace() Stack trace de la désactivation du pointer lock PlayerController.js:158:25
    onPointerLockChange http://localhost:3000/js/player/PlayerController.js:158
    setupEventListeners http://localhost:3000/js/player/PlayerController.js:58
📐 Renderer redimensionné: 506x624 Renderer.js:153:17
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:485:43
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20

PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> npm start

> jscraft-server@1.0.0 start
> node server/server.js

🌱 WorldGenerator initialisé avec seed: 178567
🌍 WorldManager initialisé avec seed: 178567
🎮 GameManager initialisé
🌐 Serveur WebSocket initialisé
🚀 Serveur JScraft démarré sur http://localhost:3000
📁 Fichiers client servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\client
🔗 Fichiers partagés servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\shared
🎮 Boucle de jeu serveur démarrée (20 ticks/s)
📊 Stats: 0 joueurs, 17 TPS, 62.00ms/tick
👤 Nouveau joueur connecté: dp7djis91mdhk8l67
👤 Player dp7djis91mdhk8l67 a rejoint le jeu
👤 Joueur dp7djis91mdhk8l67 créé à la position (0, 100, 0)
🧱 Chunk généré: (3, 0)
🧱 Chunk généré: (-7, 0)
🧱 Chunk généré: (-6, -4)
🧱 Chunk généré: (-6, -3)
🧱 Chunk généré: (-6, -2)
🧱 Chunk généré: (-6, -1)
🧱 Chunk généré: (-6, 0)
🧱 Chunk généré: (-6, 1)
🧱 Chunk généré: (-6, 2)
🧱 Chunk généré: (-6, 3)
🧱 Chunk généré: (-6, 4)
🧱 Chunk généré: (-5, -6)
🧱 Chunk généré: (-5, -5)
🧱 Chunk généré: (-5, -4)
🧱 Chunk généré: (-5, -3)
🧱 Chunk généré: (-5, -2)
🧱 Chunk généré: (-5, -1)
🧱 Chunk généré: (-5, 0)
🧱 Chunk généré: (-5, 1)
🧱 Chunk généré: (-5, 2)
🧱 Chunk généré: (-5, 3)
🧱 Chunk généré: (-5, 4)
🧱 Chunk généré: (-5, 5)
🧱 Chunk généré: (-5, 6)
🧱 Chunk généré: (-4, -7)
🧱 Chunk généré: (-4, -6)
🧱 Chunk généré: (-4, -5)
🧱 Chunk généré: (-4, -4)
🧱 Chunk généré: (-4, -3)
🧱 Chunk généré: (-4, -2)
🧱 Chunk généré: (-4, -1)
🧱 Chunk généré: (-4, 0)
🧱 Chunk généré: (-4, 1)
🧱 Chunk généré: (-4, 2)
🧱 Chunk généré: (-4, 3)
🧱 Chunk généré: (-4, 4)
🧱 Chunk généré: (-4, 5)
🧱 Chunk généré: (-4, 6)
🧱 Chunk généré: (-4, 7)
🧱 Chunk généré: (-3, -8)
🧱 Chunk généré: (-3, -7)
🧱 Chunk généré: (-3, -6)
🧱 Chunk généré: (-3, -5)
🧱 Chunk généré: (-3, -4)
🧱 Chunk généré: (-3, -3)
🧱 Chunk généré: (-3, -2)
🧱 Chunk généré: (-3, -1)
🧱 Chunk généré: (-3, 0)
🧱 Chunk généré: (-3, 1)
🧱 Chunk généré: (-3, 2)
🧱 Chunk généré: (-3, 3)
🧱 Chunk généré: (-3, 4)
🧱 Chunk généré: (-3, 5)
🧱 Chunk généré: (-3, 6)
🧱 Chunk généré: (-3, 7)
🧱 Chunk généré: (-3, 8)
🧱 Chunk généré: (-2, -8)
🧱 Chunk généré: (-2, -7)
🧱 Chunk généré: (-2, -6)
🧱 Chunk généré: (-2, -5)
🧱 Chunk généré: (-2, -4)
🧱 Chunk généré: (-2, -3)
🧱 Chunk généré: (-2, -2)
🧱 Chunk généré: (-2, -1)
🧱 Chunk généré: (-2, 0)
🧱 Chunk généré: (-2, 1)
🧱 Chunk généré: (-2, 2)
🧱 Chunk généré: (-2, 3)
🧱 Chunk généré: (-2, 4)
🧱 Chunk généré: (-2, 5)
🧱 Chunk généré: (-2, 6)
🧱 Chunk généré: (-2, 7)
🧱 Chunk généré: (-2, 8)
🧱 Chunk généré: (-1, -9)
🧱 Chunk généré: (-1, -8)
🧱 Chunk généré: (-1, -7)
🧱 Chunk généré: (-1, -6)
🧱 Chunk généré: (-1, -5)
🧱 Chunk généré: (-1, -4)
🧱 Chunk généré: (-1, -3)
🧱 Chunk généré: (-1, -2)
🧱 Chunk généré: (-1, -1)
🧱 Chunk généré: (-1, 0)
🧱 Chunk généré: (-1, 1)
🧱 Chunk généré: (-1, 2)
🧱 Chunk généré: (-1, 3)
🧱 Chunk généré: (-1, 4)
🧱 Chunk généré: (-1, 5)
🧱 Chunk généré: (-1, 6)
🧱 Chunk généré: (-1, 7)
🧱 Chunk généré: (-1, 8)
🧱 Chunk généré: (-1, 9)
🧱 Chunk généré: (0, -9)
🧱 Chunk généré: (0, -8)
🧱 Chunk généré: (0, -7)
🧱 Chunk généré: (0, -6)
🧱 Chunk généré: (0, -5)
🧱 Chunk généré: (0, -4)
🧱 Chunk généré: (0, -3)
🧱 Chunk généré: (0, -2)
🧱 Chunk généré: (0, -1)
🧱 Chunk généré: (0, 0)
🧱 Chunk généré: (0, 1)
🧱 Chunk généré: (0, 2)
🧱 Chunk généré: (0, 3)
🧱 Chunk généré: (0, 4)
🧱 Chunk généré: (0, 5)
🧱 Chunk généré: (0, 6)
🧱 Chunk généré: (0, 7)
🧱 Chunk généré: (0, 8)
🧱 Chunk généré: (0, 9)
🧱 Chunk généré: (1, -9)
🧱 Chunk généré: (1, -8)
🧱 Chunk généré: (1, -7)
🧱 Chunk généré: (1, -6)
🧱 Chunk généré: (1, -5)
🧱 Chunk généré: (1, -4)
🧱 Chunk généré: (1, -3)
🧱 Chunk généré: (1, -2)
🧱 Chunk généré: (1, -1)
🧱 Chunk généré: (1, 0)
🧱 Chunk généré: (1, 1)
🧱 Chunk généré: (1, 2)
🧱 Chunk généré: (1, 3)
🧱 Chunk généré: (1, 4)
🧱 Chunk généré: (1, 5)
🧱 Chunk généré: (1, 6)
🧱 Chunk généré: (1, 7)
🧱 Chunk généré: (1, 8)
🧱 Chunk généré: (1, 9)
🧱 Chunk généré: (2, -9)
🧱 Chunk généré: (2, -8)
🧱 Chunk généré: (2, -7)
🧱 Chunk généré: (2, -6)
🧱 Chunk généré: (2, -5)
🧱 Chunk généré: (2, -4)
🧱 Chunk généré: (2, -3)
🧱 Chunk généré: (2, -2)
🧱 Chunk généré: (2, -1)
🧱 Chunk généré: (2, 0)
🧱 Chunk généré: (2, 1)
🧱 Chunk généré: (2, 2)
🧱 Chunk généré: (2, 3)
🧱 Chunk généré: (2, 4)
🧱 Chunk généré: (2, 5)
🧱 Chunk généré: (2, 6)
🧱 Chunk généré: (2, 7)
🧱 Chunk généré: (2, 8)
🧱 Chunk généré: (2, 9)
🧱 Chunk généré: (3, -10)
🧱 Chunk généré: (3, -9)
🧱 Chunk généré: (3, -8)
🧱 Chunk généré: (3, -7)
🧱 Chunk généré: (3, -6)
🧱 Chunk généré: (3, -5)
🧱 Chunk généré: (3, -4)
🧱 Chunk généré: (3, -3)
🧱 Chunk généré: (3, -2)
🧱 Chunk généré: (3, -1)
🧱 Chunk généré: (3, 1)
🧱 Chunk généré: (3, 2)
🧱 Chunk généré: (3, 3)
🧱 Chunk généré: (3, 4)
🧱 Chunk généré: (3, 5)
🧱 Chunk généré: (3, 6)
🧱 Chunk généré: (3, 7)
🧱 Chunk généré: (3, 8)
🧱 Chunk généré: (3, 9)
🧱 Chunk généré: (3, 10)
🧱 Chunk généré: (4, -9)
🧱 Chunk généré: (4, -8)
🧱 Chunk généré: (4, -7)
🧱 Chunk généré: (4, -6)
🧱 Chunk généré: (4, -5)
🧱 Chunk généré: (4, -4)
🧱 Chunk généré: (4, -3)
🧱 Chunk généré: (4, -2)
🧱 Chunk généré: (4, -1)
🧱 Chunk généré: (4, 0)
🧱 Chunk généré: (4, 1)
🧱 Chunk généré: (4, 2)
🧱 Chunk généré: (4, 3)
🧱 Chunk généré: (4, 4)
🧱 Chunk généré: (4, 5)
🧱 Chunk généré: (4, 6)
🧱 Chunk généré: (4, 7)
🧱 Chunk généré: (4, 8)
🧱 Chunk généré: (4, 9)
🧱 Chunk généré: (5, -9)
🧱 Chunk généré: (5, -8)
🧱 Chunk généré: (5, -7)
🧱 Chunk généré: (5, -6)
🧱 Chunk généré: (5, -5)
🧱 Chunk généré: (5, -4)
🧱 Chunk généré: (5, -3)
🧱 Chunk généré: (5, -2)
🧱 Chunk généré: (5, -1)
🧱 Chunk généré: (5, 0)
🧱 Chunk généré: (5, 1)
🧱 Chunk généré: (5, 2)
🧱 Chunk généré: (5, 3)
🧱 Chunk généré: (5, 4)
🧱 Chunk généré: (5, 5)
🧱 Chunk généré: (5, 6)
🧱 Chunk généré: (5, 7)
🧱 Chunk généré: (5, 8)
🧱 Chunk généré: (5, 9)
🧱 Chunk généré: (6, -9)
🧱 Chunk généré: (6, -8)
🧱 Chunk généré: (6, -7)
🧱 Chunk généré: (6, -6)
🧱 Chunk généré: (6, -5)
🧱 Chunk généré: (6, -4)
🧱 Chunk généré: (6, -3)
🧱 Chunk généré: (6, -2)
🧱 Chunk généré: (6, -1)
🧱 Chunk généré: (6, 0)
🧱 Chunk généré: (6, 1)
🧱 Chunk généré: (6, 2)
🧱 Chunk généré: (6, 3)
🧱 Chunk généré: (6, 4)
🧱 Chunk généré: (6, 5)
🧱 Chunk généré: (6, 6)
🧱 Chunk généré: (6, 7)
🧱 Chunk généré: (6, 8)
🧱 Chunk généré: (6, 9)
🧱 Chunk généré: (7, -9)
🧱 Chunk généré: (7, -8)
🧱 Chunk généré: (7, -7)
🧱 Chunk généré: (7, -6)
🧱 Chunk généré: (7, -5)
🧱 Chunk généré: (7, -4)
🧱 Chunk généré: (7, -3)
🧱 Chunk généré: (7, -2)
🧱 Chunk généré: (7, -1)
🧱 Chunk généré: (7, 0)
🧱 Chunk généré: (7, 1)
🧱 Chunk généré: (7, 2)
🧱 Chunk généré: (7, 3)
🧱 Chunk généré: (7, 4)
🧱 Chunk généré: (7, 5)
🧱 Chunk généré: (7, 6)
🧱 Chunk généré: (7, 7)
🧱 Chunk généré: (7, 8)
🧱 Chunk généré: (7, 9)
🧱 Chunk généré: (8, -8)
🧱 Chunk généré: (8, -7)
🧱 Chunk généré: (8, -6)
🧱 Chunk généré: (8, -5)
🧱 Chunk généré: (8, -4)
🧱 Chunk généré: (8, -3)
🧱 Chunk généré: (8, -2)
🧱 Chunk généré: (8, -1)
🧱 Chunk généré: (8, 0)
🧱 Chunk généré: (8, 1)
🧱 Chunk généré: (8, 2)
🧱 Chunk généré: (8, 3)
🧱 Chunk généré: (8, 4)
🧱 Chunk généré: (8, 5)
🧱 Chunk généré: (8, 6)
🧱 Chunk généré: (8, 7)
🧱 Chunk généré: (8, 8)
🧱 Chunk généré: (9, -8)
🧱 Chunk généré: (9, -7)
🧱 Chunk généré: (9, -6)
🧱 Chunk généré: (9, -5)
🧱 Chunk généré: (9, -4)
🧱 Chunk généré: (9, -3)
🧱 Chunk généré: (9, -2)
🧱 Chunk généré: (9, -1)
🧱 Chunk généré: (9, 0)
🧱 Chunk généré: (9, 1)
🧱 Chunk généré: (9, 2)
🧱 Chunk généré: (9, 3)
🧱 Chunk généré: (9, 4)
🧱 Chunk généré: (9, 5)
🧱 Chunk généré: (9, 6)
🧱 Chunk généré: (9, 7)
🧱 Chunk généré: (9, 8)
🧱 Chunk généré: (10, -7)
🧱 Chunk généré: (10, -6)
🧱 Chunk généré: (10, -5)
🧱 Chunk généré: (10, -4)
🧱 Chunk généré: (10, -3)
🧱 Chunk généré: (10, -2)
🧱 Chunk généré: (10, -1)
🧱 Chunk généré: (10, 0)
🧱 Chunk généré: (10, 1)
🧱 Chunk généré: (10, 2)
🧱 Chunk généré: (10, 3)
🧱 Chunk généré: (10, 4)
🧱 Chunk généré: (10, 5)
🧱 Chunk généré: (10, 6)
🧱 Chunk généré: (10, 7)
🧱 Chunk généré: (11, -6)
🧱 Chunk généré: (11, -5)
🧱 Chunk généré: (11, -4)
🧱 Chunk généré: (11, -3)
🧱 Chunk généré: (11, -2)
🧱 Chunk généré: (11, -1)
🧱 Chunk généré: (11, 0)
🧱 Chunk généré: (11, 1)
🧱 Chunk généré: (11, 2)
🧱 Chunk généré: (11, 3)
🧱 Chunk généré: (11, 4)
🧱 Chunk généré: (11, 5)
🧱 Chunk généré: (11, 6)
🧱 Chunk généré: (12, -4)
🧱 Chunk généré: (12, -3)
🧱 Chunk généré: (12, -2)
🧱 Chunk généré: (12, -1)
🧱 Chunk généré: (12, 0)
🧱 Chunk généré: (12, 1)
🧱 Chunk généré: (12, 2)
🧱 Chunk généré: (12, 3)
🧱 Chunk généré: (12, 4)
🧱 Chunk généré: (13, 0)
📊 Stats: 1 joueurs, 18 TPS, 62.00ms/tick
🧱 Chunk généré: (-8, 0)
🧱 Chunk généré: (-7, -4)
🧱 Chunk généré: (-7, -3)
🧱 Chunk généré: (-7, -2)
🧱 Chunk généré: (-7, -1)
🧱 Chunk généré: (-7, 1)
🧱 Chunk généré: (-7, 2)
🧱 Chunk généré: (-7, 3)
🧱 Chunk généré: (-7, 4)
🧱 Chunk généré: (-6, -6)
🧱 Chunk généré: (-6, -5)
🧱 Chunk généré: (-6, 5)
🧱 Chunk généré: (-6, 6)
🧱 Chunk généré: (-5, -7)
🧱 Chunk généré: (-5, 7)
🧱 Chunk généré: (-4, -8)
🧱 Chunk généré: (-4, 8)
🧱 Chunk généré: (-2, -9)
🧱 Chunk généré: (-2, 9)
🧱 Chunk généré: (2, -10)
🧱 Chunk généré: (2, 10)
📊 Stats: 1 joueurs, 17 TPS, 62.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 19 TPS, 62.00ms/tick
🧱 Chunk généré: (-9, 0)
🧱 Chunk généré: (-8, -4)
🧱 Chunk généré: (-8, -3)
🧱 Chunk généré: (-8, -2)
🧱 Chunk généré: (-8, -1)
🧱 Chunk généré: (-8, 1)
🧱 Chunk généré: (-8, 2)
🧱 Chunk généré: (-8, 3)
🧱 Chunk généré: (-8, 4)
🧱 Chunk généré: (-7, -6)
🧱 Chunk généré: (-7, -5)
🧱 Chunk généré: (-7, 5)
🧱 Chunk généré: (-7, 6)
🧱 Chunk généré: (-6, -7)
🧱 Chunk généré: (-6, 7)
🧱 Chunk généré: (-5, -8)
🧱 Chunk généré: (-5, 8)
🧱 Chunk généré: (-3, -9)
🧱 Chunk généré: (-3, 9)
🧱 Chunk généré: (1, -10)
🧱 Chunk généré: (1, 10)
👋 Joueur déconnecté: dp7djis91mdhk8l67
👋 Player dp7djis91mdhk8l67 a quitté le jeu
👤 Nouveau joueur connecté: 00bgkf0b6mdhk9gd9
👤 Player 00bgkf0b6mdhk9gd9 a rejoint le jeu
👤 Joueur 00bgkf0b6mdhk9gd9 créé à la position (0, 100, 0)
🧱 Chunk généré: (4, -10)
🧱 Chunk généré: (5, -11)
🧱 Chunk généré: (5, -10)
🧱 Chunk généré: (6, -10)
🧱 Chunk généré: (7, -10)
🧱 Chunk généré: (8, -10)
🧱 Chunk généré: (8, -9)
🧱 Chunk généré: (9, -10)
🧱 Chunk généré: (9, -9)
🧱 Chunk généré: (10, -9)
🧱 Chunk généré: (10, -8)
🧱 Chunk généré: (11, -9)
🧱 Chunk généré: (11, -8)
🧱 Chunk généré: (11, -7)
🧱 Chunk généré: (11, 7)
🧱 Chunk généré: (12, -8)
🧱 Chunk généré: (12, -7)
🧱 Chunk généré: (12, -6)
🧱 Chunk généré: (12, -5)
🧱 Chunk généré: (12, 5)
🧱 Chunk généré: (12, 6)
🧱 Chunk généré: (13, -7)
🧱 Chunk généré: (13, -6)
🧱 Chunk généré: (13, -5)
🧱 Chunk généré: (13, -4)
🧱 Chunk généré: (13, -3)
🧱 Chunk généré: (13, -2)
🧱 Chunk généré: (13, -1)
🧱 Chunk généré: (13, 1)
🧱 Chunk généré: (13, 2)
🧱 Chunk généré: (13, 3)
🧱 Chunk généré: (13, 4)
🧱 Chunk généré: (13, 5)
🧱 Chunk généré: (14, -5)
🧱 Chunk généré: (14, -4)
🧱 Chunk généré: (14, -3)
🧱 Chunk généré: (14, -2)
🧱 Chunk généré: (14, -1)
🧱 Chunk généré: (14, 0)
🧱 Chunk généré: (14, 1)
🧱 Chunk généré: (14, 2)
🧱 Chunk généré: (14, 3)
🧱 Chunk généré: (15, -1)
🗑️ 37 chunks déchargés
📊 Stats: 1 joueurs, 17 TPS, 62.00ms/tick
🧱 Chunk généré: (5, 10)
🧱 Chunk généré: (8, 9)
🧱 Chunk généré: (9, 9)
🧱 Chunk généré: (10, 8)
🧱 Chunk généré: (11, 8)
🧱 Chunk généré: (12, 7)
🧱 Chunk généré: (13, 6)
🧱 Chunk généré: (14, 4)
🧱 Chunk généré: (15, 0)
📊 Stats: 1 joueurs, 16 TPS, 62.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 64.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 62.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 64.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 64.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 62.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 64.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 62.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 62.00ms/tick

