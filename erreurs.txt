INDEX : Logs du terminal console du serveur ci bas a partir de la ligne : 4
INDEX : Logs du navigateur a partir de la ligne : 4948

Error: Could not establish connection. Receiving end does not exist. 2 content-script-start.js:3650:37
🎮 Game client initialisé main.js:37:17
🚀 Initialisation du client... main.js:42:21
🖥️ UIManager initialisé UIManager.js:13:17
⚙️ OptionsManager initialisé OptionsManager.js:47:17
⚙️ Options par défaut utilisées OptionsManager.js:374:25
⚙️ Options chargées et appliquées OptionsManager.js:57:17
🎨 Renderer initialisé Renderer.js:24:17
💡 Éclairage configuré Renderer.js:99:17
🌫️ Brouillard configuré Renderer.js:109:17
✅ Renderer initialisé avec succès Renderer.js:61:21
📊 WebGL: WebGL2 Renderer.js:62:21
📊 Max textures: 16 Renderer.js:63:21
🌍 ClientWorld initialisé ClientWorld.js:33:17
📷 PlayerCamera initialisée à la position: 
Object { x: 0, y: 101.7, z: 0 }
PlayerCamera.js:29:17
🎒 Inventory initialisé Inventory.js:24:17
🎮 PlayerController initialisé PlayerController.js:43:17
💬 ChatManager initialisé ChatManager.js:25:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:106:25
✅ Client initialisé avec succès main.js:72:21
🎮 Démarrage de la boucle de jeu main.js:184:17
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🎨 Infos de rendu: main.js:237:25
- Scène enfants: 2 main.js:238:25
- Position caméra: 
Object { x: 0, y: 101.7, z: 0 }
main.js:239:25
- Rotation caméra: 
Object { _x: 0, _y: 0, _z: 0, _order: "XYZ", _onChangeCallback: Ce() }
main.js:240:25
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 10 PlayerController.js:264:21
📦 Réception chunk: (-1, 0) ClientWorld.js:90:17
🎨 Initialisation des ressources partagées... ClientChunk.js:30:17
✅ Géométrie partagée créée ClientChunk.js:40:17
🎨 Ressources partagées initialisées ClientChunk.js:76:17
📦 ClientChunk créé: (-1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 0): 7 types de blocs, 4898 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 0), distance: 1.00 ClientWorld.js:112:17
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
👤 ID joueur reçu: 918837wjlmdhktymr main.js:312:17
👤 ID joueur défini: 918837wjlmdhktymr PlayerController.js:500:17
🌱 Seed du monde défini: 825274 ClientWorld.js:38:17
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 64 PlayerController.js:264:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📐 Renderer redimensionné: 506x624 Renderer.js:153:17
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 29 PlayerController.js:264:21
🔍 Appel de hideConnectionScreen et showInstructions... main.js:100:29
🔍 UIManager.hideConnectionScreen() appelée UIManager.js:99:17
🔍 Element connectionScreen: true UIManager.js:100:17
🔍 Element uiOverlay: true UIManager.js:101:17
🔍 Classes connectionScreen avant: <empty string> UIManager.js:104:21
🔍 Classes connectionScreen après: hidden UIManager.js:106:21
🔍 Classes uiOverlay avant: hidden UIManager.js:112:21
🔍 Classes uiOverlay après: <empty string> UIManager.js:114:21
✅ hideConnectionScreen terminée UIManager.js:119:17
🔍 UIManager.showInstructions() appelée UIManager.js:139:17
🔍 Element instructions: true UIManager.js:140:17
🔍 Classes instructions avant: hidden UIManager.js:143:21
🔍 Classes instructions après: <empty string> UIManager.js:145:21
✅ Instructions affichées UIManager.js:146:21
✅ Connecté au serveur et prêt à jouer main.js:103:29
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 5 PlayerController.js:264:21
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:485:43
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 19 PlayerController.js:264:21
🔒 Pointer lock activé PlayerController.js:147:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 191 PlayerController.js:264:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "KeyA", "Space" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0150", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "KeyA", "Space" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "KeyA", "Space" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "KeyA", "Space" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "KeyA", "Space" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0150", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "KeyA", "KeyD" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "KeyA", "KeyD" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyD" ]
 moveX: 1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 57 PlayerController.js:264:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA", "KeyS" ]
 moveX: -1 moveZ: 1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyA" ]
 moveX: -1 moveZ: 0 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "KeyA" ]
 moveX: -1 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🎯 Première position reçue du serveur: 
Object { x: -8, y: 73.7, z: 3 }
PlayerController.js:353:25
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "3.16", y: "73.70", z: "-0.26" }
PlayerController.js:448:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1.5, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ShiftLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "Space" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array(3) [ "KeyW", "Space", "ControlLeft" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
🦘 [JUMP] Tentative de saut (géré par serveur) PlayerController.js:273:25
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement PlayerController.js:264:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:298:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 42 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "24.07", y: "129.46", z: "5.38" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 7 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "32.01", y: "129.40", z: "7.34" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 8 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "38.85", y: "129.19", z: "9.03" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 7 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "44.76", y: "128.82", z: "10.48" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 8 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "49.87", y: "129.16", z: "11.74" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 7 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "54.31", y: "128.97", z: "12.83" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 24 PlayerController.js:264:21
🔓 Pointer lock désactivé PlayerController.js:154:21
🔍 Pointer lock désactivé après avoir été actif - vérifier la cause PlayerController.js:157:25
console.trace() Stack trace de la désactivation du pointer lock PlayerController.js:158:25
    onPointerLockChange http://localhost:3000/js/player/PlayerController.js:158
    setupEventListeners http://localhost:3000/js/player/PlayerController.js:58
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 16 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "58.17", y: "129.20", z: "13.78" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 3 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "61.89", y: "129.17", z: "14.68" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 8 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "65.14", y: "128.98", z: "15.46" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 7 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "67.99", y: "129.20", z: "16.15" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 8 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "70.51", y: "129.09", z: "16.76" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 10 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "72.75", y: "128.82", z: "17.29" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 5 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "74.73", y: "129.16", z: "17.77" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 7 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "76.52", y: "128.97", z: "18.19" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 8 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "78.12", y: "129.20", z: "18.57" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 7 PlayerController.js:264:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:358:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:425:21
🔧 [CORRECTION] Après correction: 
Object { x: "79.57", y: "129.08", z: "18.92" }
PlayerController.js:448:21
🚫 [GRAVITY] Gravité côté client désactivée - gérée par le serveur uniquement 5 PlayerController.js:264:21
