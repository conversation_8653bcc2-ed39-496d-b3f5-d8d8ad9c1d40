INDEX : Logs du terminal console du serveur ci bas a partir de la ligne 3 et a partir de la ligne 452 de ce fichier les logs du navigateur :

PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> npm start

> jscraft-server@1.0.0 start
> node server/server.js

🌱 WorldGenerator initialisé avec seed: 891165
🌍 WorldManager initialisé avec seed: 891165
🎮 GameManager initialisé
🌐 Serveur WebSocket initialisé
🚀 Serveur JScraft démarré sur http://localhost:3000
📁 Fichiers client servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\client
🔗 Fichiers partagés servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\shared
🎮 Boucle de jeu serveur démarrée (20 ticks/s)
📊 Stats: 0 joueurs, 17 TPS, 63.00ms/tick
👤 Nouveau joueur connecté: vxydzayh6mdhjro5n
👤 Player vxydzayh6mdhjro5n a rejoint le jeu
👤 Joueur vxydzayh6mdhjro5n créé à la position (0, 100, 0)
🧱 Chunk généré: (0, -5)
🧱 Chunk généré: (0, -6)
🧱 Chunk généré: (-10, -5)
🧱 Chunk généré: (-9, -9)
🧱 Chunk généré: (-9, -8)
🧱 Chunk généré: (-9, -7)
🧱 Chunk généré: (-9, -6)
🧱 Chunk généré: (-9, -5)
🧱 Chunk généré: (-9, -4)
🧱 Chunk généré: (-9, -3)
🧱 Chunk généré: (-9, -2)
🧱 Chunk généré: (-9, -1)
🧱 Chunk généré: (-8, -11)
🧱 Chunk généré: (-8, -10)
🧱 Chunk généré: (-8, -9)
🧱 Chunk généré: (-8, -8)
🧱 Chunk généré: (-8, -7)
🧱 Chunk généré: (-8, -6)
🧱 Chunk généré: (-8, -5)
🧱 Chunk généré: (-8, -4)
🧱 Chunk généré: (-8, -3)
🧱 Chunk généré: (-8, -2)
🧱 Chunk généré: (-8, -1)
🧱 Chunk généré: (-8, 0)
🧱 Chunk généré: (-8, 1)
🧱 Chunk généré: (-7, -12)
🧱 Chunk généré: (-7, -11)
🧱 Chunk généré: (-7, -10)
🧱 Chunk généré: (-7, -9)
🧱 Chunk généré: (-7, -8)
🧱 Chunk généré: (-7, -7)
🧱 Chunk généré: (-7, -6)
🧱 Chunk généré: (-7, -5)
🧱 Chunk généré: (-7, -4)
🧱 Chunk généré: (-7, -3)
🧱 Chunk généré: (-7, -2)
🧱 Chunk généré: (-7, -1)
🧱 Chunk généré: (-7, 0)
🧱 Chunk généré: (-7, 1)
🧱 Chunk généré: (-7, 2)
🧱 Chunk généré: (-6, -13)
🧱 Chunk généré: (-6, -12)
🧱 Chunk généré: (-6, -11)
🧱 Chunk généré: (-6, -10)
🧱 Chunk généré: (-6, -9)
🧱 Chunk généré: (-6, -8)
🧱 Chunk généré: (-6, -7)
🧱 Chunk généré: (-6, -6)
🧱 Chunk généré: (-6, -5)
🧱 Chunk généré: (-6, -4)
🧱 Chunk généré: (-6, -3)
🧱 Chunk généré: (-6, -2)
🧱 Chunk généré: (-6, -1)
🧱 Chunk généré: (-6, 0)
🧱 Chunk généré: (-6, 1)
🧱 Chunk généré: (-6, 2)
🧱 Chunk généré: (-6, 3)
🧱 Chunk généré: (-5, -13)
🧱 Chunk généré: (-5, -12)
🧱 Chunk généré: (-5, -11)
🧱 Chunk généré: (-5, -10)
🧱 Chunk généré: (-5, -9)
🧱 Chunk généré: (-5, -8)
🧱 Chunk généré: (-5, -7)
🧱 Chunk généré: (-5, -6)
🧱 Chunk généré: (-5, -5)
🧱 Chunk généré: (-5, -4)
🧱 Chunk généré: (-5, -3)
🧱 Chunk généré: (-5, -2)
🧱 Chunk généré: (-5, -1)
🧱 Chunk généré: (-5, 0)
🧱 Chunk généré: (-5, 1)
🧱 Chunk généré: (-5, 2)
🧱 Chunk généré: (-5, 3)
🧱 Chunk généré: (-4, -14)
🧱 Chunk généré: (-4, -13)
🧱 Chunk généré: (-4, -12)
🧱 Chunk généré: (-4, -11)
🧱 Chunk généré: (-4, -10)
🧱 Chunk généré: (-4, -9)
🧱 Chunk généré: (-4, -8)
🧱 Chunk généré: (-4, -7)
🧱 Chunk généré: (-4, -6)
🧱 Chunk généré: (-4, -5)
🧱 Chunk généré: (-4, -4)
🧱 Chunk généré: (-4, -3)
🧱 Chunk généré: (-4, -2)
🧱 Chunk généré: (-4, -1)
🧱 Chunk généré: (-4, 0)
🧱 Chunk généré: (-4, 1)
🧱 Chunk généré: (-4, 2)
🧱 Chunk généré: (-4, 3)
🧱 Chunk généré: (-4, 4)
🧱 Chunk généré: (-3, -14)
🧱 Chunk généré: (-3, -13)
🧱 Chunk généré: (-3, -12)
🧱 Chunk généré: (-3, -11)
🧱 Chunk généré: (-3, -10)
🧱 Chunk généré: (-3, -9)
🧱 Chunk généré: (-3, -8)
🧱 Chunk généré: (-3, -7)
🧱 Chunk généré: (-3, -6)
🧱 Chunk généré: (-3, -5)
🧱 Chunk généré: (-3, -4)
🧱 Chunk généré: (-3, -3)
🧱 Chunk généré: (-3, -2)
🧱 Chunk généré: (-3, -1)
🧱 Chunk généré: (-3, 0)
🧱 Chunk généré: (-3, 1)
🧱 Chunk généré: (-3, 2)
🧱 Chunk généré: (-3, 3)
🧱 Chunk généré: (-3, 4)
🧱 Chunk généré: (-2, -14)
🧱 Chunk généré: (-2, -13)
🧱 Chunk généré: (-2, -12)
🧱 Chunk généré: (-2, -11)
🧱 Chunk généré: (-2, -10)
🧱 Chunk généré: (-2, -9)
🧱 Chunk généré: (-2, -8)
🧱 Chunk généré: (-2, -7)
🧱 Chunk généré: (-2, -6)
🧱 Chunk généré: (-2, -5)
🧱 Chunk généré: (-2, -4)
🧱 Chunk généré: (-2, -3)
🧱 Chunk généré: (-2, -2)
🧱 Chunk généré: (-2, -1)
🧱 Chunk généré: (-2, 0)
🧱 Chunk généré: (-2, 1)
🧱 Chunk généré: (-2, 2)
🧱 Chunk généré: (-2, 3)
🧱 Chunk généré: (-2, 4)
🧱 Chunk généré: (-1, -14)
🧱 Chunk généré: (-1, -13)
🧱 Chunk généré: (-1, -12)
🧱 Chunk généré: (-1, -11)
🧱 Chunk généré: (-1, -10)
🧱 Chunk généré: (-1, -9)
🧱 Chunk généré: (-1, -8)
🧱 Chunk généré: (-1, -7)
🧱 Chunk généré: (-1, -6)
🧱 Chunk généré: (-1, -5)
🧱 Chunk généré: (-1, -4)
🧱 Chunk généré: (-1, -3)
🧱 Chunk généré: (-1, -2)
🧱 Chunk généré: (-1, -1)
🧱 Chunk généré: (-1, 0)
🧱 Chunk généré: (-1, 1)
🧱 Chunk généré: (-1, 2)
🧱 Chunk généré: (-1, 3)
🧱 Chunk généré: (-1, 4)
🧱 Chunk généré: (0, -15)
🧱 Chunk généré: (0, -14)
🧱 Chunk généré: (0, -13)
🧱 Chunk généré: (0, -12)
🧱 Chunk généré: (0, -11)
🧱 Chunk généré: (0, -10)
🧱 Chunk généré: (0, -9)
🧱 Chunk généré: (0, -8)
🧱 Chunk généré: (0, -7)
🧱 Chunk généré: (0, -4)
🧱 Chunk généré: (0, -3)
🧱 Chunk généré: (0, -2)
🧱 Chunk généré: (0, -1)
🧱 Chunk généré: (0, 0)
🧱 Chunk généré: (0, 1)
🧱 Chunk généré: (0, 2)
🧱 Chunk généré: (0, 3)
🧱 Chunk généré: (0, 4)
🧱 Chunk généré: (0, 5)
🧱 Chunk généré: (1, -14)
🧱 Chunk généré: (1, -13)
🧱 Chunk généré: (1, -12)
🧱 Chunk généré: (1, -11)
🧱 Chunk généré: (1, -10)
🧱 Chunk généré: (1, -9)
🧱 Chunk généré: (1, -8)
🧱 Chunk généré: (1, -7)
🧱 Chunk généré: (1, -6)
🧱 Chunk généré: (1, -5)
🧱 Chunk généré: (1, -4)
🧱 Chunk généré: (1, -3)
🧱 Chunk généré: (1, -2)
🧱 Chunk généré: (1, -1)
🧱 Chunk généré: (1, 0)
🧱 Chunk généré: (1, 1)
🧱 Chunk généré: (1, 2)
🧱 Chunk généré: (1, 3)
🧱 Chunk généré: (1, 4)
🧱 Chunk généré: (2, -14)
🧱 Chunk généré: (2, -13)
🧱 Chunk généré: (2, -12)
🧱 Chunk généré: (2, -11)
🧱 Chunk généré: (2, -10)
🧱 Chunk généré: (2, -9)
🧱 Chunk généré: (2, -8)
🧱 Chunk généré: (2, -7)
🧱 Chunk généré: (2, -6)
🧱 Chunk généré: (2, -5)
🧱 Chunk généré: (2, -4)
🧱 Chunk généré: (2, -3)
🧱 Chunk généré: (2, -2)
🧱 Chunk généré: (2, -1)
🧱 Chunk généré: (2, 0)
🧱 Chunk généré: (2, 1)
🧱 Chunk généré: (2, 2)
🧱 Chunk généré: (2, 3)
🧱 Chunk généré: (2, 4)
🧱 Chunk généré: (3, -14)
🧱 Chunk généré: (3, -13)
🧱 Chunk généré: (3, -12)
🧱 Chunk généré: (3, -11)
🧱 Chunk généré: (3, -10)
🧱 Chunk généré: (3, -9)
🧱 Chunk généré: (3, -8)
🧱 Chunk généré: (3, -7)
🧱 Chunk généré: (3, -6)
🧱 Chunk généré: (3, -5)
🧱 Chunk généré: (3, -4)
🧱 Chunk généré: (3, -3)
🧱 Chunk généré: (3, -2)
🧱 Chunk généré: (3, -1)
🧱 Chunk généré: (3, 0)
🧱 Chunk généré: (3, 1)
🧱 Chunk généré: (3, 2)
🧱 Chunk généré: (3, 3)
🧱 Chunk généré: (3, 4)
🧱 Chunk généré: (4, -14)
🧱 Chunk généré: (4, -13)
🧱 Chunk généré: (4, -12)
🧱 Chunk généré: (4, -11)
🧱 Chunk généré: (4, -10)
🧱 Chunk généré: (4, -9)
🧱 Chunk généré: (4, -8)
🧱 Chunk généré: (4, -7)
🧱 Chunk généré: (4, -6)
🧱 Chunk généré: (4, -5)
🧱 Chunk généré: (4, -4)
🧱 Chunk généré: (4, -3)
🧱 Chunk généré: (4, -2)
🧱 Chunk généré: (4, -1)
🧱 Chunk généré: (4, 0)
🧱 Chunk généré: (4, 1)
🧱 Chunk généré: (4, 2)
🧱 Chunk généré: (4, 3)
🧱 Chunk généré: (4, 4)
🧱 Chunk généré: (5, -13)
🧱 Chunk généré: (5, -12)
🧱 Chunk généré: (5, -11)
🧱 Chunk généré: (5, -10)
🧱 Chunk généré: (5, -9)
🧱 Chunk généré: (5, -8)
🧱 Chunk généré: (5, -7)
🧱 Chunk généré: (5, -6)
🧱 Chunk généré: (5, -5)
🧱 Chunk généré: (5, -4)
🧱 Chunk généré: (5, -3)
🧱 Chunk généré: (5, -2)
🧱 Chunk généré: (5, -1)
🧱 Chunk généré: (5, 0)
🧱 Chunk généré: (5, 1)
🧱 Chunk généré: (5, 2)
🧱 Chunk généré: (5, 3)
🧱 Chunk généré: (6, -13)
🧱 Chunk généré: (6, -12)
🧱 Chunk généré: (6, -11)
🧱 Chunk généré: (6, -10)
🧱 Chunk généré: (6, -9)
🧱 Chunk généré: (6, -8)
🧱 Chunk généré: (6, -7)
🧱 Chunk généré: (6, -6)
🧱 Chunk généré: (6, -5)
🧱 Chunk généré: (6, -4)
🧱 Chunk généré: (6, -3)
🧱 Chunk généré: (6, -2)
🧱 Chunk généré: (6, -1)
🧱 Chunk généré: (6, 0)
🧱 Chunk généré: (6, 1)
🧱 Chunk généré: (6, 2)
🧱 Chunk généré: (6, 3)
🧱 Chunk généré: (7, -12)
🧱 Chunk généré: (7, -11)
🧱 Chunk généré: (7, -10)
🧱 Chunk généré: (7, -9)
🧱 Chunk généré: (7, -8)
🧱 Chunk généré: (7, -7)
🧱 Chunk généré: (7, -6)
🧱 Chunk généré: (7, -5)
🧱 Chunk généré: (7, -4)
🧱 Chunk généré: (7, -3)
🧱 Chunk généré: (7, -2)
🧱 Chunk généré: (7, -1)
🧱 Chunk généré: (7, 0)
🧱 Chunk généré: (7, 1)
🧱 Chunk généré: (7, 2)
🧱 Chunk généré: (8, -11)
🧱 Chunk généré: (8, -10)
🧱 Chunk généré: (8, -9)
🧱 Chunk généré: (8, -8)
🧱 Chunk généré: (8, -7)
🧱 Chunk généré: (8, -6)
🧱 Chunk généré: (8, -5)
🧱 Chunk généré: (8, -4)
🧱 Chunk généré: (8, -3)
🧱 Chunk généré: (8, -2)
🧱 Chunk généré: (8, -1)
🧱 Chunk généré: (8, 0)
🧱 Chunk généré: (8, 1)
🧱 Chunk généré: (9, -9)
🧱 Chunk généré: (9, -8)
🧱 Chunk généré: (9, -7)
🧱 Chunk généré: (9, -6)
🧱 Chunk généré: (9, -5)
🧱 Chunk généré: (9, -4)
🧱 Chunk généré: (9, -3)
🧱 Chunk généré: (9, -2)
🧱 Chunk généré: (9, -1)
🧱 Chunk généré: (10, -5)
🧱 Chunk généré: (-12, -7)
🧱 Chunk généré: (-11, -11)
🧱 Chunk généré: (-11, -10)
🧱 Chunk généré: (-11, -9)
🧱 Chunk généré: (-11, -8)
🧱 Chunk généré: (-11, -7)
🧱 Chunk généré: (-11, -6)
🧱 Chunk généré: (-11, -5)
🧱 Chunk généré: (-11, -4)
🧱 Chunk généré: (-11, -3)
🧱 Chunk généré: (-10, -13)
🧱 Chunk généré: (-10, -12)
🧱 Chunk généré: (-10, -11)
🧱 Chunk généré: (-10, -10)
🧱 Chunk généré: (-10, -9)
🧱 Chunk généré: (-10, -8)
🧱 Chunk généré: (-10, -7)
🧱 Chunk généré: (-10, -6)
🧱 Chunk généré: (-10, -4)
🧱 Chunk généré: (-10, -3)
🧱 Chunk généré: (-10, -2)
🧱 Chunk généré: (-10, -1)
🧱 Chunk généré: (-9, -14)
🧱 Chunk généré: (-9, -13)
🧱 Chunk généré: (-9, -12)
🧱 Chunk généré: (-9, -11)
🧱 Chunk généré: (-9, -10)
🧱 Chunk généré: (-9, 0)
🧱 Chunk généré: (-8, -15)
🧱 Chunk généré: (-8, -14)
🧱 Chunk généré: (-8, -13)
🧱 Chunk généré: (-8, -12)
🧱 Chunk généré: (-7, -15)
🧱 Chunk généré: (-7, -14)
🧱 Chunk généré: (-7, -13)
🧱 Chunk généré: (-6, -16)
🧱 Chunk généré: (-6, -15)
🧱 Chunk généré: (-6, -14)
🧱 Chunk généré: (-5, -16)
🧱 Chunk généré: (-5, -15)
🧱 Chunk généré: (-5, -14)
🧱 Chunk généré: (-4, -16)
🧱 Chunk généré: (-4, -15)
🧱 Chunk généré: (-3, -16)
🧱 Chunk généré: (-3, -15)
🧱 Chunk généré: (-2, -17)
🧱 Chunk généré: (-2, -16)
🧱 Chunk généré: (-2, -15)
🧱 Chunk généré: (-1, -16)
🧱 Chunk généré: (-1, -15)
🧱 Chunk généré: (0, -16)
🧱 Chunk généré: (1, -16)
🧱 Chunk généré: (1, -15)
🧱 Chunk généré: (2, -16)
🧱 Chunk généré: (2, -15)
🧱 Chunk généré: (3, -15)
🧱 Chunk généré: (4, -15)
🧱 Chunk généré: (5, -14)
📊 Stats: 1 joueurs, 1 TPS, 7079.00ms/tick
🧱 Chunk généré: (-12, -8)
🧱 Chunk généré: (-11, -12)
🧱 Chunk généré: (-10, -14)
🧱 Chunk généré: (-9, -15)
🧱 Chunk généré: (-8, -16)
🧱 Chunk généré: (-7, -16)
🧱 Chunk généré: (-6, -17)
🧱 Chunk généré: (-5, -17)
🧱 Chunk généré: (-4, -17)
🧱 Chunk généré: (-3, -17)
🧱 Chunk généré: (-2, -18)
🧱 Chunk généré: (-1, -17)
🧱 Chunk généré: (0, -17)
🧱 Chunk généré: (1, -17)
🧱 Chunk généré: (2, -17)
🧱 Chunk généré: (3, -16)
🧱 Chunk généré: (4, -16)
🧱 Chunk généré: (5, -15)
🧱 Chunk généré: (6, -14)
📊 Stats: 1 joueurs, 20 TPS, 61.00ms/tick
🧱 Chunk généré: (7, -13)
👋 Joueur déconnecté: vxydzayh6mdhjro5n
👋 Player vxydzayh6mdhjro5n a quitté le jeu
👤 Nouveau joueur connecté: t4pvmiz84mdhjs4wl
👤 Player t4pvmiz84mdhjs4wl a rejoint le jeu
👤 Joueur t4pvmiz84mdhjs4wl créé à la position (0, 100, 0)
🧱 Chunk généré: (-6, 4)
🧱 Chunk généré: (-5, 4)
🧱 Chunk généré: (-5, 5)
🧱 Chunk généré: (-5, 6)
🧱 Chunk généré: (-5, 7)
🧱 Chunk généré: (-5, 8)
🧱 Chunk généré: (-4, 5)
🧱 Chunk généré: (-4, 6)
🧱 Chunk généré: (-4, 7)
🧱 Chunk généré: (-4, 8)
🧱 Chunk généré: (-4, 9)
🧱 Chunk généré: (-4, 10)
🧱 Chunk généré: (-3, 5)
🧱 Chunk généré: (-3, 6)
🧱 Chunk généré: (-3, 7)
🧱 Chunk généré: (-3, 8)
🧱 Chunk généré: (-3, 9)
🧱 Chunk généré: (-3, 10)
🧱 Chunk généré: (-3, 11)
🧱 Chunk généré: (-2, 5)
🧱 Chunk généré: (-2, 6)
🧱 Chunk généré: (-2, 7)
🧱 Chunk généré: (-2, 8)
🧱 Chunk généré: (-2, 9)
🧱 Chunk généré: (-2, 10)
🧱 Chunk généré: (-2, 11)
🧱 Chunk généré: (-2, 12)
🧱 Chunk généré: (-1, 5)
🧱 Chunk généré: (-1, 6)
🧱 Chunk généré: (-1, 7)
🧱 Chunk généré: (-1, 8)
🧱 Chunk généré: (-1, 9)
🧱 Chunk généré: (-1, 10)
🧱 Chunk généré: (-1, 11)
🧱 Chunk généré: (-1, 12)
🧱 Chunk généré: (0, 6)
🧱 Chunk généré: (0, 7)
🧱 Chunk généré: (0, 8)
🧱 Chunk généré: (0, 9)
🧱 Chunk généré: (0, 10)
🧱 Chunk généré: (0, 11)
🧱 Chunk généré: (0, 12)
🧱 Chunk généré: (0, 13)
🧱 Chunk généré: (1, 5)
🧱 Chunk généré: (1, 6)
🧱 Chunk généré: (1, 7)
🧱 Chunk généré: (1, 8)
🧱 Chunk généré: (1, 9)
🧱 Chunk généré: (1, 10)
🧱 Chunk généré: (1, 11)
🧱 Chunk généré: (1, 12)
🧱 Chunk généré: (1, 13)
🧱 Chunk généré: (2, 5)
🧱 Chunk généré: (2, 6)
🧱 Chunk généré: (2, 7)
🧱 Chunk généré: (2, 8)
🧱 Chunk généré: (2, 9)
🧱 Chunk généré: (2, 10)
🧱 Chunk généré: (2, 11)
🧱 Chunk généré: (2, 12)
🧱 Chunk généré: (2, 13)
🧱 Chunk généré: (3, 5)
🧱 Chunk généré: (3, 6)
🧱 Chunk généré: (3, 7)
🧱 Chunk généré: (3, 8)
🧱 Chunk généré: (3, 9)
🧱 Chunk généré: (3, 10)
🧱 Chunk généré: (3, 11)
🧱 Chunk généré: (3, 12)
🧱 Chunk généré: (3, 13)
🧱 Chunk généré: (4, 5)
🧱 Chunk généré: (4, 6)
🧱 Chunk généré: (4, 7)
🧱 Chunk généré: (4, 8)
🧱 Chunk généré: (4, 9)
🧱 Chunk généré: (4, 10)
🧱 Chunk généré: (4, 11)
🧱 Chunk généré: (4, 12)
🧱 Chunk généré: (4, 13)
🧱 Chunk généré: (4, 14)
🧱 Chunk généré: (5, 4)
🧱 Chunk généré: (5, 5)
🧱 Chunk généré: (5, 6)
🧱 Chunk généré: (5, 7)
🧱 Chunk généré: (5, 8)
🧱 Chunk généré: (5, 9)
🧱 Chunk généré: (5, 10)
🧱 Chunk généré: (5, 11)
🧱 Chunk généré: (5, 12)
🧱 Chunk généré: (5, 13)
🧱 Chunk généré: (6, 4)
🧱 Chunk généré: (6, 5)
🧱 Chunk généré: (6, 6)
🧱 Chunk généré: (6, 7)
🧱 Chunk généré: (6, 8)
🧱 Chunk généré: (6, 9)
🧱 Chunk généré: (6, 10)
🧱 Chunk généré: (6, 11)
🧱 Chunk généré: (6, 12)
🧱 Chunk généré: (6, 13)
🧱 Chunk généré: (7, 3)
🧱 Chunk généré: (7, 4)
🧱 Chunk généré: (7, 5)
🧱 Chunk généré: (7, 6)
🧱 Chunk généré: (7, 7)
🧱 Chunk généré: (7, 8)
🧱 Chunk généré: (7, 9)
🧱 Chunk généré: (7, 10)
🧱 Chunk généré: (7, 11)
🧱 Chunk généré: (7, 12)
🧱 Chunk généré: (7, 13)
🧱 Chunk généré: (8, 2)
🧱 Chunk généré: (8, 3)
🧱 Chunk généré: (8, 4)
🧱 Chunk généré: (8, 5)
🧱 Chunk généré: (8, 6)
🧱 Chunk généré: (8, 7)
🧱 Chunk généré: (8, 8)
🧱 Chunk généré: (8, 9)
🧱 Chunk généré: (8, 10)
🧱 Chunk généré: (8, 11)
🧱 Chunk généré: (8, 12)
🧱 Chunk généré: (8, 13)
🧱 Chunk généré: (9, 0)
🧱 Chunk généré: (9, 1)
🧱 Chunk généré: (9, 2)
🧱 Chunk généré: (9, 3)
🧱 Chunk généré: (9, 4)
🧱 Chunk généré: (9, 5)
🧱 Chunk généré: (9, 6)
🧱 Chunk généré: (9, 7)
🧱 Chunk généré: (9, 8)
🧱 Chunk généré: (9, 9)
🧱 Chunk généré: (9, 10)
🧱 Chunk généré: (9, 11)
🧱 Chunk généré: (9, 12)
🧱 Chunk généré: (10, -4)
🧱 Chunk généré: (10, -3)
🧱 Chunk généré: (10, -2)
🧱 Chunk généré: (10, -1)
🧱 Chunk généré: (10, 0)
🧱 Chunk généré: (10, 1)
🧱 Chunk généré: (10, 2)
🧱 Chunk généré: (10, 3)
🧱 Chunk généré: (10, 4)
🧱 Chunk généré: (10, 5)
🧱 Chunk généré: (10, 6)
🧱 Chunk généré: (10, 7)
🧱 Chunk généré: (10, 8)
🧱 Chunk généré: (10, 9)
🧱 Chunk généré: (10, 10)
🧱 Chunk généré: (10, 11)
🧱 Chunk généré: (10, 12)
🧱 Chunk généré: (11, -3)
🧱 Chunk généré: (11, -2)
🧱 Chunk généré: (11, -1)
🧱 Chunk généré: (11, 0)
🧱 Chunk généré: (11, 1)
🧱 Chunk généré: (11, 2)
🧱 Chunk généré: (11, 3)
🧱 Chunk généré: (11, 4)
🧱 Chunk généré: (11, 5)
🧱 Chunk généré: (11, 6)
🧱 Chunk généré: (11, 7)
🧱 Chunk généré: (11, 8)
🧱 Chunk généré: (11, 9)
🧱 Chunk généré: (11, 10)
🧱 Chunk généré: (11, 11)
🧱 Chunk généré: (12, -2)
🧱 Chunk généré: (12, -1)
🧱 Chunk généré: (12, 0)
🧱 Chunk généré: (12, 1)
🧱 Chunk généré: (12, 2)
🧱 Chunk généré: (12, 3)
🧱 Chunk généré: (12, 4)
🧱 Chunk généré: (12, 5)
🧱 Chunk généré: (12, 6)
🧱 Chunk généré: (12, 7)
🧱 Chunk généré: (12, 8)
🧱 Chunk généré: (12, 9)
🧱 Chunk généré: (12, 10)
🧱 Chunk généré: (13, 0)
🧱 Chunk généré: (13, 1)
🧱 Chunk généré: (13, 2)
🧱 Chunk généré: (13, 3)
🧱 Chunk généré: (13, 4)
🧱 Chunk généré: (13, 5)
🧱 Chunk généré: (13, 6)
🧱 Chunk généré: (13, 7)
🧱 Chunk généré: (13, 8)
🧱 Chunk généré: (14, 4)
🗑️ 228 chunks déchargés
📊 Stats: 1 joueurs, 16 TPS, 61.00ms/tick
🧱 Chunk généré: (11, -4)
🧱 Chunk généré: (12, -3)
🧱 Chunk généré: (13, -1)
🧱 Chunk généré: (14, 3)
🧱 Chunk généré: (10, -6)
🧱 Chunk généré: (11, -5)
🧱 Chunk généré: (12, -4)
🧱 Chunk généré: (13, -2)
🧱 Chunk généré: (14, 2)
🧱 Chunk généré: (-4, -5)
🧱 Chunk généré: (-3, -6)
🧱 Chunk généré: (-2, -7)
🧱 Chunk généré: (-1, -7)
🧱 Chunk généré: (0, -8)
🧱 Chunk généré: (1, -8)
🧱 Chunk généré: (2, -8)
🧱 Chunk généré: (3, -8)
🧱 Chunk généré: (4, -9)
🧱 Chunk généré: (5, -8)
🧱 Chunk généré: (6, -8)
🧱 Chunk généré: (7, -8)
🧱 Chunk généré: (8, -8)
🧱 Chunk généré: (9, -7)
🧱 Chunk généré: (10, -7)
🧱 Chunk généré: (11, -6)
🧱 Chunk généré: (12, -5)
🧱 Chunk généré: (13, -3)
🧱 Chunk généré: (14, 1)
📊 Stats: 1 joueurs, 18 TPS, 59.00ms/tick
🧱 Chunk généré: (-5, -4)
🧱 Chunk généré: (-4, -6)
🧱 Chunk généré: (-3, -7)
🧱 Chunk généré: (-2, -8)
🧱 Chunk généré: (-1, -8)
🧱 Chunk généré: (0, -9)
🧱 Chunk généré: (1, -9)
🧱 Chunk généré: (2, -9)
🧱 Chunk généré: (3, -9)
🧱 Chunk généré: (4, -10)
🧱 Chunk généré: (5, -9)
🧱 Chunk généré: (6, -9)
🧱 Chunk généré: (7, -9)
🧱 Chunk généré: (8, -9)
🧱 Chunk généré: (9, -8)
🧱 Chunk généré: (10, -8)
🧱 Chunk généré: (11, -7)
🧱 Chunk généré: (12, -6)
🧱 Chunk généré: (13, -4)
🧱 Chunk généré: (14, 0)
🗑️ 36 chunks déchargés
🧱 Chunk généré: (-6, -4)
🧱 Chunk généré: (-6, -3)
🧱 Chunk généré: (-5, -6)
🧱 Chunk généré: (-5, -5)
🧱 Chunk généré: (-4, -7)
🧱 Chunk généré: (-3, -8)
🧱 Chunk généré: (-1, -9)
🧱 Chunk généré: (3, -10)
🧱 Chunk généré: (-7, -1)
🧱 Chunk généré: (-6, -5)
🧱 Chunk généré: (-5, -7)
🧱 Chunk généré: (-4, -8)
🧱 Chunk généré: (-3, -9)
🧱 Chunk généré: (-2, -9)
🧱 Chunk généré: (-1, -10)
🧱 Chunk généré: (0, -10)
🧱 Chunk généré: (1, -10)
🧱 Chunk généré: (2, -10)
🧱 Chunk généré: (3, -11)
🧱 Chunk généré: (5, -10)
🧱 Chunk généré: (6, -10)
🧱 Chunk généré: (7, -10)
🧱 Chunk généré: (9, -9)
🧱 Chunk généré: (-8, -1)
🧱 Chunk généré: (-7, -5)
🧱 Chunk généré: (-7, -4)
🧱 Chunk généré: (-7, -3)
🧱 Chunk généré: (-7, -2)
🧱 Chunk généré: (-7, 3)
🧱 Chunk généré: (-6, -7)
🧱 Chunk généré: (-6, -6)
🧱 Chunk généré: (-6, 5)
🧱 Chunk généré: (-5, -8)
🧱 Chunk généré: (-4, -9)
🧱 Chunk généré: (-2, -10)
🧱 Chunk généré: (2, -11)
🗑️ 31 chunks déchargés
🧱 Chunk généré: (-8, -2)
🧱 Chunk généré: (-7, -6)
🧱 Chunk généré: (-6, -8)
🧱 Chunk généré: (-5, -9)
🧱 Chunk généré: (-4, -10)
🧱 Chunk généré: (-3, -10)
🧱 Chunk généré: (-2, -11)
🧱 Chunk généré: (-1, -11)
🧱 Chunk généré: (0, -11)
🧱 Chunk généré: (1, -11)
🧱 Chunk généré: (2, -12)
🧱 Chunk généré: (4, -11)
🧱 Chunk généré: (5, -11)
🧱 Chunk généré: (6, -11)
🧱 Chunk généré: (8, -10)
📊 Stats: 1 joueurs, 13 TPS, 62.00ms/tick
🧱 Chunk généré: (-9, -2)
🧱 Chunk généré: (-8, -6)
🧱 Chunk généré: (-8, -5)
🧱 Chunk généré: (-8, -4)
🧱 Chunk généré: (-8, -3)
🧱 Chunk généré: (-8, 0)
🧱 Chunk généré: (-8, 1)
🧱 Chunk généré: (-8, 2)
🧱 Chunk généré: (-7, -8)
🧱 Chunk généré: (-7, -7)
🧱 Chunk généré: (-7, 4)
🧱 Chunk généré: (-6, -9)
🧱 Chunk généré: (-5, -10)
🧱 Chunk généré: (-3, -11)
🧱 Chunk généré: (1, -12)
🗑️ 29 chunks déchargés
🧱 Chunk généré: (-9, -3)
🧱 Chunk généré: (-8, -7)
🧱 Chunk généré: (-7, -9)
🧱 Chunk généré: (-6, -10)
🧱 Chunk généré: (-5, -11)
🧱 Chunk généré: (-4, -11)
🧱 Chunk généré: (-3, -12)
🧱 Chunk généré: (-2, -12)
🧱 Chunk généré: (-1, -12)
🧱 Chunk généré: (0, -12)
🧱 Chunk généré: (1, -13)
🧱 Chunk généré: (3, -12)
🧱 Chunk généré: (4, -12)
🧱 Chunk généré: (5, -12)
🧱 Chunk généré: (7, -11)
🧱 Chunk généré: (-10, -3)
🧱 Chunk généré: (-9, -7)
🧱 Chunk généré: (-9, -6)
🧱 Chunk généré: (-9, -5)
🧱 Chunk généré: (-9, -4)
🧱 Chunk généré: (-9, -1)
🧱 Chunk généré: (-9, 0)
🧱 Chunk généré: (-9, 1)
🧱 Chunk généré: (-8, -9)
🧱 Chunk généré: (-8, -8)
🧱 Chunk généré: (-8, 3)
🧱 Chunk généré: (-7, -10)
🧱 Chunk généré: (-6, -11)
🧱 Chunk généré: (-4, -12)
🧱 Chunk généré: (0, -13)
🗑️ 31 chunks déchargés
🧱 Chunk généré: (-10, -4)
🧱 Chunk généré: (-9, -8)
🧱 Chunk généré: (-8, -10)
🧱 Chunk généré: (-7, -11)
🧱 Chunk généré: (-6, -12)
🧱 Chunk généré: (-5, -12)
🧱 Chunk généré: (-4, -13)
🧱 Chunk généré: (-3, -13)
🧱 Chunk généré: (-2, -13)
🧱 Chunk généré: (-1, -13)
🧱 Chunk généré: (0, -14)
🧱 Chunk généré: (2, -13)
🧱 Chunk généré: (3, -13)
🧱 Chunk généré: (4, -13)
🧱 Chunk généré: (6, -12)
📊 Stats: 1 joueurs, 12 TPS, 63.00ms/tick
🧱 Chunk généré: (-11, -4)
🧱 Chunk généré: (-10, -8)
🧱 Chunk généré: (-10, -7)
🧱 Chunk généré: (-10, -6)
🧱 Chunk généré: (-10, -5)
🧱 Chunk généré: (-10, -2)
🧱 Chunk généré: (-10, -1)
🧱 Chunk généré: (-10, 0)
🧱 Chunk généré: (-9, -10)
🧱 Chunk généré: (-9, -9)
🧱 Chunk généré: (-9, 2)
🧱 Chunk généré: (-8, -11)
🧱 Chunk généré: (-7, -12)
🧱 Chunk généré: (-5, -13)
🧱 Chunk généré: (-1, -14)
🗑️ 31 chunks déchargés
🧱 Chunk généré: (-11, -5)
🧱 Chunk généré: (-10, -9)
🧱 Chunk généré: (-9, -11)
🧱 Chunk généré: (-8, -12)
🧱 Chunk généré: (-7, -13)
🧱 Chunk généré: (-6, -13)
🧱 Chunk généré: (-5, -14)
🧱 Chunk généré: (-4, -14)
🧱 Chunk généré: (-3, -14)
🧱 Chunk généré: (-2, -14)
🧱 Chunk généré: (-1, -15)
🧱 Chunk généré: (1, -14)
🧱 Chunk généré: (2, -14)
🧱 Chunk généré: (3, -14)
🧱 Chunk généré: (5, -13)
🧱 Chunk généré: (-12, -5)
🧱 Chunk généré: (-11, -9)
🧱 Chunk généré: (-11, -8)
🧱 Chunk généré: (-11, -7)
🧱 Chunk généré: (-11, -6)
🧱 Chunk généré: (-11, -3)
🧱 Chunk généré: (-11, -2)
🧱 Chunk généré: (-11, -1)
🧱 Chunk généré: (-10, -11)
🧱 Chunk généré: (-10, -10)
🧱 Chunk généré: (-10, 1)
🧱 Chunk généré: (-9, -12)
🧱 Chunk généré: (-8, -13)
🧱 Chunk généré: (-6, -14)
🧱 Chunk généré: (-2, -15)
🗑️ 31 chunks déchargés

🎮 Game client initialisé main.js:37:17
🚀 Initialisation du client... main.js:42:21
🖥️ UIManager initialisé UIManager.js:13:17
⚙️ OptionsManager initialisé OptionsManager.js:47:17
⚙️ Options par défaut utilisées OptionsManager.js:374:25
⚙️ Options chargées et appliquées OptionsManager.js:57:17
🎨 Renderer initialisé Renderer.js:24:17
💡 Éclairage configuré Renderer.js:99:17
🌫️ Brouillard configuré Renderer.js:109:17
✅ Renderer initialisé avec succès Renderer.js:61:21
📊 WebGL: WebGL2 Renderer.js:62:21
📊 Max textures: 16 Renderer.js:63:21
🌍 ClientWorld initialisé ClientWorld.js:33:17
📷 PlayerCamera initialisée à la position: 
Object { x: 0, y: 101.7, z: 0 }
PlayerCamera.js:29:17
🎒 Inventory initialisé Inventory.js:24:17
🎮 PlayerController initialisé PlayerController.js:43:17
💬 ChatManager initialisé ChatManager.js:25:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:105:25
✅ Client initialisé avec succès main.js:72:21
🎮 Démarrage de la boucle de jeu main.js:183:17
🎨 Infos de rendu: main.js:236:25
- Scène enfants: 2 main.js:237:25
- Position caméra: 
Object { x: 0, y: 101.7, z: 0 }
main.js:238:25
- Rotation caméra: 
Object { _x: 0, _y: 0, _z: 0, _order: "XYZ", _onChangeCallback: Ce() }
main.js:239:25
📦 Réception chunk: (0, -5) ClientWorld.js:90:17
🎨 Initialisation des ressources partagées... ClientChunk.js:30:17
✅ Géométrie partagée créée ClientChunk.js:40:17
🎨 Ressources partagées initialisées ClientChunk.js:76:17
📦 ClientChunk créé: (0, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -5): 7 types de blocs, 5584 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -5), distance: 5.00 ClientWorld.js:112:17
👤 ID joueur reçu: vxydzayh6mdhjro5n main.js:311:17
👤 ID joueur défini: vxydzayh6mdhjro5n PlayerController.js:383:17
🌱 Seed du monde défini: 891165 ClientWorld.js:38:17
📐 Renderer redimensionné: 506x624 Renderer.js:153:17
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
✅ Connecté au serveur et prêt à jouer main.js:102:29
🔒 Pointer lock activé PlayerController.js:147:21
🎯 Première position reçue du serveur: 
Object { x: -22.04619909453149, y: 129.2, z: -101.97817126215223 }
PlayerController.js:288:25
🔓 Pointer lock désactivé PlayerController.js:154:21
🔍 Pointer lock désactivé après avoir été actif - vérifier la cause PlayerController.js:157:25
console.trace() Stack trace de la désactivation du pointer lock PlayerController.js:158:25
    onPointerLockChange http://localhost:3000/js/player/PlayerController.js:158
    setupEventListeners http://localhost:3000/js/player/PlayerController.js:58
🔄 Tentative de reconnexion... main.js:158:17
console.trace() 🔍 Stack trace de la reconnexion main.js:159:17
    reconnect http://localhost:3000/js/main.js:159
    setupEventListeners http://localhost:3000/js/main.js:144
🔌 Déconnexion manuelle SocketClient.js:255:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
🔒 Pointer lock activé PlayerController.js:147:21
🔌 Connexion WebSocket fermée 1000 Déconnexion manuelle SocketClient.js:81:21
❌ Déconnecté du serveur main.js:113:25
GET
ws://localhost:3000/ws
[HTTP/1.1 101 Switching Protocols 2ms]

✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:105:25
📦 Réception chunk: (-5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 0): 8 types de blocs, 4413 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 0), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 1): 8 types de blocs, 4320 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 1), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (-5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 2): 8 types de blocs, 4380 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 2), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (-5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 3): 6 types de blocs, 4187 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 3) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 3), distance: 10.77 ClientWorld.js:112:17
✅ Connecté au serveur et prêt à jouer main.js:102:29
📦 Réception chunk: (-4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -2): 7 types de blocs, 4507 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -2), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -1): 8 types de blocs, 4462 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -1), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 0): 6 types de blocs, 4329 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 0) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 0), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 1): 8 types de blocs, 4324 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 1), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (-4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 2): 8 types de blocs, 4239 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 2), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (-4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 3): 8 types de blocs, 4152 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 3), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (-4, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 4): 8 types de blocs, 4120 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 4), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (-3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -3): 7 types de blocs, 5271 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -3), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -2): 10 types de blocs, 4952 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -2) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -1): 10 types de blocs, 4570 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -1) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -1), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 0): 8 types de blocs, 4329 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 0), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (-3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 1): 6 types de blocs, 4229 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 1), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (-3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 2): 8 types de blocs, 4233 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 2), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 3): 8 types de blocs, 4129 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 3), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (-3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 4): 6 types de blocs, 4051 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 4) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 4), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (-2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -4): 8 types de blocs, 5435 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -4), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -3): 7 types de blocs, 5232 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -3), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -2): 8 types de blocs, 5049 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -2), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -1): 8 types de blocs, 4908 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 0): 9 types de blocs, 4785 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 0), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 1): 8 types de blocs, 4357 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 1), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 2): 8 types de blocs, 4253 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 2), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (-2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 3): 8 types de blocs, 4175 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 3), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (-2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 4): 8 types de blocs, 4114 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 4), distance: 11.05 ClientWorld.js:112:17
📦 Réception chunk: (-1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -4): 8 types de blocs, 5470 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -4), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -3): 7 types de blocs, 5266 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -3), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -2): 7 types de blocs, 5188 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -2), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -1): 8 types de blocs, 5035 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -1), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 0): 8 types de blocs, 4819 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 0), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 1): 7 types de blocs, 4837 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 1), distance: 8.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 2): 10 types de blocs, 4507 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 2) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 2), distance: 9.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 3): 9 types de blocs, 4360 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 3), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 4): 9 types de blocs, 4147 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 4), distance: 11.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -5): 7 types de blocs, 5584 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -5), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (0, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -4): 7 types de blocs, 5532 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -4), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (0, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -3): 7 types de blocs, 5375 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -3), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (0, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -2): 7 types de blocs, 5240 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -2), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (0, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -1): 7 types de blocs, 4923 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (0, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 0): 7 types de blocs, 5021 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 0), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (0, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 1): 7 types de blocs, 4779 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 1), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (0, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 2): 7 types de blocs, 4645 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 2), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (0, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 3): 7 types de blocs, 4550 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 3), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (0, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 4): 8 types de blocs, 4631 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 4), distance: 11.05 ClientWorld.js:112:17
📦 Réception chunk: (0, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 5): 9 types de blocs, 4487 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 5), distance: 12.04 ClientWorld.js:112:17
📦 Réception chunk: (1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -5): 7 types de blocs, 5681 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -5), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -4): 7 types de blocs, 5512 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -4), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -3): 7 types de blocs, 5409 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -3), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -2): 9 types de blocs, 5329 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -1): 7 types de blocs, 5055 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -1), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 0): 7 types de blocs, 4923 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 0), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 1): 7 types de blocs, 4859 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 1), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 2): 7 types de blocs, 4703 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 2), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 3): 7 types de blocs, 4611 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 3), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 4): 7 types de blocs, 4690 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 4), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -5): 8 types de blocs, 5546 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -5), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -4): 8 types de blocs, 5543 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -4), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -3): 7 types de blocs, 5439 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -2): 7 types de blocs, 5277 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -2), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -1): 7 types de blocs, 5173 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -1), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 0): 7 types de blocs, 5015 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 0), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 1): 7 types de blocs, 4802 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 1), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 2): 7 types de blocs, 4837 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 2), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 3): 7 types de blocs, 4696 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 3), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 4): 7 types de blocs, 4604 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 4), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -5): 7 types de blocs, 5854 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -5), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -4): 7 types de blocs, 5518 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -3): 7 types de blocs, 5509 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -3), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -2): 7 types de blocs, 5242 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -2), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -1): 7 types de blocs, 5018 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -1), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 0): 8 types de blocs, 5194 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 0), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 1): 7 types de blocs, 4942 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 1), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 2): 7 types de blocs, 4815 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 2), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 3): 7 types de blocs, 4667 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 3), distance: 10.77 ClientWorld.js:112:17
📦 Réception chunk: (3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 4): 7 types de blocs, 4537 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 4), distance: 11.70 ClientWorld.js:112:17
📦 Réception chunk: (4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -6): 5 types de blocs, 5869 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -6) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -6), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -5): 4 types de blocs, 5837 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -5) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -4): 7 types de blocs, 5726 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -4), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -3): 7 types de blocs, 5646 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -3), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -2): 8 types de blocs, 5213 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -2), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -1): 7 types de blocs, 5147 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -1), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 0): 7 types de blocs, 5077 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 0), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 1): 8 types de blocs, 4918 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 1), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 2): 8 types de blocs, 4786 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 2), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 3): 7 types de blocs, 4724 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 3), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (4, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 4): 6 types de blocs, 4642 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 4) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 4), distance: 12.08 ClientWorld.js:112:17
📦 Réception chunk: (5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -5): 4 types de blocs, 5770 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -5) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -5), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -4): 4 types de blocs, 5769 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -4), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -3): 4 types de blocs, 5539 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -3) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -3), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -2): 7 types de blocs, 5385 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -2), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -1): 7 types de blocs, 5214 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -1), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 0): 6 types de blocs, 4973 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 0) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 0), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 1): 7 types de blocs, 4832 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 1), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 2): 7 types de blocs, 4729 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 2), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 3): 7 types de blocs, 4675 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 3), distance: 11.66 ClientWorld.js:112:17
📦 Réception chunk: (6, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -5): 4 types de blocs, 5695 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -5) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -5), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (6, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -4): 4 types de blocs, 5616 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -4), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (6, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -3): 4 types de blocs, 5517 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -3) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -3), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -2): 5 types de blocs, 5317 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -2) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -2), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -1): 4 types de blocs, 5131 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -1) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -1), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 0): 7 types de blocs, 4938 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 0), distance: 9.90 ClientWorld.js:112:17
📦 Réception chunk: (6, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 1): 6 types de blocs, 4842 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 1), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 2): 7 types de blocs, 4818 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 2), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (6, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 3): 7 types de blocs, 4599 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 3), distance: 12.21 ClientWorld.js:112:17
📦 Réception chunk: (7, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -5): 4 types de blocs, 5620 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -5) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -5), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (7, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -4): 4 types de blocs, 5541 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -4), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (7, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -3): 4 types de blocs, 5409 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -3) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -3), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (7, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -2): 4 types de blocs, 5240 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -2) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -2), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (7, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -1): 4 types de blocs, 5055 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -1) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -1), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (7, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 0): 4 types de blocs, 4856 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 0) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 0), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (7, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 1): 4 types de blocs, 4664 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 1) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 1), distance: 11.31 ClientWorld.js:112:17
📦 Réception chunk: (7, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 2): 4 types de blocs, 4482 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 2) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 2), distance: 12.04 ClientWorld.js:112:17
📦 Réception chunk: (8, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -5): 4 types de blocs, 5549 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -5) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -5), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (8, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -4): 4 types de blocs, 5468 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -4), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (8, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -3): 4 types de blocs, 5331 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -3) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -3), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (8, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -2): 4 types de blocs, 5158 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -2) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -2), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (8, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -1): 4 types de blocs, 4967 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -1) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -1), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (8, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 0): 4 types de blocs, 4775 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 0) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 0), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (8, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 1): 4 types de blocs, 4577 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 1) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 1), distance: 12.04 ClientWorld.js:112:17
📦 Réception chunk: (9, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -4): 7 types de blocs, 5406 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -4), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (9, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -3): 5 types de blocs, 5262 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -3) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -3), distance: 10.77 ClientWorld.js:112:17
📦 Réception chunk: (9, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -2): 4 types de blocs, 5154 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -2) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -2), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (9, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -1): 4 types de blocs, 5150 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, -1) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, -1), distance: 11.66 ClientWorld.js:112:17
👤 ID joueur reçu: t4pvmiz84mdhjs4wl main.js:311:17
👤 ID joueur défini: t4pvmiz84mdhjs4wl PlayerController.js:383:17
🌱 Seed du monde défini: 891165 ClientWorld.js:38:17
📐 Renderer redimensionné: 506x580 Renderer.js:153:17
📐 Renderer redimensionné: 506x624 Renderer.js:153:17
💬 Chat ouvert ChatManager.js:131:17
💬 Chat fermé ChatManager.js:153:17
🔓 Pointer lock désactivé PlayerController.js:154:21
🔍 Pointer lock désactivé après avoir été actif - vérifier la cause PlayerController.js:157:25
console.trace() Stack trace de la désactivation du pointer lock PlayerController.js:158:25
    onPointerLockChange http://localhost:3000/js/player/PlayerController.js:158
    setupEventListeners http://localhost:3000/js/player/PlayerController.js:58
