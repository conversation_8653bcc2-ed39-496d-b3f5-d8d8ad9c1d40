INDEX : Logs du terminal console du serveur ci bas a partir de la ligne : 4
INDEX : Logs du navigateur a partir de la ligne : 1011

📦 Réception chunk: (2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 2): 7 types de blocs, 4432 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 3): 7 types de blocs, 4362 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 4): 7 types de blocs, 4330 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (2, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 5): 7 types de blocs, 4349 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (2, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 6): 7 types de blocs, 4312 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (2, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 7): 8 types de blocs, 4360 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 7), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (2, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 8): 7 types de blocs, 4476 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 8), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (3, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -9): 8 types de blocs, 4262 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -9) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -9), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (3, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -8): 9 types de blocs, 4264 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -8) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -8), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (3, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -7): 7 types de blocs, 4172 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -7), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -6): 6 types de blocs, 4184 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -6) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -6), distance: 6.71 ClientWorld.js:112:17
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0990", newVelocityY: "-46.53" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📦 Réception chunk: (3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -5): 6 types de blocs, 4188 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -5) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -4): 8 types de blocs, 4216 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -3): 8 types de blocs, 4197 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -2): 6 types de blocs, 4150 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -1): 9 types de blocs, 4128 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 0): 6 types de blocs, 4037 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 0) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 0), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 1): 8 types de blocs, 4069 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 2): 9 types de blocs, 4083 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 3): 9 types de blocs, 4270 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 4): 8 types de blocs, 4190 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 5): 7 types de blocs, 4235 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 5), distance: 5.83 ClientWorld.js:112:17
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0820", newVelocityY: "-48.01" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📦 Réception chunk: (3, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 6): 7 types de blocs, 4298 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (3, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 7): 7 types de blocs, 4358 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 7), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (3, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 8): 7 types de blocs, 4551 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 8), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (4, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -8): 6 types de blocs, 4159 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -8) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -8), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (4, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -7): 6 types de blocs, 4139 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -7) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -7), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -6): 8 types de blocs, 4210 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -5): 8 types de blocs, 4194 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -4): 6 types de blocs, 4138 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -4) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -3): 8 types de blocs, 4194 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -2): 8 types de blocs, 4125 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -1): 9 types de blocs, 4041 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -1), distance: 4.12 ClientWorld.js:112:17
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0860", newVelocityY: "-49.55" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📦 Réception chunk: (4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 0): 6 types de blocs, 3961 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 0) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 0), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 1): 8 types de blocs, 4020 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 2): 7 types de blocs, 3873 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 3): 8 types de blocs, 3897 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (4, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 4): 9 types de blocs, 3965 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (4, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 5): 9 types de blocs, 4118 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (4, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 6): 9 types de blocs, 4208 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 6) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (4, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 7): 7 types de blocs, 4417 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 7), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (4, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 8): 7 types de blocs, 4502 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 8), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (5, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -8): 9 types de blocs, 4085 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -8) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -8), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (5, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -7): 9 types de blocs, 4199 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -7) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (5, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -6): 8 types de blocs, 4170 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -6), distance: 7.81 ClientWorld.js:112:17
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0830", newVelocityY: "-51.05" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📦 Réception chunk: (5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -5): 8 types de blocs, 4132 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -5), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -4): 8 types de blocs, 4134 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -3): 8 types de blocs, 4145 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -2): 6 types de blocs, 3995 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -1): 7 types de blocs, 3945 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 0): 8 types de blocs, 4005 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 0), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 1): 8 types de blocs, 3913 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 2): 9 types de blocs, 3879 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 3): 8 types de blocs, 3926 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (5, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 4): 8 types de blocs, 3900 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (5, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 5): 6 types de blocs, 3878 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 5) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 5), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (5, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 6): 6 types de blocs, 3957 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 6) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 6), distance: 7.81 ClientWorld.js:112:17
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0830", newVelocityY: "-52.54" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📦 Réception chunk: (5, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 7): 11 types de blocs, 4174 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 7) a 11 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (6, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -7): 7 types de blocs, 3922 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -7), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (6, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -6): 9 types de blocs, 4124 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -6) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -6), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (6, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -5): 10 types de blocs, 4191 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -5) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -5), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (6, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -4): 10 types de blocs, 4129 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -4) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -4), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (6, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -3): 7 types de blocs, 4046 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -3), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -2): 8 types de blocs, 3994 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -1): 6 types de blocs, 3909 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 0): 9 types de blocs, 3877 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 0), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (6, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 1): 8 types de blocs, 3905 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 1), distance: 6.08 ClientWorld.js:112:17
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0840", newVelocityY: "-54.05" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📦 Réception chunk: (6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 2): 6 types de blocs, 3755 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (6, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 3): 8 types de blocs, 3861 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 3), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (6, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 4): 8 types de blocs, 3849 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 4), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (6, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 5): 8 types de blocs, 3894 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 5), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (6, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 6): 6 types de blocs, 3969 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 6) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 6), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (6, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 7): 7 types de blocs, 4114 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 7), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (7, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -6): 6 types de blocs, 4001 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -6) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -6), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (7, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -5): 8 types de blocs, 3961 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -5), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (7, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -4): 9 types de blocs, 4103 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -4), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (7, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -3): 8 types de blocs, 4100 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -3), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (7, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -2): 6 types de blocs, 3975 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -2), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (7, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -1): 8 types de blocs, 3962 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, -1), distance: 7.07 ClientWorld.js:112:17
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0840", newVelocityY: "-55.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📦 Réception chunk: (7, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 0): 7 types de blocs, 3828 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 0), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (7, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 1): 8 types de blocs, 3805 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 1), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (7, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 2): 9 types de blocs, 3792 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 2), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (7, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 3): 8 types de blocs, 3771 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 3), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (7, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 4): 7 types de blocs, 3788 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 4), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (7, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 5): 8 types de blocs, 3903 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 5), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (7, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 6): 8 types de blocs, 4043 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 6), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (8, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -4): 8 types de blocs, 3857 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -4), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (8, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -3): 9 types de blocs, 4011 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -3), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (8, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -2): 9 types de blocs, 4059 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -2), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (8, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -1): 6 types de blocs, 3925 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, -1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, -1), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (8, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 0): 9 types de blocs, 4060 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 0), distance: 8.00 ClientWorld.js:112:17
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0790", newVelocityY: "-56.99" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📦 Réception chunk: (8, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 1): 8 types de blocs, 3870 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 1), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (8, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 2): 9 types de blocs, 3790 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 2), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (8, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 3): 7 types de blocs, 3798 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 3), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (8, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 4): 8 types de blocs, 3857 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 4), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (9, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 0): 9 types de blocs, 3892 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (9, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (9, 0), distance: 9.00 ClientWorld.js:112:17
👤 ID joueur reçu: mg9lade05mdhkmpbh main.js:312:17
👤 ID joueur défini: mg9lade05mdhkmpbh PlayerController.js:501:17
🌱 Seed du monde défini: 607664 ClientWorld.js:38:17
🎨 Représentation visuelle créée pour 7zknxlnu2mdhkm1rm RemotePlayer.js:72:17
👤 RemotePlayer créé: 7zknxlnu2mdhkm1rm RemotePlayer.js:37:17
👤 Joueur ajouté: 7zknxlnu2mdhkm1rm ClientWorld.js:150:17
🎯 Première position reçue du serveur: 
Object { x: -14, y: 77.7, z: 9 }
PlayerController.js:355:25
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-2.10", y: "18.21", z: "1.35" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-3.88", y: "27.14", z: "2.50" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-5.40", y: "34.72", z: "3.47" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-6.69", y: "41.17", z: "4.30" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-7.79", y: "46.65", z: "5.01" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-8.72", y: "51.31", z: "5.61" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-9.51", y: "55.27", z: "6.11" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-10.19", y: "58.63", z: "6.55" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-10.76", y: "61.49", z: "6.92" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-11.24", y: "63.92", z: "7.23" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-11.66", y: "65.99", z: "7.49" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-12.01", y: "67.75", z: "7.72" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-12.31", y: "69.24", z: "7.91" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-12.56", y: "70.51", z: "8.08" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-12.78", y: "71.59", z: "8.21" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-12.96", y: "72.50", z: "8.33" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.12", y: "73.28", z: "8.43" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.25", y: "73.95", z: "8.52" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.36", y: "74.51", z: "8.59" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.46", y: "74.99", z: "8.65" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.54", y: "75.39", z: "8.70" }
PlayerController.js:449:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.61", y: "75.74", z: "8.75" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0400", newVelocityY: "-57.71" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.67", y: "74.07", z: "8.79" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0230", newVelocityY: "-58.12" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0130", newVelocityY: "-58.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-58.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0280", newVelocityY: "-59.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0130", newVelocityY: "-59.40" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🗑️ Nettoyage RemotePlayer: 7zknxlnu2mdhkm1rm RemotePlayer.js:432:17
👋 Joueur supprimé: 7zknxlnu2mdhkm1rm ClientWorld.js:160:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0250", newVelocityY: "-59.85" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.72", y: "68.65", z: "8.82" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0130", newVelocityY: "-60.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-60.35" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-60.64" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0120", newVelocityY: "-60.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0190", newVelocityY: "-61.20" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-61.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.76", y: "65.36", z: "8.85" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-61.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-62.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-62.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-62.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-62.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-63.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-63.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-63.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.80", y: "60.11", z: "8.87" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-64.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-64.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-64.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-65.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-65.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-65.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-65.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.83", y: "56.28", z: "8.89" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-66.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-66.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-66.89" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-67.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-67.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-67.79" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-68.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-68.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-68.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-68.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-69.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-69.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.85", y: "47.94", z: "8.90" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-69.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-70.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-70.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-70.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-71.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-71.39" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔒 Pointer lock activé PlayerController.js:147:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-71.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.87", y: "45.43", z: "8.92" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-71.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-72.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-72.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-72.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-73.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-73.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-73.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-74.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.89", y: "41.95", z: "8.93" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-74.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-74.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-74.99" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-75.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-75.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-75.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-76.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.91", y: "39.83", z: "8.94" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-76.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-76.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-77.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-77.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-77.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-77.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-78.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.93", y: "45.60", z: "8.44" }
PlayerController.js:449:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-78.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-78.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-79.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-79.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-79.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0180", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-80.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-80.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-13.99", y: "50.19", z: "7.86" }
PlayerController.js:449:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-80.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-80.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-81.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-81.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "KeyW" ]
 moveX: 0 moveZ: -1 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-81.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-82.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.04", y: "55.11", z: "7.43" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-82.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-82.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-83.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-83.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-83.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-83.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-84.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.06", y: "57.88", z: "7.33" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-84.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-84.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-85.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-85.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-85.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-86.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-86.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-86.69" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-86.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-87.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-87.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.09", y: "55.16", z: "7.11" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-87.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-88.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-88.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-88.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-89.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-89.39" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-89.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-89.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.12", y: "56.21", z: "6.82" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-90.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-90.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-90.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-91.19" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-91.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-91.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-92.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.16", y: "58.06", z: "6.49" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-92.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-92.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-92.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-93.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-93.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-93.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-94.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-94.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.20", y: "58.09", z: "6.11" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-94.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-95.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-95.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-95.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-95.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-96.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-96.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.24", y: "59.29", z: "5.69" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-96.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-97.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-97.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-97.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-98.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-98.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-98.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-98.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.29", y: "58.71", z: "5.25" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-99.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-99.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-99.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-100.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-100.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-100.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-101.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.34", y: "59.29", z: "4.78" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-101.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-101.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-101.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-102.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-102.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-102.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-103.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.39", y: "59.69", z: "4.29" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-103.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-103.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-104.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-104.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0170", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-104.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0360", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0360", newVelocityY: "-105.32" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📐 Renderer redimensionné: 506x624 Renderer.js:153:17
🔍 [PHYSICS] updatePrediction: 
Object { deltaTime: "0.0160", speed: 5, sprintMultiplier: 1, isFlying: false, position: {…}, velocity: {…} }
PlayerController.js:211:21
🎮 [MOVEMENT] Touches actives: 
Array [ "F12" ]
 moveX: 0 moveZ: 0 PlayerController.js:240:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-105.61" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-105.91" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-106.20" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0560", newVelocityY: "-107.21" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0100", newVelocityY: "-107.39" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.43", y: "48.98", z: "3.82" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-107.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🔓 Pointer lock désactivé PlayerController.js:154:21
🔍 Pointer lock désactivé après avoir été actif - vérifier la cause PlayerController.js:157:25
console.trace() Stack trace de la désactivation du pointer lock PlayerController.js:158:25
    onPointerLockChange http://localhost:3000/js/player/PlayerController.js:158
    setupEventListeners http://localhost:3000/js/player/PlayerController.js:58
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-107.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-108.25" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-108.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-108.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.47", y: "53.39", z: "3.42" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-109.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:485:43
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-109.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-109.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-110.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-110.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-110.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-110.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-111.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-111.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-111.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-112.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-112.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-112.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-113.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-113.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.51", y: "41.04", z: "3.09" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0230", newVelocityY: "-113.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.54", y: "51.97", z: "2.80" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0100", newVelocityY: "-113.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-114.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-114.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-114.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-115.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-115.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-115.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.56", y: "52.73", z: "2.56" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-116.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-116.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-116.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-116.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-117.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-117.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-117.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.58", y: "52.50", z: "2.35" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-118.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-118.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-118.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-119.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-119.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-119.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-119.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-120.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.60", y: "50.35", z: "2.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-120.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-120.89" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-121.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-121.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-121.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-122.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-122.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.61", y: "50.12", z: "2.03" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-122.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-122.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-123.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-123.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-123.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-124.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-124.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-124.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.63", y: "47.92", z: "1.90" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-125.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-125.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-125.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-125.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-126.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-126.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-126.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-127.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.64", y: "45.68", z: "1.79" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-127.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-127.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-128.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-128.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-128.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-128.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-129.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.65", y: "45.36", z: "1.70" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-129.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-129.85" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-130.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-130.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-130.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-131.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-131.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.66", y: "45.00", z: "1.62" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-131.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-131.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-132.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-132.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-132.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-133.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-133.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-133.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.66", y: "42.55", z: "1.56" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-134.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-134.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-134.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-134.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-135.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-135.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-135.90" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-136.21" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-136.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.67", y: "38.11", z: "1.50" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-136.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-137.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-137.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-137.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-137.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-138.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.67", y: "40.01", z: "1.45" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-138.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-138.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-139.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-139.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-139.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-140.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-140.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.68", y: "39.56", z: "1.41" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-140.65" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-140.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-141.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-141.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-141.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-142.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-142.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-142.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.68", y: "36.79", z: "1.38" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-143.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-143.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-143.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-143.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-144.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-144.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-144.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-145.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-145.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.68", y: "32.31", z: "1.35" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-145.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-146.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-146.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-146.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-146.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-147.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.69", y: "34.19", z: "1.32" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-147.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-147.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-148.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-148.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-148.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-149.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-149.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.69", y: "33.60", z: "1.30" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-149.69" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-149.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-150.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-150.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-150.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-151.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-151.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-151.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.69", y: "30.82", z: "1.29" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-152.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-152.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-152.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-152.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-153.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-153.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-153.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-154.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.69", y: "28.19", z: "1.27" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-154.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-154.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-155.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-155.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-155.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-155.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-156.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.69", y: "27.82", z: "1.26" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-156.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-156.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-157.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-157.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-157.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-158.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-158.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.69", y: "27.29", z: "1.25" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-158.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-158.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-159.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-159.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-159.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-160.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-160.45" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-160.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-161.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-161.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-161.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.69", y: "17.72", z: "1.24" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-161.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-162.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-162.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-162.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-163.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-163.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-163.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-164.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "15.80", z: "1.23" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-164.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-164.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-164.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-165.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-165.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-165.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-166.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "16.30", z: "1.22" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-166.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-166.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-167.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-167.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-167.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-167.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-168.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-168.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-168.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0210", newVelocityY: "-169.24" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "8.90", z: "1.22" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-169.54" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-169.83" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-170.12" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0200", newVelocityY: "-170.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0120", newVelocityY: "-170.69" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "15.16", z: "1.21" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-171.02" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0140", newVelocityY: "-171.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-171.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-171.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-172.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-172.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-172.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "15.39", z: "1.21" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-173.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-173.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-173.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-173.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-174.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-174.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-174.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-175.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "12.55", z: "1.20" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-175.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-175.79" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-176.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-176.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-176.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-176.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-177.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "12.59", z: "1.20" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-177.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-177.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-178.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-178.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-178.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-179.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-179.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-179.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-179.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-180.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-180.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "2.15", z: "1.20" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-180.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-181.15" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-181.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-181.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-182.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-182.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-182.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-182.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "0.41", z: "1.20" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-183.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-183.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-183.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-184.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-184.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-184.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-185.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-185.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-1.18", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-185.69" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-185.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-186.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-186.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-186.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-187.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-187.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-0.25", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-187.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-188.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-188.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-188.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-188.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-189.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-189.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "0.49", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-189.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-190.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-190.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-190.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-191.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-191.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-191.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-191.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-2.03", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-192.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-192.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-192.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-193.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-193.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-193.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-194.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-1.63", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-194.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-194.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-194.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-195.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-195.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-195.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-196.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-1.33", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-196.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-196.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-197.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-197.39" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-197.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-197.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-198.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-198.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-198.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-199.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-199.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-199.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-15.51", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-200.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-200.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-200.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-200.95" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-201.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-201.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-201.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-13.86", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-202.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-202.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-202.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-203.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-203.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-203.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-203.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-204.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-15.45", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-204.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-204.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-205.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-205.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-205.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-206.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-206.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-206.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-206.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-20.06", z: "1.19" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-207.29" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-207.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-207.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-208.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-208.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-208.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-209.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-18.45", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-209.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-209.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-209.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-210.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-210.56" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-210.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-14.24", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-211.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-211.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-211.79" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-212.06" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-212.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-212.65" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-212.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-213.26" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-16.79", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-213.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-213.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-214.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-214.45" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-214.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-215.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-215.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-16.29", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-215.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-215.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-216.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-216.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-216.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-217.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-217.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-217.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-19.23", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-218.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-218.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-218.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-218.99" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-219.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-219.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-219.87" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-18.63", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-220.18" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-220.46" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-220.77" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-221.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-221.36" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-221.67" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-221.98" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-222.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-21.73", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-222.57" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-222.88" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-223.16" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-223.47" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-223.76" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-224.08" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-224.37" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-224.66" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-24.26", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-224.96" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-225.27" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0180", newVelocityY: "-225.59" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0150", newVelocityY: "-225.86" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-226.17" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-226.48" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-226.78" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
📡 [SERVER] Mise à jour reçue: 
Object { position: {…}, velocity: {…}, predicted: {…}, error: {…} }
PlayerController.js:360:21
🔧 [CORRECTION] Erreur de prédiction détectée: 
Object { error: {…}, correctionStrength: 0.15, beforeCorrection: {…} }
PlayerController.js:427:21
🔧 [CORRECTION] Après correction: 
Object { x: "-14.70", y: "-23.97", z: "1.18" }
PlayerController.js:449:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-227.07" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-227.38" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-227.68" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-227.97" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-228.28" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0170", newVelocityY: "-228.58" }
PlayerController.js:263:21
📍 [POSITION] Mouvement appliqué: 
Object { from: {…}, to: {…}, delta: {…} }
PlayerController.js:300:21
🌍 [GRAVITY] Gravité appliquée: 
Object { gravity: 18, deltaTime: "0.0160", newVelocityY: "-228.87" }
PlayerController.js:263:21

PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> npm start

> jscraft-server@1.0.0 start
> node server/server.js

🌱 WorldGenerator initialisé avec seed: 607664
🌍 WorldManager initialisé avec seed: 607664
🎮 GameManager initialisé
🌐 Serveur WebSocket initialisé
🚀 Serveur JScraft démarré sur http://localhost:3000
📁 Fichiers client servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\client
🔗 Fichiers partagés servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\shared
🎮 Boucle de jeu serveur démarrée (20 ticks/s)
👤 Nouveau joueur connecté: 7zknxlnu2mdhkm1rm
👤 Player 7zknxlnu2mdhkm1rm a rejoint le jeu
👤 Joueur 7zknxlnu2mdhkm1rm créé à la position (0, 100, 0)
🔍 [SPAWN] Recherche position de spawn...
🧱 [SPAWN] Génération du chunk de spawn (0,0)...
🧱 Chunk généré: (0, 0)
🧱 Chunk généré: (-1, -1)
🔍 [SPAWN] Tentative 1: (-4, -15) groundHeight: 76, spawnY: 77.7
✅ [SPAWN] Position sûre trouvée: (-4, 77.70, -15)
📍 [PLAYER 7zknxlnu2mdhkm1rm] Position définie: {
  from: { x: '0.00', y: '100.00', z: '0.00' },
  to: { x: '-4.00', y: '77.70', z: '-15.00' }
}
🧱 Chunk généré: (-11, -1)
🧱 Chunk généré: (-10, -5)
🧱 Chunk généré: (-10, -4)
🧱 Chunk généré: (-10, -3)
🧱 Chunk généré: (-10, -2)
🧱 Chunk généré: (-10, -1)
🧱 Chunk généré: (-10, 0)
🧱 Chunk généré: (-10, 1)
🧱 Chunk généré: (-10, 2)
🧱 Chunk généré: (-10, 3)
🧱 Chunk généré: (-9, -7)
🧱 Chunk généré: (-9, -6)
🧱 Chunk généré: (-9, -5)
🧱 Chunk généré: (-9, -4)
🧱 Chunk généré: (-9, -3)
🧱 Chunk généré: (-9, -2)
🧱 Chunk généré: (-9, -1)
🧱 Chunk généré: (-9, 0)
🧱 Chunk généré: (-9, 1)
🧱 Chunk généré: (-9, 2)
🧱 Chunk généré: (-9, 3)
🧱 Chunk généré: (-9, 4)
🧱 Chunk généré: (-9, 5)
🧱 Chunk généré: (-8, -8)
🧱 Chunk généré: (-8, -7)
🧱 Chunk généré: (-8, -6)
🧱 Chunk généré: (-8, -5)
🧱 Chunk généré: (-8, -4)
🧱 Chunk généré: (-8, -3)
🧱 Chunk généré: (-8, -2)
🧱 Chunk généré: (-8, -1)
🧱 Chunk généré: (-8, 0)
🧱 Chunk généré: (-8, 1)
🧱 Chunk généré: (-8, 2)
🧱 Chunk généré: (-8, 3)
🧱 Chunk généré: (-8, 4)
🧱 Chunk généré: (-8, 5)
🧱 Chunk généré: (-8, 6)
🧱 Chunk généré: (-7, -9)
🧱 Chunk généré: (-7, -8)
🧱 Chunk généré: (-7, -7)
🧱 Chunk généré: (-7, -6)
🧱 Chunk généré: (-7, -5)
🧱 Chunk généré: (-7, -4)
🧱 Chunk généré: (-7, -3)
🧱 Chunk généré: (-7, -2)
🧱 Chunk généré: (-7, -1)
🧱 Chunk généré: (-7, 0)
🧱 Chunk généré: (-7, 1)
🧱 Chunk généré: (-7, 2)
🧱 Chunk généré: (-7, 3)
🧱 Chunk généré: (-7, 4)
🧱 Chunk généré: (-7, 5)
🧱 Chunk généré: (-7, 6)
🧱 Chunk généré: (-7, 7)
🧱 Chunk généré: (-6, -9)
🧱 Chunk généré: (-6, -8)
🧱 Chunk généré: (-6, -7)
🧱 Chunk généré: (-6, -6)
🧱 Chunk généré: (-6, -5)
🧱 Chunk généré: (-6, -4)
🧱 Chunk généré: (-6, -3)
🧱 Chunk généré: (-6, -2)
🧱 Chunk généré: (-6, -1)
🧱 Chunk généré: (-6, 0)
🧱 Chunk généré: (-6, 1)
🧱 Chunk généré: (-6, 2)
🧱 Chunk généré: (-6, 3)
🧱 Chunk généré: (-6, 4)
🧱 Chunk généré: (-6, 5)
🧱 Chunk généré: (-6, 6)
🧱 Chunk généré: (-6, 7)
🧱 Chunk généré: (-5, -10)
🧱 Chunk généré: (-5, -9)
🧱 Chunk généré: (-5, -8)
🧱 Chunk généré: (-5, -7)
🧱 Chunk généré: (-5, -6)
🧱 Chunk généré: (-5, -5)
🧱 Chunk généré: (-5, -4)
🧱 Chunk généré: (-5, -3)
🧱 Chunk généré: (-5, -2)
🧱 Chunk généré: (-5, -1)
🧱 Chunk généré: (-5, 0)
🧱 Chunk généré: (-5, 1)
🧱 Chunk généré: (-5, 2)
🧱 Chunk généré: (-5, 3)
🧱 Chunk généré: (-5, 4)
🧱 Chunk généré: (-5, 5)
🧱 Chunk généré: (-5, 6)
🧱 Chunk généré: (-5, 7)
🧱 Chunk généré: (-5, 8)
🧱 Chunk généré: (-4, -10)
🧱 Chunk généré: (-4, -9)
🧱 Chunk généré: (-4, -8)
🧱 Chunk généré: (-4, -7)
🧱 Chunk généré: (-4, -6)
🧱 Chunk généré: (-4, -5)
🧱 Chunk généré: (-4, -4)
🧱 Chunk généré: (-4, -3)
🧱 Chunk généré: (-4, -2)
🧱 Chunk généré: (-4, -1)
🧱 Chunk généré: (-4, 0)
🧱 Chunk généré: (-4, 1)
🧱 Chunk généré: (-4, 2)
🧱 Chunk généré: (-4, 3)
🧱 Chunk généré: (-4, 4)
🧱 Chunk généré: (-4, 5)
🧱 Chunk généré: (-4, 6)
🧱 Chunk généré: (-4, 7)
🧱 Chunk généré: (-4, 8)
🧱 Chunk généré: (-3, -10)
🧱 Chunk généré: (-3, -9)
🧱 Chunk généré: (-3, -8)
🧱 Chunk généré: (-3, -7)
🧱 Chunk généré: (-3, -6)
🧱 Chunk généré: (-3, -5)
🧱 Chunk généré: (-3, -4)
🧱 Chunk généré: (-3, -3)
🧱 Chunk généré: (-3, -2)
🧱 Chunk généré: (-3, -1)
🧱 Chunk généré: (-3, 0)
🧱 Chunk généré: (-3, 1)
🧱 Chunk généré: (-3, 2)
🧱 Chunk généré: (-3, 3)
🧱 Chunk généré: (-3, 4)
🧱 Chunk généré: (-3, 5)
🧱 Chunk généré: (-3, 6)
🧱 Chunk généré: (-3, 7)
🧱 Chunk généré: (-3, 8)
🧱 Chunk généré: (-2, -10)
🧱 Chunk généré: (-2, -9)
🧱 Chunk généré: (-2, -8)
🧱 Chunk généré: (-2, -7)
🧱 Chunk généré: (-2, -6)
🧱 Chunk généré: (-2, -5)
🧱 Chunk généré: (-2, -4)
🧱 Chunk généré: (-2, -3)
🧱 Chunk généré: (-2, -2)
🧱 Chunk généré: (-2, -1)
🧱 Chunk généré: (-2, 0)
🧱 Chunk généré: (-2, 1)
🧱 Chunk généré: (-2, 2)
🧱 Chunk généré: (-2, 3)
🧱 Chunk généré: (-2, 4)
🧱 Chunk généré: (-2, 5)
🧱 Chunk généré: (-2, 6)
🧱 Chunk généré: (-2, 7)
🧱 Chunk généré: (-2, 8)
🧱 Chunk généré: (-1, -11)
🧱 Chunk généré: (-1, -10)
🧱 Chunk généré: (-1, -9)
🧱 Chunk généré: (-1, -8)
🧱 Chunk généré: (-1, -7)
🧱 Chunk généré: (-1, -6)
🧱 Chunk généré: (-1, -5)
🧱 Chunk généré: (-1, -4)
🧱 Chunk généré: (-1, -3)
🧱 Chunk généré: (-1, -2)
🧱 Chunk généré: (-1, 0)
🧱 Chunk généré: (-1, 1)
🧱 Chunk généré: (-1, 2)
🧱 Chunk généré: (-1, 3)
🧱 Chunk généré: (-1, 4)
🧱 Chunk généré: (-1, 5)
🧱 Chunk généré: (-1, 6)
🧱 Chunk généré: (-1, 7)
🧱 Chunk généré: (-1, 8)
🧱 Chunk généré: (-1, 9)
🧱 Chunk généré: (0, -10)
🧱 Chunk généré: (0, -9)
🧱 Chunk généré: (0, -8)
🧱 Chunk généré: (0, -7)
🧱 Chunk généré: (0, -6)
🧱 Chunk généré: (0, -5)
🧱 Chunk généré: (0, -4)
🧱 Chunk généré: (0, -3)
🧱 Chunk généré: (0, -2)
🧱 Chunk généré: (0, -1)
🧱 Chunk généré: (0, 0)
🧱 Chunk généré: (0, 1)
🧱 Chunk généré: (0, 2)
🧱 Chunk généré: (0, 3)
🧱 Chunk généré: (0, 4)
🧱 Chunk généré: (0, 5)
🧱 Chunk généré: (0, 6)
🧱 Chunk généré: (0, 7)
🧱 Chunk généré: (0, 8)
🧱 Chunk généré: (1, -10)
🧱 Chunk généré: (1, -9)
🧱 Chunk généré: (1, -8)
🧱 Chunk généré: (1, -7)
🧱 Chunk généré: (1, -6)
🧱 Chunk généré: (1, -5)
🧱 Chunk généré: (1, -4)
🧱 Chunk généré: (1, -3)
🧱 Chunk généré: (1, -2)
🧱 Chunk généré: (1, -1)
🧱 Chunk généré: (1, 0)
🧱 Chunk généré: (1, 1)
🧱 Chunk généré: (1, 2)
🧱 Chunk généré: (1, 3)
🧱 Chunk généré: (1, 4)
🧱 Chunk généré: (1, 5)
🧱 Chunk généré: (1, 6)
🧱 Chunk généré: (1, 7)
🧱 Chunk généré: (1, 8)
🧱 Chunk généré: (2, -10)
🧱 Chunk généré: (2, -9)
🧱 Chunk généré: (2, -8)
🧱 Chunk généré: (2, -7)
🧱 Chunk généré: (2, -6)
🧱 Chunk généré: (2, -5)
🧱 Chunk généré: (2, -4)
🧱 Chunk généré: (2, -3)
🧱 Chunk généré: (2, -2)
🧱 Chunk généré: (2, -1)
🧱 Chunk généré: (2, 0)
🧱 Chunk généré: (2, 1)
🧱 Chunk généré: (2, 2)
🧱 Chunk généré: (2, 3)
🧱 Chunk généré: (2, 4)
🧱 Chunk généré: (2, 5)
🧱 Chunk généré: (2, 6)
🧱 Chunk généré: (2, 7)
🧱 Chunk généré: (2, 8)
🧱 Chunk généré: (3, -10)
🧱 Chunk généré: (3, -9)
🧱 Chunk généré: (3, -8)
🧱 Chunk généré: (3, -7)
🧱 Chunk généré: (3, -6)
🧱 Chunk généré: (3, -5)
🧱 Chunk généré: (3, -4)
🧱 Chunk généré: (3, -3)
🧱 Chunk généré: (3, -2)
🧱 Chunk généré: (3, -1)
🧱 Chunk généré: (3, 0)
🧱 Chunk généré: (3, 1)
🧱 Chunk généré: (3, 2)
🧱 Chunk généré: (3, 3)
🧱 Chunk généré: (3, 4)
🧱 Chunk généré: (3, 5)
🧱 Chunk généré: (3, 6)
🧱 Chunk généré: (3, 7)
🧱 Chunk généré: (3, 8)
🧱 Chunk généré: (4, -9)
🧱 Chunk généré: (4, -8)
🧱 Chunk généré: (4, -7)
🧱 Chunk généré: (4, -6)
🧱 Chunk généré: (4, -5)
🧱 Chunk généré: (4, -4)
🧱 Chunk généré: (4, -3)
🧱 Chunk généré: (4, -2)
🧱 Chunk généré: (4, -1)
🧱 Chunk généré: (4, 0)
🧱 Chunk généré: (4, 1)
🧱 Chunk généré: (4, 2)
🧱 Chunk généré: (4, 3)
🧱 Chunk généré: (4, 4)
🧱 Chunk généré: (4, 5)
🧱 Chunk généré: (4, 6)
🧱 Chunk généré: (4, 7)
🧱 Chunk généré: (5, -9)
🧱 Chunk généré: (5, -8)
🧱 Chunk généré: (5, -7)
🧱 Chunk généré: (5, -6)
🧱 Chunk généré: (5, -5)
🧱 Chunk généré: (5, -4)
🧱 Chunk généré: (5, -3)
🧱 Chunk généré: (5, -2)
🧱 Chunk généré: (5, -1)
🧱 Chunk généré: (5, 0)
🧱 Chunk généré: (5, 1)
🧱 Chunk généré: (5, 2)
🧱 Chunk généré: (5, 3)
🧱 Chunk généré: (5, 4)
🧱 Chunk généré: (5, 5)
🧱 Chunk généré: (5, 6)
🧱 Chunk généré: (5, 7)
🧱 Chunk généré: (6, -8)
🧱 Chunk généré: (6, -7)
🧱 Chunk généré: (6, -6)
🧱 Chunk généré: (6, -5)
🧱 Chunk généré: (6, -4)
🧱 Chunk généré: (6, -3)
🧱 Chunk généré: (6, -2)
🧱 Chunk généré: (6, -1)
🧱 Chunk généré: (6, 0)
🧱 Chunk généré: (6, 1)
🧱 Chunk généré: (6, 2)
🧱 Chunk généré: (6, 3)
🧱 Chunk généré: (6, 4)
🧱 Chunk généré: (6, 5)
🧱 Chunk généré: (6, 6)
🧱 Chunk généré: (7, -7)
🧱 Chunk généré: (7, -6)
🧱 Chunk généré: (7, -5)
🧱 Chunk généré: (7, -4)
🧱 Chunk généré: (7, -3)
🧱 Chunk généré: (7, -2)
🧱 Chunk généré: (7, -1)
🧱 Chunk généré: (7, 0)
🧱 Chunk généré: (7, 1)
🧱 Chunk généré: (7, 2)
🧱 Chunk généré: (7, 3)
🧱 Chunk généré: (7, 4)
🧱 Chunk généré: (7, 5)
🧱 Chunk généré: (8, -5)
🧱 Chunk généré: (8, -4)
🧱 Chunk généré: (8, -3)
🧱 Chunk généré: (8, -2)
🧱 Chunk généré: (8, -1)
🧱 Chunk généré: (8, 0)
🧱 Chunk généré: (8, 1)
🧱 Chunk généré: (8, 2)
🧱 Chunk généré: (8, 3)
🧱 Chunk généré: (9, -1)
📊 Stats: 1 joueurs, 16 TPS, 7396.00ms/tick
🧱 Chunk généré: (-11, -2)
🧱 Chunk généré: (-10, -6)
🧱 Chunk généré: (-9, -8)
🧱 Chunk généré: (-8, -9)
🧱 Chunk généré: (-7, -10)
🧱 Chunk généré: (-6, -10)
🧱 Chunk généré: (-5, -11)
🧱 Chunk généré: (-4, -11)
🧱 Chunk généré: (-3, -11)
🧱 Chunk généré: (-2, -11)
🧱 Chunk généré: (-1, -12)
🧱 Chunk généré: (0, -11)
🧱 Chunk généré: (1, -11)
🧱 Chunk généré: (2, -11)
🧱 Chunk généré: (3, -11)
🧱 Chunk généré: (4, -10)
🧱 Chunk généré: (5, -10)
🧱 Chunk généré: (6, -9)
🧱 Chunk généré: (7, -8)
🧱 Chunk généré: (8, -6)
🧱 Chunk généré: (9, -2)
📊 Stats: 1 joueurs, 18 TPS, 64.00ms/tick
🧱 Chunk généré: (0, 9)
🧱 Chunk généré: (4, 8)
🧱 Chunk généré: (6, 7)
🧱 Chunk généré: (7, 6)
🧱 Chunk généré: (8, -7)
🧱 Chunk généré: (8, 4)
🧱 Chunk généré: (8, 5)
🧱 Chunk généré: (9, -5)
🧱 Chunk généré: (9, -4)
🧱 Chunk généré: (9, -3)
🧱 Chunk généré: (9, 0)
🧱 Chunk généré: (9, 1)
🧱 Chunk généré: (9, 2)
🧱 Chunk généré: (9, 3)
🧱 Chunk généré: (10, -1)
📊 Stats: 1 joueurs, 18 TPS, 51.00ms/tick
👤 Nouveau joueur connecté: mg9lade05mdhkmpbh
👤 Player mg9lade05mdhkmpbh a rejoint le jeu
👤 Joueur mg9lade05mdhkmpbh créé à la position (0, 100, 0)
🔍 [SPAWN] Recherche position de spawn...
🔍 [SPAWN] Tentative 1: (-14, 9) groundHeight: 76, spawnY: 77.7
✅ [SPAWN] Position sûre trouvée: (-14, 77.70, 9)
📍 [PLAYER mg9lade05mdhkmpbh] Position définie: {
  from: { x: '0.00', y: '100.00', z: '0.00' },
  to: { x: '-14.00', y: '77.70', z: '9.00' }
}
🧱 Chunk généré: (-11, 0)
🧱 Chunk généré: (-10, 4)
🧱 Chunk généré: (-9, 6)
🧱 Chunk généré: (-8, 7)
🧱 Chunk généré: (-7, 8)
🧱 Chunk généré: (-6, 8)
🧱 Chunk généré: (-5, 9)
🧱 Chunk généré: (-4, 9)
🧱 Chunk généré: (-3, 9)
🧱 Chunk généré: (-2, 9)
🧱 Chunk généré: (-1, 10)
🧱 Chunk généré: (1, 9)
🧱 Chunk généré: (2, 9)
🧱 Chunk généré: (3, 9)
🧱 Chunk généré: (5, 8)
👋 Joueur déconnecté: 7zknxlnu2mdhkm1rm
👋 Player 7zknxlnu2mdhkm1rm a quitté le jeu
📊 Stats: 1 joueurs, 16 TPS, 61.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 64.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 62.00ms/tick