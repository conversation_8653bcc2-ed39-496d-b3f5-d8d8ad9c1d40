INDEX : Logs du terminal console du serveur ci bas a partir de la ligne 
INDEX : Logs du navigateur a partir de la ligne 4 :

🎮 Game client initialisé main.js:37:17
🚀 Initialisation du client... main.js:42:21
🖥️ UIManager initialisé UIManager.js:13:17
⚙️ OptionsManager initialisé OptionsManager.js:47:17
⚙️ Options par défaut utilisées OptionsManager.js:374:25
⚙️ Options chargées et appliquées OptionsManager.js:57:17
🎨 Renderer initialisé Renderer.js:24:17
💡 Éclairage configuré Renderer.js:99:17
🌫️ Brouillard configuré Renderer.js:109:17
✅ Renderer initialisé avec succès Renderer.js:61:21
📊 WebGL: WebGL2 Renderer.js:62:21
📊 Max textures: 16 Renderer.js:63:21
🌍 ClientWorld initialisé ClientWorld.js:33:17
📷 PlayerCamera initialisée à la position: 
Object { x: 0, y: 101.7, z: 0 }
PlayerCamera.js:29:17
🎒 Inventory initialisé Inventory.js:24:17
🎮 PlayerController initialisé PlayerController.js:43:17
💬 ChatManager initialisé ChatManager.js:25:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:106:25
✅ Client initialisé avec succès main.js:72:21
🎮 Démarrage de la boucle de jeu main.js:184:17
🎨 Infos de rendu: main.js:237:25
- Scène enfants: 2 main.js:238:25
- Position caméra: 
Object { x: 0, y: 101.7, z: 0 }
main.js:239:25
- Rotation caméra: 
Object { _x: 0, _y: 0, _z: 0, _order: "XYZ", _onChangeCallback: Ce() }
main.js:240:25
📦 Réception chunk: (-5, -7) ClientWorld.js:90:17
🎨 Initialisation des ressources partagées... ClientChunk.js:30:17
✅ Géométrie partagée créée ClientChunk.js:40:17
🎨 Ressources partagées initialisées ClientChunk.js:76:17
📦 ClientChunk créé: (-5, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -7): 4 types de blocs, 5658 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -7) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (-4, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -7): 4 types de blocs, 5699 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -7) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -7), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -6): 7 types de blocs, 5454 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -5): 8 types de blocs, 5391 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -4): 7 types de blocs, 5166 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (-4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -3): 7 types de blocs, 4799 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-3, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -8): 4 types de blocs, 5883 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -8), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (-3, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -7): 7 types de blocs, 5664 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -7), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -6): 8 types de blocs, 5461 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -5): 8 types de blocs, 5199 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -4): 8 types de blocs, 5080 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -3): 8 types de blocs, 4854 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (-3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -2): 7 types de blocs, 4728 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -1): 7 types de blocs, 4793 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-2, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -7): 7 types de blocs, 5522 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -7), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (-2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -6): 8 types de blocs, 5301 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -5): 7 types de blocs, 5305 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -4): 7 types de blocs, 5205 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -3): 7 types de blocs, 5006 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -2): 8 types de blocs, 4845 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (-2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -1): 7 types de blocs, 4789 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 0): 7 types de blocs, 4676 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 0), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -7): 7 types de blocs, 5670 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -7), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-1, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -6): 7 types de blocs, 5587 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -6), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -5): 7 types de blocs, 5430 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -4): 7 types de blocs, 5292 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -3): 8 types de blocs, 5175 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -2): 8 types de blocs, 4936 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (-1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -1): 7 types de blocs, 4789 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (-1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 0): 7 types de blocs, 4763 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 0), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 1): 9 types de blocs, 4751 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (0, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -7): 7 types de blocs, 5678 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -7), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -6): 7 types de blocs, 5543 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -6), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -5): 8 types de blocs, 5517 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -5), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -4): 8 types de blocs, 5459 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -4), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -3): 8 types de blocs, 5237 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -3), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -2): 7 types de blocs, 5152 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -2), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (0, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -1): 7 types de blocs, 5075 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -1), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 0): 7 types de blocs, 4891 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 0), distance: 0.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 1): 7 types de blocs, 4846 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 1), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (1, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -7): 7 types de blocs, 5749 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -7), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (1, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -6): 7 types de blocs, 5704 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -6), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -5): 8 types de blocs, 5475 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -5), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -4): 8 types de blocs, 5511 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -4), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -3): 8 types de blocs, 5366 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -2): 7 types de blocs, 5266 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -1): 7 types de blocs, 5203 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 0): 10 types de blocs, 5129 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 0) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 0), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 1): 9 types de blocs, 4840 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 2): 6 types de blocs, 4393 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 2), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -6): 9 types de blocs, 5749 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -6) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -6), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -5): 8 types de blocs, 5730 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -5), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -4): 7 types de blocs, 5609 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -3): 8 types de blocs, 5537 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -3), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -2): 10 types de blocs, 5407 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -2) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -1): 9 types de blocs, 4984 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 0): 9 types de blocs, 4659 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 0), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 1): 6 types de blocs, 4573 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 2): 7 types de blocs, 4501 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 2), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -6): 7 types de blocs, 5782 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -6), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -5): 8 types de blocs, 5586 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -4): 9 types de blocs, 5576 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -4), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -3): 9 types de blocs, 5233 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -3), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -2): 7 types de blocs, 4820 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -1): 6 types de blocs, 4769 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 0): 8 types de blocs, 4757 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 0), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 1): 6 types de blocs, 4681 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 2): 9 types de blocs, 4655 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 2), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -5): 11 types de blocs, 5223 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -5) a 11 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -5), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -4): 9 types de blocs, 4948 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -4), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -3): 9 types de blocs, 4967 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -2): 8 types de blocs, 4916 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -1): 8 types de blocs, 4837 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 0): 10 types de blocs, 4915 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 0) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 0), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 1): 10 types de blocs, 4884 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 1) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 2): 8 types de blocs, 4759 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (5, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -7): 8 types de blocs, 5194 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -4): 8 types de blocs, 4951 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -4), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -3): 9 types de blocs, 4947 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -2): 8 types de blocs, 4962 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -1): 8 types de blocs, 5035 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 0): 8 types de blocs, 4919 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 0), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 1): 10 types de blocs, 4927 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 1) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 2): 8 types de blocs, 4840 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 2), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 3): 8 types de blocs, 4818 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -2): 8 types de blocs, 4960 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -1): 8 types de blocs, 4958 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 0): 8 types de blocs, 4978 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 0), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (6, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 1): 8 types de blocs, 4972 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 2): 10 types de blocs, 4985 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 2) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 2), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (7, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 2): 9 types de blocs, 5015 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 2), distance: 7.28 ClientWorld.js:112:17
👤 ID joueur reçu: j3d5uhzl3mdhk1bp6 main.js:312:17
👤 ID joueur défini: j3d5uhzl3mdhk1bp6 PlayerController.js:383:17
🌱 Seed du monde défini: 532230 ClientWorld.js:38:17
🔍 Appel de hideConnectionScreen et showInstructions... main.js:100:29
🔍 UIManager.hideConnectionScreen() appelée UIManager.js:99:17
🔍 Element connectionScreen: true UIManager.js:100:17
🔍 Element uiOverlay: true UIManager.js:101:17
🔍 Classes connectionScreen avant: <empty string> UIManager.js:104:21
🔍 Classes connectionScreen après: hidden UIManager.js:106:21
🔍 Classes uiOverlay avant: hidden UIManager.js:112:21
🔍 Classes uiOverlay après: <empty string> UIManager.js:114:21
✅ hideConnectionScreen terminée UIManager.js:119:17
🔍 UIManager.showInstructions() appelée UIManager.js:139:17
🔍 Element instructions: true UIManager.js:140:17
🔍 Classes instructions avant: hidden UIManager.js:143:21
🔍 Classes instructions après: <empty string> UIManager.js:145:21
✅ Instructions affichées UIManager.js:146:21
✅ Connecté au serveur et prêt à jouer main.js:103:29
📐 Renderer redimensionné: 506x624 Renderer.js:153:17
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
🎨 Représentation visuelle créée pour 1kcnwko9gmdhk12nq RemotePlayer.js:72:17
👤 RemotePlayer créé: 1kcnwko9gmdhk12nq RemotePlayer.js:37:17
👤 Joueur ajouté: 1kcnwko9gmdhk12nq ClientWorld.js:150:17
🎯 Première position reçue du serveur: 
Object { x: 94, y: 80.7, z: -99 }
PlayerController.js:288:25
🗑️ Nettoyage RemotePlayer: 1kcnwko9gmdhk12nq RemotePlayer.js:432:17
👋 Joueur supprimé: 1kcnwko9gmdhk12nq ClientWorld.js:160:21

PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> npm start

> jscraft-server@1.0.0 start
> node server/server.js

🌱 WorldGenerator initialisé avec seed: 532230
🌍 WorldManager initialisé avec seed: 532230
🎮 GameManager initialisé
🌐 Serveur WebSocket initialisé
🚀 Serveur JScraft démarré sur http://localhost:3000
📁 Fichiers client servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\client
🔗 Fichiers partagés servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\shared
🎮 Boucle de jeu serveur démarrée (20 ticks/s)
👤 Nouveau joueur connecté: 1kcnwko9gmdhk12nq
👤 Player 1kcnwko9gmdhk12nq a rejoint le jeu
👤 Joueur 1kcnwko9gmdhk12nq créé à la position (0, 100, 0)
🧱 Chunk généré: (-3, 2)
🧱 Chunk généré: (-13, 2)
🧱 Chunk généré: (-12, -2)
🧱 Chunk généré: (-12, -1)
🧱 Chunk généré: (-12, 0)
🧱 Chunk généré: (-12, 1)
🧱 Chunk généré: (-12, 2)
🧱 Chunk généré: (-12, 3)
🧱 Chunk généré: (-12, 4)
🧱 Chunk généré: (-12, 5)
🧱 Chunk généré: (-12, 6)
🧱 Chunk généré: (-11, -4)
🧱 Chunk généré: (-11, -3)
🧱 Chunk généré: (-11, -2)
🧱 Chunk généré: (-11, -1)
🧱 Chunk généré: (-11, 0)
🧱 Chunk généré: (-11, 1)
🧱 Chunk généré: (-11, 2)
🧱 Chunk généré: (-11, 3)
🧱 Chunk généré: (-11, 4)
🧱 Chunk généré: (-11, 5)
🧱 Chunk généré: (-11, 6)
🧱 Chunk généré: (-11, 7)
🧱 Chunk généré: (-11, 8)
🧱 Chunk généré: (-10, -5)
🧱 Chunk généré: (-10, -4)
🧱 Chunk généré: (-10, -3)
🧱 Chunk généré: (-10, -2)
🧱 Chunk généré: (-10, -1)
🧱 Chunk généré: (-10, 0)
🧱 Chunk généré: (-10, 1)
🧱 Chunk généré: (-10, 2)
🧱 Chunk généré: (-10, 3)
🧱 Chunk généré: (-10, 4)
🧱 Chunk généré: (-10, 5)
🧱 Chunk généré: (-10, 6)
🧱 Chunk généré: (-10, 7)
🧱 Chunk généré: (-10, 8)
🧱 Chunk généré: (-10, 9)
🧱 Chunk généré: (-9, -6)
🧱 Chunk généré: (-9, -5)
🧱 Chunk généré: (-9, -4)
🧱 Chunk généré: (-9, -3)
🧱 Chunk généré: (-9, -2)
🧱 Chunk généré: (-9, -1)
🧱 Chunk généré: (-9, 0)
🧱 Chunk généré: (-9, 1)
🧱 Chunk généré: (-9, 2)
🧱 Chunk généré: (-9, 3)
🧱 Chunk généré: (-9, 4)
🧱 Chunk généré: (-9, 5)
🧱 Chunk généré: (-9, 6)
🧱 Chunk généré: (-9, 7)
🧱 Chunk généré: (-9, 8)
🧱 Chunk généré: (-9, 9)
🧱 Chunk généré: (-9, 10)
🧱 Chunk généré: (-8, -6)
🧱 Chunk généré: (-8, -5)
🧱 Chunk généré: (-8, -4)
🧱 Chunk généré: (-8, -3)
🧱 Chunk généré: (-8, -2)
🧱 Chunk généré: (-8, -1)
🧱 Chunk généré: (-8, 0)
🧱 Chunk généré: (-8, 1)
🧱 Chunk généré: (-8, 2)
🧱 Chunk généré: (-8, 3)
🧱 Chunk généré: (-8, 4)
🧱 Chunk généré: (-8, 5)
🧱 Chunk généré: (-8, 6)
🧱 Chunk généré: (-8, 7)
🧱 Chunk généré: (-8, 8)
🧱 Chunk généré: (-8, 9)
🧱 Chunk généré: (-8, 10)
🧱 Chunk généré: (-7, -7)
🧱 Chunk généré: (-7, -6)
🧱 Chunk généré: (-7, -5)
🧱 Chunk généré: (-7, -4)
🧱 Chunk généré: (-7, -3)
🧱 Chunk généré: (-7, -2)
🧱 Chunk généré: (-7, -1)
🧱 Chunk généré: (-7, 0)
🧱 Chunk généré: (-7, 1)
🧱 Chunk généré: (-7, 2)
🧱 Chunk généré: (-7, 3)
🧱 Chunk généré: (-7, 4)
🧱 Chunk généré: (-7, 5)
🧱 Chunk généré: (-7, 6)
🧱 Chunk généré: (-7, 7)
🧱 Chunk généré: (-7, 8)
🧱 Chunk généré: (-7, 9)
🧱 Chunk généré: (-7, 10)
🧱 Chunk généré: (-7, 11)
🧱 Chunk généré: (-6, -7)
🧱 Chunk généré: (-6, -6)
🧱 Chunk généré: (-6, -5)
🧱 Chunk généré: (-6, -4)
🧱 Chunk généré: (-6, -3)
🧱 Chunk généré: (-6, -2)
🧱 Chunk généré: (-6, -1)
🧱 Chunk généré: (-6, 0)
🧱 Chunk généré: (-6, 1)
🧱 Chunk généré: (-6, 2)
🧱 Chunk généré: (-6, 3)
🧱 Chunk généré: (-6, 4)
🧱 Chunk généré: (-6, 5)
🧱 Chunk généré: (-6, 6)
🧱 Chunk généré: (-6, 7)
🧱 Chunk généré: (-6, 8)
🧱 Chunk généré: (-6, 9)
🧱 Chunk généré: (-6, 10)
🧱 Chunk généré: (-6, 11)
🧱 Chunk généré: (-5, -7)
🧱 Chunk généré: (-5, -6)
🧱 Chunk généré: (-5, -5)
🧱 Chunk généré: (-5, -4)
🧱 Chunk généré: (-5, -3)
🧱 Chunk généré: (-5, -2)
🧱 Chunk généré: (-5, -1)
🧱 Chunk généré: (-5, 0)
🧱 Chunk généré: (-5, 1)
🧱 Chunk généré: (-5, 2)
🧱 Chunk généré: (-5, 3)
🧱 Chunk généré: (-5, 4)
🧱 Chunk généré: (-5, 5)
🧱 Chunk généré: (-5, 6)
🧱 Chunk généré: (-5, 7)
🧱 Chunk généré: (-5, 8)
🧱 Chunk généré: (-5, 9)
🧱 Chunk généré: (-5, 10)
🧱 Chunk généré: (-5, 11)
🧱 Chunk généré: (-4, -7)
🧱 Chunk généré: (-4, -6)
🧱 Chunk généré: (-4, -5)
🧱 Chunk généré: (-4, -4)
🧱 Chunk généré: (-4, -3)
🧱 Chunk généré: (-4, -2)
🧱 Chunk généré: (-4, -1)
🧱 Chunk généré: (-4, 0)
🧱 Chunk généré: (-4, 1)
🧱 Chunk généré: (-4, 2)
🧱 Chunk généré: (-4, 3)
🧱 Chunk généré: (-4, 4)
🧱 Chunk généré: (-4, 5)
🧱 Chunk généré: (-4, 6)
🧱 Chunk généré: (-4, 7)
🧱 Chunk généré: (-4, 8)
🧱 Chunk généré: (-4, 9)
🧱 Chunk généré: (-4, 10)
🧱 Chunk généré: (-4, 11)
🧱 Chunk généré: (-3, -8)
🧱 Chunk généré: (-3, -7)
🧱 Chunk généré: (-3, -6)
🧱 Chunk généré: (-3, -5)
🧱 Chunk généré: (-3, -4)
🧱 Chunk généré: (-3, -3)
🧱 Chunk généré: (-3, -2)
🧱 Chunk généré: (-3, -1)
🧱 Chunk généré: (-3, 0)
🧱 Chunk généré: (-3, 1)
🧱 Chunk généré: (-3, 3)
🧱 Chunk généré: (-3, 4)
🧱 Chunk généré: (-3, 5)
🧱 Chunk généré: (-3, 6)
🧱 Chunk généré: (-3, 7)
🧱 Chunk généré: (-3, 8)
🧱 Chunk généré: (-3, 9)
🧱 Chunk généré: (-3, 10)
🧱 Chunk généré: (-3, 11)
🧱 Chunk généré: (-3, 12)
🧱 Chunk généré: (-2, -7)
🧱 Chunk généré: (-2, -6)
🧱 Chunk généré: (-2, -5)
🧱 Chunk généré: (-2, -4)
🧱 Chunk généré: (-2, -3)
🧱 Chunk généré: (-2, -2)
🧱 Chunk généré: (-2, -1)
🧱 Chunk généré: (-2, 0)
🧱 Chunk généré: (-2, 1)
🧱 Chunk généré: (-2, 2)
🧱 Chunk généré: (-2, 3)
🧱 Chunk généré: (-2, 4)
🧱 Chunk généré: (-2, 5)
🧱 Chunk généré: (-2, 6)
🧱 Chunk généré: (-2, 7)
🧱 Chunk généré: (-2, 8)
🧱 Chunk généré: (-2, 9)
🧱 Chunk généré: (-2, 10)
🧱 Chunk généré: (-2, 11)
🧱 Chunk généré: (-1, -7)
🧱 Chunk généré: (-1, -6)
🧱 Chunk généré: (-1, -5)
🧱 Chunk généré: (-1, -4)
🧱 Chunk généré: (-1, -3)
🧱 Chunk généré: (-1, -2)
🧱 Chunk généré: (-1, -1)
🧱 Chunk généré: (-1, 0)
🧱 Chunk généré: (-1, 1)
🧱 Chunk généré: (-1, 2)
🧱 Chunk généré: (-1, 3)
🧱 Chunk généré: (-1, 4)
🧱 Chunk généré: (-1, 5)
🧱 Chunk généré: (-1, 6)
🧱 Chunk généré: (-1, 7)
🧱 Chunk généré: (-1, 8)
🧱 Chunk généré: (-1, 9)
🧱 Chunk généré: (-1, 10)
🧱 Chunk généré: (-1, 11)
🧱 Chunk généré: (0, -7)
🧱 Chunk généré: (0, -6)
🧱 Chunk généré: (0, -5)
🧱 Chunk généré: (0, -4)
🧱 Chunk généré: (0, -3)
🧱 Chunk généré: (0, -2)
🧱 Chunk généré: (0, -1)
🧱 Chunk généré: (0, 0)
🧱 Chunk généré: (0, 1)
🧱 Chunk généré: (0, 2)
🧱 Chunk généré: (0, 3)
🧱 Chunk généré: (0, 4)
🧱 Chunk généré: (0, 5)
🧱 Chunk généré: (0, 6)
🧱 Chunk généré: (0, 7)
🧱 Chunk généré: (0, 8)
🧱 Chunk généré: (0, 9)
🧱 Chunk généré: (0, 10)
🧱 Chunk généré: (0, 11)
🧱 Chunk généré: (1, -7)
🧱 Chunk généré: (1, -6)
🧱 Chunk généré: (1, -5)
🧱 Chunk généré: (1, -4)
🧱 Chunk généré: (1, -3)
🧱 Chunk généré: (1, -2)
🧱 Chunk généré: (1, -1)
🧱 Chunk généré: (1, 0)
🧱 Chunk généré: (1, 1)
🧱 Chunk généré: (1, 2)
🧱 Chunk généré: (1, 3)
🧱 Chunk généré: (1, 4)
🧱 Chunk généré: (1, 5)
🧱 Chunk généré: (1, 6)
🧱 Chunk généré: (1, 7)
🧱 Chunk généré: (1, 8)
🧱 Chunk généré: (1, 9)
🧱 Chunk généré: (1, 10)
🧱 Chunk généré: (1, 11)
🧱 Chunk généré: (2, -6)
🧱 Chunk généré: (2, -5)
🧱 Chunk généré: (2, -4)
🧱 Chunk généré: (2, -3)
🧱 Chunk généré: (2, -2)
🧱 Chunk généré: (2, -1)
🧱 Chunk généré: (2, 0)
🧱 Chunk généré: (2, 1)
🧱 Chunk généré: (2, 2)
🧱 Chunk généré: (2, 3)
🧱 Chunk généré: (2, 4)
🧱 Chunk généré: (2, 5)
🧱 Chunk généré: (2, 6)
🧱 Chunk généré: (2, 7)
🧱 Chunk généré: (2, 8)
🧱 Chunk généré: (2, 9)
🧱 Chunk généré: (2, 10)
🧱 Chunk généré: (3, -6)
🧱 Chunk généré: (3, -5)
🧱 Chunk généré: (3, -4)
🧱 Chunk généré: (3, -3)
🧱 Chunk généré: (3, -2)
🧱 Chunk généré: (3, -1)
🧱 Chunk généré: (3, 0)
🧱 Chunk généré: (3, 1)
🧱 Chunk généré: (3, 2)
🧱 Chunk généré: (3, 3)
🧱 Chunk généré: (3, 4)
🧱 Chunk généré: (3, 5)
🧱 Chunk généré: (3, 6)
🧱 Chunk généré: (3, 7)
🧱 Chunk généré: (3, 8)
🧱 Chunk généré: (3, 9)
🧱 Chunk généré: (3, 10)
🧱 Chunk généré: (4, -5)
🧱 Chunk généré: (4, -4)
🧱 Chunk généré: (4, -3)
🧱 Chunk généré: (4, -2)
🧱 Chunk généré: (4, -1)
🧱 Chunk généré: (4, 0)
🧱 Chunk généré: (4, 1)
🧱 Chunk généré: (4, 2)
🧱 Chunk généré: (4, 3)
🧱 Chunk généré: (4, 4)
🧱 Chunk généré: (4, 5)
🧱 Chunk généré: (4, 6)
🧱 Chunk généré: (4, 7)
🧱 Chunk généré: (4, 8)
🧱 Chunk généré: (4, 9)
🧱 Chunk généré: (5, -4)
🧱 Chunk généré: (5, -3)
🧱 Chunk généré: (5, -2)
🧱 Chunk généré: (5, -1)
🧱 Chunk généré: (5, 0)
🧱 Chunk généré: (5, 1)
🧱 Chunk généré: (5, 2)
🧱 Chunk généré: (5, 3)
🧱 Chunk généré: (5, 4)
🧱 Chunk généré: (5, 5)
🧱 Chunk généré: (5, 6)
🧱 Chunk généré: (5, 7)
🧱 Chunk généré: (5, 8)
🧱 Chunk généré: (6, -2)
🧱 Chunk généré: (6, -1)
🧱 Chunk généré: (6, 0)
🧱 Chunk généré: (6, 1)
🧱 Chunk généré: (6, 2)
🧱 Chunk généré: (6, 3)
🧱 Chunk généré: (6, 4)
🧱 Chunk généré: (6, 5)
🧱 Chunk généré: (6, 6)
🧱 Chunk généré: (7, 2)
📊 Stats: 1 joueurs, 13 TPS, 9529.00ms/tick
👤 Nouveau joueur connecté: j3d5uhzl3mdhk1bp6
👤 Player j3d5uhzl3mdhk1bp6 a rejoint le jeu
👤 Joueur j3d5uhzl3mdhk1bp6 créé à la position (0, 100, 0)
🧱 Chunk généré: (5, -7)
🧱 Chunk généré: (-4, -11)
🧱 Chunk généré: (-4, -10)
🧱 Chunk généré: (-4, -9)
🧱 Chunk généré: (-4, -8)
🧱 Chunk généré: (-3, -13)
🧱 Chunk généré: (-3, -12)
🧱 Chunk généré: (-3, -11)
🧱 Chunk généré: (-3, -10)
🧱 Chunk généré: (-3, -9)
🧱 Chunk généré: (-2, -14)
🧱 Chunk généré: (-2, -13)
🧱 Chunk généré: (-2, -12)
🧱 Chunk généré: (-2, -11)
🧱 Chunk généré: (-2, -10)
🧱 Chunk généré: (-2, -9)
🧱 Chunk généré: (-2, -8)
🧱 Chunk généré: (-1, -15)
🧱 Chunk généré: (-1, -14)
🧱 Chunk généré: (-1, -13)
🧱 Chunk généré: (-1, -12)
🧱 Chunk généré: (-1, -11)
🧱 Chunk généré: (-1, -10)
🧱 Chunk généré: (-1, -9)
🧱 Chunk généré: (-1, -8)
🧱 Chunk généré: (0, -15)
🧱 Chunk généré: (0, -14)
🧱 Chunk généré: (0, -13)
🧱 Chunk généré: (0, -12)
🧱 Chunk généré: (0, -11)
🧱 Chunk généré: (0, -10)
🧱 Chunk généré: (0, -9)
🧱 Chunk généré: (0, -8)
🧱 Chunk généré: (1, -16)
🧱 Chunk généré: (1, -15)
🧱 Chunk généré: (1, -14)
🧱 Chunk généré: (1, -13)
🧱 Chunk généré: (1, -12)
🧱 Chunk généré: (1, -11)
🧱 Chunk généré: (1, -10)
🧱 Chunk généré: (1, -9)
🧱 Chunk généré: (1, -8)
🧱 Chunk généré: (2, -16)
🧱 Chunk généré: (2, -15)
🧱 Chunk généré: (2, -14)
🧱 Chunk généré: (2, -13)
🧱 Chunk généré: (2, -12)
🧱 Chunk généré: (2, -11)
🧱 Chunk généré: (2, -10)
🧱 Chunk généré: (2, -9)
🧱 Chunk généré: (2, -8)
🧱 Chunk généré: (2, -7)
🧱 Chunk généré: (3, -16)
🧱 Chunk généré: (3, -15)
🧱 Chunk généré: (3, -14)
🧱 Chunk généré: (3, -13)
🧱 Chunk généré: (3, -12)
🧱 Chunk généré: (3, -11)
🧱 Chunk généré: (3, -10)
🧱 Chunk généré: (3, -9)
🧱 Chunk généré: (3, -8)
🧱 Chunk généré: (3, -7)
🧱 Chunk généré: (4, -16)
🧱 Chunk généré: (4, -15)
🧱 Chunk généré: (4, -14)
🧱 Chunk généré: (4, -13)
🧱 Chunk généré: (4, -12)
🧱 Chunk généré: (4, -11)
🧱 Chunk généré: (4, -10)
🧱 Chunk généré: (4, -9)
🧱 Chunk généré: (4, -8)
🧱 Chunk généré: (4, -7)
🧱 Chunk généré: (4, -6)
🧱 Chunk généré: (5, -17)
🧱 Chunk généré: (5, -16)
🧱 Chunk généré: (5, -15)
🧱 Chunk généré: (5, -14)
🧱 Chunk généré: (5, -13)
🧱 Chunk généré: (5, -12)
🧱 Chunk généré: (5, -11)
🧱 Chunk généré: (5, -10)
🧱 Chunk généré: (5, -9)
🧱 Chunk généré: (5, -8)
🧱 Chunk généré: (5, -6)
🧱 Chunk généré: (5, -5)
🧱 Chunk généré: (6, -16)
🧱 Chunk généré: (6, -15)
🧱 Chunk généré: (6, -14)
🧱 Chunk généré: (6, -13)
🧱 Chunk généré: (6, -12)
🧱 Chunk généré: (6, -11)
🧱 Chunk généré: (6, -10)
🧱 Chunk généré: (6, -9)
🧱 Chunk généré: (6, -8)
🧱 Chunk généré: (6, -7)
🧱 Chunk généré: (6, -6)
🧱 Chunk généré: (6, -5)
🧱 Chunk généré: (6, -4)
🧱 Chunk généré: (6, -3)
🧱 Chunk généré: (7, -16)
🧱 Chunk généré: (7, -15)
🧱 Chunk généré: (7, -14)
🧱 Chunk généré: (7, -13)
🧱 Chunk généré: (7, -12)
🧱 Chunk généré: (7, -11)
🧱 Chunk généré: (7, -10)
🧱 Chunk généré: (7, -9)
🧱 Chunk généré: (7, -8)
🧱 Chunk généré: (7, -7)
🧱 Chunk généré: (7, -6)
🧱 Chunk généré: (7, -5)
🧱 Chunk généré: (7, -4)
🧱 Chunk généré: (7, -3)
🧱 Chunk généré: (7, -2)
🧱 Chunk généré: (7, -1)
🧱 Chunk généré: (7, 0)
🧱 Chunk généré: (7, 1)
🧱 Chunk généré: (8, -16)
🧱 Chunk généré: (8, -15)
🧱 Chunk généré: (8, -14)
🧱 Chunk généré: (8, -13)
🧱 Chunk généré: (8, -12)
🧱 Chunk généré: (8, -11)
🧱 Chunk généré: (8, -10)
🧱 Chunk généré: (8, -9)
🧱 Chunk généré: (8, -8)
🧱 Chunk généré: (8, -7)
🧱 Chunk généré: (8, -6)
🧱 Chunk généré: (8, -5)
🧱 Chunk généré: (8, -4)
🧱 Chunk généré: (8, -3)
🧱 Chunk généré: (8, -2)
🧱 Chunk généré: (8, -1)
🧱 Chunk généré: (8, 0)
🧱 Chunk généré: (8, 1)
🧱 Chunk généré: (8, 2)
🧱 Chunk généré: (9, -16)
🧱 Chunk généré: (9, -15)
🧱 Chunk généré: (9, -14)
🧱 Chunk généré: (9, -13)
🧱 Chunk généré: (9, -12)
🧱 Chunk généré: (9, -11)
🧱 Chunk généré: (9, -10)
🧱 Chunk généré: (9, -9)
🧱 Chunk généré: (9, -8)
🧱 Chunk généré: (9, -7)
🧱 Chunk généré: (9, -6)
🧱 Chunk généré: (9, -5)
🧱 Chunk généré: (9, -4)
🧱 Chunk généré: (9, -3)
🧱 Chunk généré: (9, -2)
🧱 Chunk généré: (9, -1)
🧱 Chunk généré: (9, 0)
🧱 Chunk généré: (9, 1)
🧱 Chunk généré: (9, 2)
🧱 Chunk généré: (10, -15)
🧱 Chunk généré: (10, -14)
🧱 Chunk généré: (10, -13)
🧱 Chunk généré: (10, -12)
🧱 Chunk généré: (10, -11)
🧱 Chunk généré: (10, -10)
🧱 Chunk généré: (10, -9)
🧱 Chunk généré: (10, -8)
🧱 Chunk généré: (10, -7)
🧱 Chunk généré: (10, -6)
🧱 Chunk généré: (10, -5)
🧱 Chunk généré: (10, -4)
🧱 Chunk généré: (10, -3)
🧱 Chunk généré: (10, -2)
🧱 Chunk généré: (10, -1)
🧱 Chunk généré: (10, 0)
🧱 Chunk généré: (10, 1)
🧱 Chunk généré: (11, -15)
🧱 Chunk généré: (11, -14)
🧱 Chunk généré: (11, -13)
🧱 Chunk généré: (11, -12)
🧱 Chunk généré: (11, -11)
🧱 Chunk généré: (11, -10)
🧱 Chunk généré: (11, -9)
🧱 Chunk généré: (11, -8)
🧱 Chunk généré: (11, -7)
🧱 Chunk généré: (11, -6)
🧱 Chunk généré: (11, -5)
🧱 Chunk généré: (11, -4)
🧱 Chunk généré: (11, -3)
🧱 Chunk généré: (11, -2)
🧱 Chunk généré: (11, -1)
🧱 Chunk généré: (11, 0)
🧱 Chunk généré: (11, 1)
🧱 Chunk généré: (12, -14)
🧱 Chunk généré: (12, -13)
🧱 Chunk généré: (12, -12)
🧱 Chunk généré: (12, -11)
🧱 Chunk généré: (12, -10)
🧱 Chunk généré: (12, -9)
🧱 Chunk généré: (12, -8)
🧱 Chunk généré: (12, -7)
🧱 Chunk généré: (12, -6)
🧱 Chunk généré: (12, -5)
🧱 Chunk généré: (12, -4)
🧱 Chunk généré: (12, -3)
🧱 Chunk généré: (12, -2)
🧱 Chunk généré: (12, -1)
🧱 Chunk généré: (12, 0)
🧱 Chunk généré: (13, -13)
🧱 Chunk généré: (13, -12)
🧱 Chunk généré: (13, -11)
🧱 Chunk généré: (13, -10)
🧱 Chunk généré: (13, -9)
🧱 Chunk généré: (13, -8)
🧱 Chunk généré: (13, -7)
🧱 Chunk généré: (13, -6)
🧱 Chunk généré: (13, -5)
🧱 Chunk généré: (13, -4)
🧱 Chunk généré: (13, -3)
🧱 Chunk généré: (13, -2)
🧱 Chunk généré: (13, -1)
🧱 Chunk généré: (14, -11)
🧱 Chunk généré: (14, -10)
🧱 Chunk généré: (14, -9)
🧱 Chunk généré: (14, -8)
🧱 Chunk généré: (14, -7)
🧱 Chunk généré: (14, -6)
🧱 Chunk généré: (14, -5)
🧱 Chunk généré: (14, -4)
🧱 Chunk généré: (14, -3)
🧱 Chunk généré: (15, -7)
👋 Joueur déconnecté: 1kcnwko9gmdhk12nq
👋 Player 1kcnwko9gmdhk12nq a quitté le jeu
🗑️ 192 chunks déchargés
📊 Stats: 1 joueurs, 17 TPS, 62.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 62.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 61.00ms/tick
📊 Stats: 1 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 1 joueurs, 17 TPS, 62.00ms/tick