PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> taskkill /F /IM node.exe
Erreur : le processus "node.exe" est introuvable.
PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> npm install

up to date, audited 71 packages in 1s

14 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> npm start

> jscraft-server@1.0.0 start
> node server/server.js

🌱 WorldGenerator initialisé avec seed: 639066
🌍 WorldManager initialisé avec seed: 639066
🎮 GameManager initialisé
🌐 Serveur WebSocket initialisé
🚀 Serveur JScraft démarré sur http://localhost:3000
📁 Fichiers client servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\client
🔗 Fichiers partagés servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\shared
🎮 Boucle de jeu serveur démarrée (20 ticks/s)
📊 Stats: 0 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 0 joueurs, 16 TPS, 62.00ms/tick
👤 Nouveau joueur connecté: x9gew4gzvmdhjfbct
👤 Player x9gew4gzvmdhjfbct a rejoint le jeu
👤 Joueur x9gew4gzvmdhjfbct créé à la position (0, 100, 0)
🧱 Chunk généré: (1, 3)
🧱 Chunk généré: (-9, 3)
🧱 Chunk généré: (-8, -1)
🧱 Chunk généré: (-8, 0)
🧱 Chunk généré: (-8, 1)
🧱 Chunk généré: (-8, 2)
🧱 Chunk généré: (-8, 3)
🧱 Chunk généré: (-8, 4)
🧱 Chunk généré: (-8, 5)
🧱 Chunk généré: (-8, 6)
🧱 Chunk généré: (-8, 7)
🧱 Chunk généré: (-7, -3)
🧱 Chunk généré: (-7, -2)
🧱 Chunk généré: (-7, -1)
🧱 Chunk généré: (-7, 0)
🧱 Chunk généré: (-7, 1)
🧱 Chunk généré: (-7, 2)
🧱 Chunk généré: (-7, 3)
🧱 Chunk généré: (-7, 4)
🧱 Chunk généré: (-7, 5)
🧱 Chunk généré: (-7, 6)
🧱 Chunk généré: (-7, 7)
🧱 Chunk généré: (-7, 8)
🧱 Chunk généré: (-7, 9)
🧱 Chunk généré: (-6, -4)
🧱 Chunk généré: (-6, -3)
🧱 Chunk généré: (-6, -2)
🧱 Chunk généré: (-6, -1)
🧱 Chunk généré: (-6, 0)
🧱 Chunk généré: (-6, 1)
🧱 Chunk généré: (-6, 2)
🧱 Chunk généré: (-6, 3)
🧱 Chunk généré: (-6, 4)
🧱 Chunk généré: (-6, 5)
🧱 Chunk généré: (-6, 6)
🧱 Chunk généré: (-6, 7)
🧱 Chunk généré: (-6, 8)
🧱 Chunk généré: (-6, 9)
🧱 Chunk généré: (-6, 10)
🧱 Chunk généré: (-5, -5)
🧱 Chunk généré: (-5, -4)
🧱 Chunk généré: (-5, -3)
🧱 Chunk généré: (-5, -2)
🧱 Chunk généré: (-5, -1)
🧱 Chunk généré: (-5, 0)
🧱 Chunk généré: (-5, 1)
🧱 Chunk généré: (-5, 2)
🧱 Chunk généré: (-5, 3)
🧱 Chunk généré: (-5, 4)
🧱 Chunk généré: (-5, 5)
🧱 Chunk généré: (-5, 6)
🧱 Chunk généré: (-5, 7)
🧱 Chunk généré: (-5, 8)
🧱 Chunk généré: (-5, 9)
🧱 Chunk généré: (-5, 10)
🧱 Chunk généré: (-5, 11)
🧱 Chunk généré: (-4, -5)
🧱 Chunk généré: (-4, -4)
🧱 Chunk généré: (-4, -3)
🧱 Chunk généré: (-4, -2)
🧱 Chunk généré: (-4, -1)
🧱 Chunk généré: (-4, 0)
🧱 Chunk généré: (-4, 1)
🧱 Chunk généré: (-4, 2)
🧱 Chunk généré: (-4, 3)
🧱 Chunk généré: (-4, 4)
🧱 Chunk généré: (-4, 5)
🧱 Chunk généré: (-4, 6)
🧱 Chunk généré: (-4, 7)
🧱 Chunk généré: (-4, 8)
🧱 Chunk généré: (-4, 9)
🧱 Chunk généré: (-4, 10)
🧱 Chunk généré: (-4, 11)
🧱 Chunk généré: (-3, -6)
🧱 Chunk généré: (-3, -5)
🧱 Chunk généré: (-3, -4)
🧱 Chunk généré: (-3, -3)
🧱 Chunk généré: (-3, -2)
🧱 Chunk généré: (-3, -1)
🧱 Chunk généré: (-3, 0)
🧱 Chunk généré: (-3, 1)
🧱 Chunk généré: (-3, 2)
🧱 Chunk généré: (-3, 3)
🧱 Chunk généré: (-3, 4)
🧱 Chunk généré: (-3, 5)
🧱 Chunk généré: (-3, 6)
🧱 Chunk généré: (-3, 7)
🧱 Chunk généré: (-3, 8)
🧱 Chunk généré: (-3, 9)
🧱 Chunk généré: (-3, 10)
🧱 Chunk généré: (-3, 11)
🧱 Chunk généré: (-3, 12)
🧱 Chunk généré: (-2, -6)
🧱 Chunk généré: (-2, -5)
🧱 Chunk généré: (-2, -4)
🧱 Chunk généré: (-2, -3)
🧱 Chunk généré: (-2, -2)
🧱 Chunk généré: (-2, -1)
🧱 Chunk généré: (-2, 0)
🧱 Chunk généré: (-2, 1)
🧱 Chunk généré: (-2, 2)
🧱 Chunk généré: (-2, 3)
🧱 Chunk généré: (-2, 4)
🧱 Chunk généré: (-2, 5)
🧱 Chunk généré: (-2, 6)
🧱 Chunk généré: (-2, 7)
🧱 Chunk généré: (-2, 8)
🧱 Chunk généré: (-2, 9)
🧱 Chunk généré: (-2, 10)
🧱 Chunk généré: (-2, 11)
🧱 Chunk généré: (-2, 12)
🧱 Chunk généré: (-1, -6)
🧱 Chunk généré: (-1, -5)
🧱 Chunk généré: (-1, -4)
🧱 Chunk généré: (-1, -3)
🧱 Chunk généré: (-1, -2)
🧱 Chunk généré: (-1, -1)
🧱 Chunk généré: (-1, 0)
🧱 Chunk généré: (-1, 1)
🧱 Chunk généré: (-1, 2)
🧱 Chunk généré: (-1, 3)
🧱 Chunk généré: (-1, 4)
🧱 Chunk généré: (-1, 5)
🧱 Chunk généré: (-1, 6)
🧱 Chunk généré: (-1, 7)
🧱 Chunk généré: (-1, 8)
🧱 Chunk généré: (-1, 9)
🧱 Chunk généré: (-1, 10)
🧱 Chunk généré: (-1, 11)
🧱 Chunk généré: (-1, 12)
🧱 Chunk généré: (0, -6)
🧱 Chunk généré: (0, -5)
🧱 Chunk généré: (0, -4)
🧱 Chunk généré: (0, -3)
🧱 Chunk généré: (0, -2)
🧱 Chunk généré: (0, -1)
🧱 Chunk généré: (0, 0)
🧱 Chunk généré: (0, 1)
🧱 Chunk généré: (0, 2)
🧱 Chunk généré: (0, 3)
🧱 Chunk généré: (0, 4)
🧱 Chunk généré: (0, 5)
🧱 Chunk généré: (0, 6)
🧱 Chunk généré: (0, 7)
🧱 Chunk généré: (0, 8)
🧱 Chunk généré: (0, 9)
🧱 Chunk généré: (0, 10)
🧱 Chunk généré: (0, 11)
🧱 Chunk généré: (0, 12)
🧱 Chunk généré: (1, -7)
🧱 Chunk généré: (1, -6)
🧱 Chunk généré: (1, -5)
🧱 Chunk généré: (1, -4)
🧱 Chunk généré: (1, -3)
🧱 Chunk généré: (1, -2)
🧱 Chunk généré: (1, -1)
🧱 Chunk généré: (1, 0)
🧱 Chunk généré: (1, 1)
🧱 Chunk généré: (1, 2)
🧱 Chunk généré: (1, 4)
🧱 Chunk généré: (1, 5)
🧱 Chunk généré: (1, 6)
🧱 Chunk généré: (1, 7)
🧱 Chunk généré: (1, 8)
🧱 Chunk généré: (1, 9)
🧱 Chunk généré: (1, 10)
🧱 Chunk généré: (1, 11)
🧱 Chunk généré: (1, 12)
🧱 Chunk généré: (1, 13)
🧱 Chunk généré: (2, -6)
🧱 Chunk généré: (2, -5)
🧱 Chunk généré: (2, -4)
🧱 Chunk généré: (2, -3)
🧱 Chunk généré: (2, -2)
🧱 Chunk généré: (2, -1)
🧱 Chunk généré: (2, 0)
🧱 Chunk généré: (2, 1)
🧱 Chunk généré: (2, 2)
🧱 Chunk généré: (2, 3)
🧱 Chunk généré: (2, 4)
🧱 Chunk généré: (2, 5)
🧱 Chunk généré: (2, 6)
🧱 Chunk généré: (2, 7)
🧱 Chunk généré: (2, 8)
🧱 Chunk généré: (2, 9)
🧱 Chunk généré: (2, 10)
🧱 Chunk généré: (2, 11)
🧱 Chunk généré: (2, 12)
🧱 Chunk généré: (3, -6)
🧱 Chunk généré: (3, -5)
🧱 Chunk généré: (3, -4)
🧱 Chunk généré: (3, -3)
🧱 Chunk généré: (3, -2)
🧱 Chunk généré: (3, -1)
🧱 Chunk généré: (3, 0)
🧱 Chunk généré: (3, 1)
🧱 Chunk généré: (3, 2)
🧱 Chunk généré: (3, 3)
🧱 Chunk généré: (3, 4)
🧱 Chunk généré: (3, 5)
🧱 Chunk généré: (3, 6)
🧱 Chunk généré: (3, 7)
🧱 Chunk généré: (3, 8)
🧱 Chunk généré: (3, 9)
🧱 Chunk généré: (3, 10)
🧱 Chunk généré: (3, 11)
🧱 Chunk généré: (3, 12)
🧱 Chunk généré: (4, -6)
🧱 Chunk généré: (4, -5)
🧱 Chunk généré: (4, -4)
🧱 Chunk généré: (4, -3)
🧱 Chunk généré: (4, -2)
🧱 Chunk généré: (4, -1)
🧱 Chunk généré: (4, 0)
🧱 Chunk généré: (4, 1)
🧱 Chunk généré: (4, 2)
🧱 Chunk généré: (4, 3)
🧱 Chunk généré: (4, 4)
🧱 Chunk généré: (4, 5)
🧱 Chunk généré: (4, 6)
🧱 Chunk généré: (4, 7)
🧱 Chunk généré: (4, 8)
🧱 Chunk généré: (4, 9)
🧱 Chunk généré: (4, 10)
🧱 Chunk généré: (4, 11)
🧱 Chunk généré: (4, 12)
🧱 Chunk généré: (5, -6)
🧱 Chunk généré: (5, -5)
🧱 Chunk généré: (5, -4)
🧱 Chunk généré: (5, -3)
🧱 Chunk généré: (5, -2)
🧱 Chunk généré: (5, -1)
🧱 Chunk généré: (5, 0)
🧱 Chunk généré: (5, 1)
🧱 Chunk généré: (5, 2)
🧱 Chunk généré: (5, 3)
🧱 Chunk généré: (5, 4)
🧱 Chunk généré: (5, 5)
🧱 Chunk généré: (5, 6)
🧱 Chunk généré: (5, 7)
🧱 Chunk généré: (5, 8)
🧱 Chunk généré: (5, 9)
🧱 Chunk généré: (5, 10)
🧱 Chunk généré: (5, 11)
🧱 Chunk généré: (5, 12)
🧱 Chunk généré: (6, -5)
🧱 Chunk généré: (6, -4)
🧱 Chunk généré: (6, -3)
🧱 Chunk généré: (6, -2)
🧱 Chunk généré: (6, -1)
🧱 Chunk généré: (6, 0)
🧱 Chunk généré: (6, 1)
🧱 Chunk généré: (6, 2)
🧱 Chunk généré: (6, 3)
🧱 Chunk généré: (6, 4)
🧱 Chunk généré: (6, 5)
🧱 Chunk généré: (6, 6)
🧱 Chunk généré: (6, 7)
🧱 Chunk généré: (6, 8)
🧱 Chunk généré: (6, 9)
🧱 Chunk généré: (6, 10)
🧱 Chunk généré: (6, 11)
🧱 Chunk généré: (7, -5)
🧱 Chunk généré: (7, -4)
🧱 Chunk généré: (7, -3)
🧱 Chunk généré: (7, -2)
🧱 Chunk généré: (7, -1)
🧱 Chunk généré: (7, 0)
🧱 Chunk généré: (7, 1)
🧱 Chunk généré: (7, 2)
🧱 Chunk généré: (7, 3)
🧱 Chunk généré: (7, 4)
🧱 Chunk généré: (7, 5)
🧱 Chunk généré: (7, 6)
🧱 Chunk généré: (7, 7)
🧱 Chunk généré: (7, 8)
🧱 Chunk généré: (7, 9)
🧱 Chunk généré: (7, 10)
🧱 Chunk généré: (7, 11)
🧱 Chunk généré: (8, -4)
🧱 Chunk généré: (8, -3)
🧱 Chunk généré: (8, -2)
🧱 Chunk généré: (8, -1)
🧱 Chunk généré: (8, 0)
🧱 Chunk généré: (8, 1)
🧱 Chunk généré: (8, 2)
🧱 Chunk généré: (8, 3)
🧱 Chunk généré: (8, 4)
🧱 Chunk généré: (8, 5)
🧱 Chunk généré: (8, 6)
🧱 Chunk généré: (8, 7)
🧱 Chunk généré: (8, 8)
🧱 Chunk généré: (8, 9)
🧱 Chunk généré: (8, 10)
🧱 Chunk généré: (9, -3)
🧱 Chunk généré: (9, -2)
🧱 Chunk généré: (9, -1)
🧱 Chunk généré: (9, 0)
🧱 Chunk généré: (9, 1)
🧱 Chunk généré: (9, 2)
🧱 Chunk généré: (9, 3)
🧱 Chunk généré: (9, 4)
🧱 Chunk généré: (9, 5)
🧱 Chunk généré: (9, 6)
🧱 Chunk généré: (9, 7)
🧱 Chunk généré: (9, 8)
🧱 Chunk généré: (9, 9)
🧱 Chunk généré: (10, -1)
🧱 Chunk généré: (10, 0)
🧱 Chunk généré: (10, 1)
🧱 Chunk généré: (10, 2)
🧱 Chunk généré: (10, 3)
🧱 Chunk généré: (10, 4)
🧱 Chunk généré: (10, 5)
🧱 Chunk généré: (10, 6)
🧱 Chunk généré: (10, 7)
🧱 Chunk généré: (11, 3)
📊 Stats: 1 joueurs, 11 TPS, 7732.00ms/tick
🧱 Chunk généré: (-9, 2)
🧱 Chunk généré: (-8, -2)
🧱 Chunk généré: (-7, -4)
🧱 Chunk généré: (-6, -5)
🧱 Chunk généré: (-5, -6)
🧱 Chunk généré: (-4, -6)
🧱 Chunk généré: (-3, -7)
🧱 Chunk généré: (-2, -7)
🧱 Chunk généré: (-1, -7)
🧱 Chunk généré: (0, -7)
🧱 Chunk généré: (1, -8)
🧱 Chunk généré: (2, -7)
🧱 Chunk généré: (3, -7)
🧱 Chunk généré: (4, -7)
🧱 Chunk généré: (5, -7)
🧱 Chunk généré: (6, -6)
🧱 Chunk généré: (7, -6)
🧱 Chunk généré: (8, -5)
🧱 Chunk généré: (9, -4)
🧱 Chunk généré: (10, -2)
🧱 Chunk généré: (11, 2)
📊 Stats: 1 joueurs, 17 TPS, 63.00ms/tick
🧱 Chunk généré: (2, -8)
🧱 Chunk généré: (6, -7)
🧱 Chunk généré: (8, -6)
🧱 Chunk généré: (9, -5)
🧱 Chunk généré: (10, -4)
🧱 Chunk généré: (10, -3)
🧱 Chunk généré: (10, 8)
🧱 Chunk généré: (11, -2)
🧱 Chunk généré: (11, -1)
🧱 Chunk généré: (11, 0)
🧱 Chunk généré: (11, 1)
🧱 Chunk généré: (11, 4)
🧱 Chunk généré: (11, 5)
🧱 Chunk généré: (11, 6)
🧱 Chunk généré: (12, 2)
👋 Joueur déconnecté: x9gew4gzvmdhjfbct
👋 Player x9gew4gzvmdhjfbct a quitté le jeu
👤 Nouveau joueur connecté: lpm5jo2i2mdhjfr8h
👤 Player lpm5jo2i2mdhjfr8h a rejoint le jeu
👤 Joueur lpm5jo2i2mdhjfr8h créé à la position (0, 100, 0)
🧱 Chunk généré: (-12, 4)
🧱 Chunk généré: (-11, 0)
🧱 Chunk généré: (-11, 1)
🧱 Chunk généré: (-11, 2)
🧱 Chunk généré: (-11, 3)
🧱 Chunk généré: (-11, 4)
🧱 Chunk généré: (-11, 5)
🧱 Chunk généré: (-11, 6)
🧱 Chunk généré: (-11, 7)
🧱 Chunk généré: (-11, 8)
🧱 Chunk généré: (-10, -2)
🧱 Chunk généré: (-10, -1)
🧱 Chunk généré: (-10, 0)
🧱 Chunk généré: (-10, 1)
🧱 Chunk généré: (-10, 2)
🧱 Chunk généré: (-10, 3)
🧱 Chunk généré: (-10, 4)
🧱 Chunk généré: (-10, 5)
🧱 Chunk généré: (-10, 6)
🧱 Chunk généré: (-10, 7)
🧱 Chunk généré: (-10, 8)
🧱 Chunk généré: (-10, 9)
🧱 Chunk généré: (-10, 10)
🧱 Chunk généré: (-9, -3)
🧱 Chunk généré: (-9, -2)
🧱 Chunk généré: (-9, -1)
🧱 Chunk généré: (-9, 0)
🧱 Chunk généré: (-9, 1)
🧱 Chunk généré: (-9, 4)
🧱 Chunk généré: (-9, 5)
🧱 Chunk généré: (-9, 6)
🧱 Chunk généré: (-9, 7)
🧱 Chunk généré: (-9, 8)
🧱 Chunk généré: (-9, 9)
🧱 Chunk généré: (-9, 10)
🧱 Chunk généré: (-9, 11)
🧱 Chunk généré: (-8, -4)
🧱 Chunk généré: (-8, -3)
🧱 Chunk généré: (-8, 8)
🧱 Chunk généré: (-8, 9)
🧱 Chunk généré: (-8, 10)
🧱 Chunk généré: (-8, 11)
🧱 Chunk généré: (-8, 12)
🧱 Chunk généré: (-7, 10)
🧱 Chunk généré: (-7, 11)
🧱 Chunk généré: (-7, 12)
🧱 Chunk généré: (-6, 11)
🧱 Chunk généré: (-6, 12)
🧱 Chunk généré: (-6, 13)
🧱 Chunk généré: (-5, 12)
🧱 Chunk généré: (-5, 13)
🧱 Chunk généré: (-4, 12)
🧱 Chunk généré: (-4, 13)
🧱 Chunk généré: (-3, 13)
🧱 Chunk généré: (-2, 13)
🧱 Chunk généré: (-2, 14)
🧱 Chunk généré: (-1, 13)
🧱 Chunk généré: (0, 13)
🧱 Chunk généré: (2, 13)
🗑️ 44 chunks déchargés
📊 Stats: 1 joueurs, 17 TPS, 63.00ms/tick

LOGS navigateur : 
🎮 Game client initialisé main.js:37:17
🚀 Initialisation du client... main.js:42:21
🖥️ UIManager initialisé UIManager.js:13:17
⚙️ OptionsManager initialisé OptionsManager.js:47:17
⚙️ Options par défaut utilisées OptionsManager.js:374:25
⚙️ Options chargées et appliquées OptionsManager.js:57:17
🎨 Renderer initialisé Renderer.js:24:17
💡 Éclairage configuré Renderer.js:99:17
🌫️ Brouillard configuré Renderer.js:109:17
✅ Renderer initialisé avec succès Renderer.js:61:21
📊 WebGL: WebGL2 Renderer.js:62:21
📊 Max textures: 16 Renderer.js:63:21
🌍 ClientWorld initialisé ClientWorld.js:33:17
📷 PlayerCamera initialisée à la position: 
Object { x: 0, y: 101.7, z: 0 }
PlayerCamera.js:29:17
🎒 Inventory initialisé Inventory.js:24:17
🎮 PlayerController initialisé PlayerController.js:43:17
💬 ChatManager initialisé ChatManager.js:25:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:105:25
✅ Client initialisé avec succès main.js:72:21
🎮 Démarrage de la boucle de jeu main.js:182:17
🎨 Infos de rendu: main.js:235:25
- Scène enfants: 2 main.js:236:25
- Position caméra: 
Object { x: 0, y: 101.7, z: 0 }
main.js:237:25
- Rotation caméra: 
Object { _x: 0, _y: 0, _z: 0, _order: "XYZ", _onChangeCallback: Ce() }
main.js:238:25
📦 Réception chunk: (1, 3) ClientWorld.js:90:17
🎨 Initialisation des ressources partagées... ClientChunk.js:30:17
✅ Géométrie partagée créée ClientChunk.js:40:17
🎨 Ressources partagées initialisées ClientChunk.js:76:17
📦 ClientChunk créé: (1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 3): 7 types de blocs, 5076 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 3), distance: 3.16 ClientWorld.js:112:17
👤 ID joueur reçu: x9gew4gzvmdhjfbct main.js:310:17
👤 ID joueur défini: x9gew4gzvmdhjfbct PlayerController.js:377:17
🌱 Seed du monde défini: 639066 ClientWorld.js:38:17
✅ Connecté au serveur et prêt à jouer main.js:102:29
📐 Renderer redimensionné: 506x624 Renderer.js:153:17
Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
🎯 Première position reçue du serveur: 
Object { x: 19, y: 78.7, z: 51 }
PlayerController.js:282:25
🔒 Pointer lock activé PlayerController.js:146:21
🔓 Pointer lock désactivé PlayerController.js:153:21
🔄 Tentative de reconnexion... main.js:158:17
🔌 Déconnexion manuelle SocketClient.js:255:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
🔌 Connexion WebSocket fermée 1000 Déconnexion manuelle SocketClient.js:81:21
❌ Déconnecté du serveur main.js:113:25
🔒 Pointer lock activé PlayerController.js:146:21
GET
ws://localhost:3000/ws
[HTTP/1.1 101 Switching Protocols 1ms]

✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:105:25
📦 Réception chunk: (-9, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-9, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-9, 2): 7 types de blocs, 4803 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-9, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-9, 2), distance: 11.00 ClientWorld.js:112:17
📦 Réception chunk: (-9, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-9, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-9, 3): 9 types de blocs, 4791 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-9, 3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-9, 3), distance: 11.05 ClientWorld.js:112:17
📦 Réception chunk: (-8, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, -2): 8 types de blocs, 5014 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, -2), distance: 10.77 ClientWorld.js:112:17
📦 Réception chunk: (-8, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, -1): 6 types de blocs, 4972 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, -1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, -1), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (-8, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, 0): 6 types de blocs, 4935 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, 0) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, 0), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (-8, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, 1): 8 types de blocs, 5072 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, 1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, 1), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (-8, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, 2): 8 types de blocs, 5021 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, 2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, 2), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (-8, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, 3): 8 types de blocs, 4934 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, 3), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (-8, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, 4): 8 types de blocs, 4852 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, 4), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (-8, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, 5): 8 types de blocs, 4840 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, 5), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (-8, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, 6): 8 types de blocs, 4797 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, 6), distance: 10.77 ClientWorld.js:112:17
📦 Réception chunk: (-8, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (-8, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-8, 7): 9 types de blocs, 4694 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-8, 7) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-8, 7), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (-7, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -4): 8 types de blocs, 4993 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, -4), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (-7, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -3): 8 types de blocs, 5003 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, -3), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (-7, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -2): 6 types de blocs, 4944 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, -2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, -2), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (-7, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -1): 8 types de blocs, 5045 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, -1), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (-7, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 0): 8 types de blocs, 4958 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 0), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-7, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 1): 9 types de blocs, 4957 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 1), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (-7, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 2): 6 types de blocs, 4910 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 2), distance: 9.00 ClientWorld.js:112:17
📦 Réception chunk: (-7, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 3): 6 types de blocs, 4914 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 3) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 3), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (-7, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 4): 8 types de blocs, 4933 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 4), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-7, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 5): 8 types de blocs, 4913 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 5), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (-7, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 6): 8 types de blocs, 4900 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 6), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (-7, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 7): 9 types de blocs, 5288 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 7) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 7), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (-7, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 8): 8 types de blocs, 5531 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 8) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 8), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (-7, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, 9): 7 types de blocs, 5442 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-7, 9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-7, 9), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (-6, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -5): 8 types de blocs, 4897 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -5), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (-6, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -4): 8 types de blocs, 4886 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -4), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (-6, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -3): 8 types de blocs, 4948 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -3), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (-6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -2): 9 types de blocs, 4969 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -2), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (-6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -1): 8 types de blocs, 4999 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, -1), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (-6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 0): 8 types de blocs, 4966 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 0), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (-6, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 1): 9 types de blocs, 4985 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 1), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 2): 6 types de blocs, 4924 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 2) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 2), distance: 8.00 ClientWorld.js:112:17
📦 Réception chunk: (-6, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 3): 8 types de blocs, 5028 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 3), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-6, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 4): 9 types de blocs, 5060 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 4), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (-6, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 5): 10 types de blocs, 5199 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 5) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 5), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (-6, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 6): 9 types de blocs, 5743 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 6) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 6), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (-6, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 7): 7 types de blocs, 5785 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 7), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (-6, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 8): 7 types de blocs, 5771 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 8), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (-6, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 9): 7 types de blocs, 5634 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 9), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (-6, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, 10): 7 types de blocs, 5627 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-6, 10) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-6, 10), distance: 11.31 ClientWorld.js:112:17
📦 Réception chunk: (-5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -5): 8 types de blocs, 4793 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -5), distance: 9.90 ClientWorld.js:112:17
📦 Réception chunk: (-5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -4): 6 types de blocs, 4764 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -4) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -4), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -3): 8 types de blocs, 4825 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -3), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (-5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -2): 7 types de blocs, 4801 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -2), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -1): 6 types de blocs, 4840 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, -1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, -1), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 0): 6 types de blocs, 4849 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 0) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 0), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (-5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 1): 6 types de blocs, 5143 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 1), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 2): 9 types de blocs, 4939 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 2), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (-5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 3): 8 types de blocs, 5075 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 3), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-5, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 4): 10 types de blocs, 5566 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 4) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 4), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (-5, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 5): 9 types de blocs, 5866 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 5), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-5, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 6): 7 types de blocs, 5880 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 6), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-5, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 7): 7 types de blocs, 5797 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 7), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (-5, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 8): 7 types de blocs, 5867 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 8), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-5, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 9): 7 types de blocs, 5783 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 9), distance: 9.90 ClientWorld.js:112:17
📦 Réception chunk: (-5, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 10): 9 types de blocs, 5701 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 10) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 10), distance: 10.63 ClientWorld.js:112:17
📦 Réception chunk: (-5, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 11): 8 types de blocs, 5493 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-5, 11) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-5, 11), distance: 11.40 ClientWorld.js:112:17
📦 Réception chunk: (-4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -5): 8 types de blocs, 4733 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -5), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -4): 8 types de blocs, 4701 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -4), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (-4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -3): 8 types de blocs, 4716 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -3), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (-4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -2): 8 types de blocs, 4782 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -2), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -1): 8 types de blocs, 4792 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, -1), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 0): 8 types de blocs, 4803 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 0) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 0), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 1): 6 types de blocs, 4813 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 1), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 2): 10 types de blocs, 5260 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 2) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 2), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (-4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 3): 9 types de blocs, 5575 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 3), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (-4, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 4): 7 types de blocs, 5848 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 4), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (-4, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 5): 7 types de blocs, 5891 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 5), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-4, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 6): 7 types de blocs, 5825 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 6), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-4, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 7): 7 types de blocs, 5975 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 7), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (-4, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 8): 7 types de blocs, 5829 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 8), distance: 8.49 ClientWorld.js:112:17
📦 Réception chunk: (-4, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 9): 7 types de blocs, 5858 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 9), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (-4, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 10): 7 types de blocs, 5814 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 10) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 10), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (-4, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 11): 8 types de blocs, 5745 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-4, 11) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-4, 11), distance: 10.82 ClientWorld.js:112:17
📦 Réception chunk: (-3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -5): 8 types de blocs, 4589 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -5), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (-3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -4): 8 types de blocs, 4670 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -4), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (-3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -3): 8 types de blocs, 4642 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -3), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -2): 8 types de blocs, 4632 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -2), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -1): 8 types de blocs, 4659 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, -1), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 0): 9 types de blocs, 4940 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 0) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 0), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 1): 10 types de blocs, 5345 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 1) a 10 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 2): 7 types de blocs, 5450 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 2), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 3): 7 types de blocs, 5693 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 3), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (-3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 4): 7 types de blocs, 5769 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 4), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (-3, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 5): 7 types de blocs, 5877 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-3, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 6): 7 types de blocs, 5863 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 6), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-3, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 7): 7 types de blocs, 5930 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 7), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (-3, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 8): 7 types de blocs, 5995 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 8), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (-3, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 9): 7 types de blocs, 5849 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 9), distance: 8.60 ClientWorld.js:112:17
📦 Réception chunk: (-3, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 10): 7 types de blocs, 5892 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 10) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 10), distance: 9.43 ClientWorld.js:112:17
📦 Réception chunk: (-3, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 11): 7 types de blocs, 5901 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 11) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 11), distance: 10.30 ClientWorld.js:112:17
📦 Réception chunk: (-3, 12) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 12): 4 types de blocs, 5724 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-3, 12) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-3, 12), distance: 11.18 ClientWorld.js:112:17
📦 Réception chunk: (-2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -6): 8 types de blocs, 4520 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -6), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (-2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -5): 8 types de blocs, 4560 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -5), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -4): 8 types de blocs, 4468 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -4), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -3): 8 types de blocs, 4632 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -3), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -2): 9 types de blocs, 4754 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -2) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -2), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (-2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -1): 9 types de blocs, 5114 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, -1) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, -1), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 0): 7 types de blocs, 5251 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 0), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 1): 7 types de blocs, 5281 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 2): 7 types de blocs, 5420 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 2), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (-2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 3): 8 types de blocs, 5508 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 3), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (-2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 4): 7 types de blocs, 5739 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (-2, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 5): 7 types de blocs, 5689 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 5), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-2, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 6): 7 types de blocs, 5918 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 6), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (-2, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 7): 8 types de blocs, 5938 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 7), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (-2, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 8): 8 types de blocs, 5949 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 8) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 8), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (-2, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 9): 8 types de blocs, 5967 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 9) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 9), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (-2, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 10): 7 types de blocs, 6035 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 10) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 10), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (-2, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 11): 4 types de blocs, 5889 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 11) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 11), distance: 9.85 ClientWorld.js:112:17
📦 Réception chunk: (-2, 12) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 12): 4 types de blocs, 5692 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-2, 12) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-2, 12), distance: 10.77 ClientWorld.js:112:17
📦 Réception chunk: (-1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -5): 8 types de blocs, 4409 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -5), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -4): 8 types de blocs, 4671 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -4), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -3): 9 types de blocs, 4777 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -3) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -2): 7 types de blocs, 4937 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -2), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -1): 8 types de blocs, 5043 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, -1), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (-1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 0): 7 types de blocs, 5001 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 0), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 1): 7 types de blocs, 5077 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 2): 7 types de blocs, 5280 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 2), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 3): 7 types de blocs, 5283 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (-1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 4): 8 types de blocs, 5620 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 4) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 4), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (-1, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 5): 7 types de blocs, 5671 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 5), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (-1, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 6): 7 types de blocs, 5740 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 6), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (-1, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 7): 8 types de blocs, 5860 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 7) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 7), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (-1, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 8): 7 types de blocs, 5865 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 8) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 8), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (-1, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 9): 7 types de blocs, 6040 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 9) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 9), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (-1, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 10): 4 types de blocs, 5969 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 10) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 10), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (-1, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 11): 4 types de blocs, 5816 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 11) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 11), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (-1, 12) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 12): 4 types de blocs, 5701 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (-1, 12) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (-1, 12), distance: 10.44 ClientWorld.js:112:17
📦 Réception chunk: (0, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -5): 9 types de blocs, 4507 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -5) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -5), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (0, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -4): 7 types de blocs, 4748 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -4), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (0, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -3): 8 types de blocs, 4712 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -3), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (0, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -2): 8 types de blocs, 4888 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (0, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -1): 8 types de blocs, 4800 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, -1), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (0, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 0): 7 types de blocs, 4853 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 0), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (0, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 1): 7 types de blocs, 5086 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (0, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 2): 7 types de blocs, 5134 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 2), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (0, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 3): 7 types de blocs, 5215 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 3), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (0, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 4): 9 types de blocs, 5402 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 4), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (0, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 5): 7 types de blocs, 5521 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 5), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (0, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 6): 8 types de blocs, 5649 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 6), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (0, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 7): 7 types de blocs, 5812 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 7), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (0, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 8): 8 types de blocs, 5915 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 8) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 8), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (0, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 9): 5 types de blocs, 5954 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 9) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 9), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (0, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 10): 4 types de blocs, 5855 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 10) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 10), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (0, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 11): 4 types de blocs, 5700 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 11) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 11), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (0, 12) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 12): 4 types de blocs, 5521 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (0, 12) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (0, 12), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -5): 8 types de blocs, 4590 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -5), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -4): 7 types de blocs, 4648 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -4), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -3): 7 types de blocs, 4514 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -3), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -2): 7 types de blocs, 4727 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -2), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -1): 7 types de blocs, 4575 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 0): 7 types de blocs, 4804 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 0), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 1): 7 types de blocs, 4863 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 2): 7 types de blocs, 5034 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 2), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 3): 7 types de blocs, 5076 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 3), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (1, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 4): 9 types de blocs, 5218 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 4) a 9 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 4), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (1, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 5): 8 types de blocs, 5226 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 5), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (1, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 6): 8 types de blocs, 5560 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 6) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 6), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (1, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 7): 7 types de blocs, 5741 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 7) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 7), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (1, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 8): 4 types de blocs, 5826 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 8), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (1, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 9): 4 types de blocs, 5808 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 9) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 9), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (1, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 10): 5 types de blocs, 5719 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 10) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 10), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (1, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 11): 5 types de blocs, 5571 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 11) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 11), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (1, 12) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 12): 4 types de blocs, 5387 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 12) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 12), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (1, 13) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 13) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 13): 4 types de blocs, 6085 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (1, 13) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (1, 13), distance: 11.05 ClientWorld.js:112:17
📦 Réception chunk: (2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -5): 8 types de blocs, 4386 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -5) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -5), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -4): 7 types de blocs, 4417 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -4), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -3): 8 types de blocs, 4455 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -3) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -3), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -2): 7 types de blocs, 4442 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -2), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -1): 7 types de blocs, 4513 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, -1), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 0): 7 types de blocs, 4617 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 0), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 1): 7 types de blocs, 4734 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 1), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 2): 7 types de blocs, 4734 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 2), distance: 0.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 3): 7 types de blocs, 4912 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 3), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 4): 7 types de blocs, 5067 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 4), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 5): 7 types de blocs, 5363 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 5) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 5), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 6): 7 types de blocs, 5438 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 6) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 6), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 7): 5 types de blocs, 5579 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 7) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 7), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 8): 4 types de blocs, 5638 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 8), distance: 6.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 9): 4 types de blocs, 5625 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 9) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 9), distance: 7.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 10): 5 types de blocs, 5543 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 10) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 10), distance: 8.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 11): 5 types de blocs, 5406 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 11) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 11), distance: 9.00 ClientWorld.js:112:17
📦 Réception chunk: (2, 12) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 12): 4 types de blocs, 6175 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (2, 12) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (2, 12), distance: 10.00 ClientWorld.js:112:17
📦 Réception chunk: (3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -4): 7 types de blocs, 4313 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -4), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -3): 7 types de blocs, 4170 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -3), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -2): 8 types de blocs, 4223 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -2), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -1): 8 types de blocs, 4426 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, -1) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, -1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 0): 7 types de blocs, 4437 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 0), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 1): 7 types de blocs, 4504 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 1), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 2): 7 types de blocs, 4675 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 2), distance: 1.00 ClientWorld.js:112:17
📦 Réception chunk: (3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 3): 7 types de blocs, 4855 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 3), distance: 1.41 ClientWorld.js:112:17
📦 Réception chunk: (3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 4): 7 types de blocs, 4937 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 4), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (3, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 5): 5 types de blocs, 5024 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 5) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 5), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (3, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 6): 5 types de blocs, 5217 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 6) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 6), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (3, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 7): 5 types de blocs, 5344 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 7) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 7), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (3, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 8): 4 types de blocs, 5409 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 8), distance: 6.08 ClientWorld.js:112:17
📦 Réception chunk: (3, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 9): 4 types de blocs, 5404 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 9) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 9), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (3, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 10): 4 types de blocs, 5331 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 10) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 10), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (3, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 11): 4 types de blocs, 6284 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 11) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 11), distance: 9.06 ClientWorld.js:112:17
📦 Réception chunk: (3, 12) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 12): 4 types de blocs, 6676 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (3, 12) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (3, 12), distance: 10.05 ClientWorld.js:112:17
📦 Réception chunk: (4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -4): 7 types de blocs, 4173 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -4) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -4), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -3): 7 types de blocs, 4064 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -3), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -2): 8 types de blocs, 4087 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -2), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -1): 7 types de blocs, 4066 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, -1), distance: 3.61 ClientWorld.js:112:17
✅ Connecté au serveur et prêt à jouer main.js:102:29
📦 Réception chunk: (4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 0): 7 types de blocs, 4216 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 0), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 1): 7 types de blocs, 4410 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 1), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 2): 7 types de blocs, 4449 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 2), distance: 2.00 ClientWorld.js:112:17
📦 Réception chunk: (4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 3): 6 types de blocs, 4374 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 3) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 3), distance: 2.24 ClientWorld.js:112:17
📦 Réception chunk: (4, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 4): 4 types de blocs, 4587 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 4), distance: 2.83 ClientWorld.js:112:17
📦 Réception chunk: (4, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 5): 6 types de blocs, 4877 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 5) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 5), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (4, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 6): 5 types de blocs, 4968 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 6) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 6), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (4, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 7): 5 types de blocs, 5099 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 7) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 7), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (4, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 8): 5 types de blocs, 5168 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 8) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 8), distance: 6.32 ClientWorld.js:112:17
📦 Réception chunk: (4, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 9): 5 types de blocs, 5196 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 9) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 9), distance: 7.28 ClientWorld.js:112:17
📦 Réception chunk: (4, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 10): 4 types de blocs, 6331 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 10) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 10), distance: 8.25 ClientWorld.js:112:17
📦 Réception chunk: (4, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 11): 4 types de blocs, 6604 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 11) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 11), distance: 9.22 ClientWorld.js:112:17
📦 Réception chunk: (4, 12) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 12): 4 types de blocs, 6420 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (4, 12) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (4, 12), distance: 10.20 ClientWorld.js:112:17
📦 Réception chunk: (5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -3): 7 types de blocs, 3936 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -3) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -3), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -2): 8 types de blocs, 3888 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -2) a 8 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -2), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -1): 7 types de blocs, 4043 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, -1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, -1), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 0): 7 types de blocs, 3912 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 0), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 1): 5 types de blocs, 3810 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 1) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 1), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 2): 4 types de blocs, 3977 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 2) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 2), distance: 3.00 ClientWorld.js:112:17
📦 Réception chunk: (5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 3): 4 types de blocs, 4162 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 3) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 3), distance: 3.16 ClientWorld.js:112:17
📦 Réception chunk: (5, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 4): 5 types de blocs, 4362 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 4) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 4), distance: 3.61 ClientWorld.js:112:17
📦 Réception chunk: (5, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 5): 5 types de blocs, 4561 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 5) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 5), distance: 4.24 ClientWorld.js:112:17
📦 Réception chunk: (5, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 6): 4 types de blocs, 4738 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 6) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 6), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (5, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 7): 5 types de blocs, 4864 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 7) a 5 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 7), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (5, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 8): 4 types de blocs, 5269 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 8), distance: 6.71 ClientWorld.js:112:17
📦 Réception chunk: (5, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 9): 4 types de blocs, 6196 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 9) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 9), distance: 7.62 ClientWorld.js:112:17
📦 Réception chunk: (5, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 10): 4 types de blocs, 6436 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 10) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 10), distance: 8.54 ClientWorld.js:112:17
📦 Réception chunk: (5, 11) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 11): 4 types de blocs, 6305 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (5, 11) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (5, 11), distance: 9.49 ClientWorld.js:112:17
📦 Réception chunk: (6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -2): 7 types de blocs, 3514 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -2) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -2), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -1): 4 types de blocs, 3495 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, -1) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, -1), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 0): 7 types de blocs, 3555 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 0) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 0), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (6, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 1): 7 types de blocs, 3672 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 1) a 7 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 1), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 2): 4 types de blocs, 3818 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 2) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 2), distance: 4.00 ClientWorld.js:112:17
📦 Réception chunk: (6, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 3): 4 types de blocs, 3996 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 3) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 3), distance: 4.12 ClientWorld.js:112:17
📦 Réception chunk: (6, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 4): 4 types de blocs, 4193 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 4), distance: 4.47 ClientWorld.js:112:17
📦 Réception chunk: (6, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 5): 4 types de blocs, 4384 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 5) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 5), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (6, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 6): 4 types de blocs, 4552 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 6) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 6), distance: 5.66 ClientWorld.js:112:17
📦 Réception chunk: (6, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 7): 6 types de blocs, 5337 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 7) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 7), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (6, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 8): 4 types de blocs, 5990 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 8), distance: 7.21 ClientWorld.js:112:17
📦 Réception chunk: (6, 9) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 9): 4 types de blocs, 6164 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 9) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 9), distance: 8.06 ClientWorld.js:112:17
📦 Réception chunk: (6, 10) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 10): 4 types de blocs, 6134 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (6, 10) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (6, 10), distance: 8.94 ClientWorld.js:112:17
📦 Réception chunk: (7, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 0): 6 types de blocs, 3485 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 0) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 0), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (7, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 1): 6 types de blocs, 3589 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 1) a 6 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 1), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (7, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 2): 4 types de blocs, 3735 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 2) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 2), distance: 5.00 ClientWorld.js:112:17
📦 Réception chunk: (7, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 3): 4 types de blocs, 3907 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 3) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 3), distance: 5.10 ClientWorld.js:112:17
📦 Réception chunk: (7, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 4): 4 types de blocs, 4096 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 4), distance: 5.39 ClientWorld.js:112:17
📦 Réception chunk: (7, 5) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 5): 4 types de blocs, 4535 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 5) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 5), distance: 5.83 ClientWorld.js:112:17
📦 Réception chunk: (7, 6) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 6): 4 types de blocs, 5247 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 6) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 6), distance: 6.40 ClientWorld.js:112:17
📦 Réception chunk: (7, 7) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 7): 4 types de blocs, 5688 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 7) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 7), distance: 7.07 ClientWorld.js:112:17
📦 Réception chunk: (7, 8) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 8): 4 types de blocs, 5875 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (7, 8) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (7, 8), distance: 7.81 ClientWorld.js:112:17
📦 Réception chunk: (8, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 4): 4 types de blocs, 4546 blocs visibles ClientChunk.js:183:17
✅ Mesh chunk (8, 4) a 4 enfants ClientChunk.js:187:21
✅ Chunk ajouté: (8, 4), distance: 6.32 ClientWorld.js:112:17
👤 ID joueur reçu: lpm5jo2i2mdhjfr8h main.js:310:17
👤 ID joueur défini: lpm5jo2i2mdhjfr8h PlayerController.js:377:17
🌱 Seed du monde défini: 639066 ClientWorld.js:38:17
🔓 Pointer lock désactivé PlayerController.js:153:21
