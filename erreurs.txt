🔄 Tentative de reconnexion... main.js:152:17
🔌 Déconnexion manuelle SocketClient.js:255:17
🌐 Connexion au serveur: ws://localhost:3000/ws main.js:84:17
🔌 Tentative de connexion à ws://localhost:3000/ws SocketClient.js:38:21
🔌 Connexion WebSocket fermée 1000 Déconnexion manuelle SocketClient.js:81:21
❌ Déconnecté du serveur main.js:107:25
🔒 Pointer lock activé PlayerController.js:146:21
GET
ws://localhost:3000/ws
[HTTP/1.1 101 Switching Protocols 2ms]

✅ Connexion WebSocket établie SocketClient.js:51:21
✅ Connecté au serveur main.js:99:25
📦 Réception chunk: (-7, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-7, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-7, -6): 10 types de blocs, 4280 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-6, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -10): 5 types de blocs, 3750 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-6, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -9): 8 types de blocs, 3787 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-6, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -8): 6 types de blocs, 3887 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
🔓 Pointer lock désactivé PlayerController.js:153:21
📦 Réception chunk: (-6, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -7): 8 types de blocs, 4086 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-6, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -6): 6 types de blocs, 4127 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-6, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -5): 8 types de blocs, 4347 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-6, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -4): 8 types de blocs, 4487 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-6, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -3): 8 types de blocs, 4585 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-6, -2): 9 types de blocs, 4680 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -12): 4 types de blocs, 3806 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -11): 7 types de blocs, 3772 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -10): 9 types de blocs, 3779 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -9): 7 types de blocs, 3728 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -8): 9 types de blocs, 3849 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -7): 6 types de blocs, 3906 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -6): 9 types de blocs, 4065 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -5): 8 types de blocs, 4223 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -4): 9 types de blocs, 4385 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -3): 8 types de blocs, 4488 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -2): 8 types de blocs, 4622 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, -1): 8 types de blocs, 4694 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-5, 0): 6 types de blocs, 4698 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -12): 4 types de blocs, 3752 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -11): 10 types de blocs, 3776 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -10): 9 types de blocs, 3771 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -9): 8 types de blocs, 3760 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -8): 9 types de blocs, 3794 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -7): 8 types de blocs, 3882 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -6): 7 types de blocs, 3961 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -5): 8 types de blocs, 4134 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -4): 9 types de blocs, 4335 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -3): 8 types de blocs, 4429 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -2): 8 types de blocs, 4510 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, -1): 8 types de blocs, 4627 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 0): 9 types de blocs, 4677 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-4, 1): 9 types de blocs, 4897 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -12): 7 types de blocs, 3754 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -11): 7 types de blocs, 3764 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -10): 8 types de blocs, 3743 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -9): 6 types de blocs, 3733 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -8): 8 types de blocs, 3831 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -7): 6 types de blocs, 3796 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -6): 8 types de blocs, 3922 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -5): 6 types de blocs, 4023 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -4): 8 types de blocs, 4246 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -3): 8 types de blocs, 4366 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -2): 8 types de blocs, 4461 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, -1): 8 types de blocs, 4571 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 0): 9 types de blocs, 4911 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 1): 8 types de blocs, 5260 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-3, 2): 7 types de blocs, 5434 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -12): 9 types de blocs, 3878 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -11): 8 types de blocs, 3969 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -10): 10 types de blocs, 3757 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -9): 6 types de blocs, 3712 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -8): 9 types de blocs, 3758 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -7): 7 types de blocs, 3748 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -6): 8 types de blocs, 3867 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -5): 8 types de blocs, 4013 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -4): 8 types de blocs, 4127 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -3): 8 types de blocs, 4260 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -2): 10 types de blocs, 4445 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, -1): 10 types de blocs, 4800 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 0): 7 types de blocs, 5149 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 1): 7 types de blocs, 5158 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-2, 2): 7 types de blocs, 5236 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -13) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -13) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -13): 7 types de blocs, 3937 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -12): 8 types de blocs, 3962 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -11): 8 types de blocs, 3869 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -10): 10 types de blocs, 3811 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -9): 8 types de blocs, 3750 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -8): 9 types de blocs, 3752 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -7): 8 types de blocs, 3807 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -6): 8 types de blocs, 3885 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -5): 6 types de blocs, 3924 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -4): 9 types de blocs, 4237 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -3): 9 types de blocs, 4412 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -2): 8 types de blocs, 4791 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, -1): 7 types de blocs, 4891 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 0): 7 types de blocs, 5028 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 1): 7 types de blocs, 5129 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 2): 7 types de blocs, 5236 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (-1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (-1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (-1, 3): 7 types de blocs, 5270 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -12): 7 types de blocs, 3968 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -11): 9 types de blocs, 3891 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -10): 8 types de blocs, 3853 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -9): 6 types de blocs, 3717 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -8): 9 types de blocs, 3776 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -7): 6 types de blocs, 3721 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -6): 8 types de blocs, 3863 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -5): 9 types de blocs, 4072 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -4): 9 types de blocs, 4345 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -3): 7 types de blocs, 4476 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -2): 7 types de blocs, 4654 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, -1): 8 types de blocs, 4908 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 0): 7 types de blocs, 5031 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 1): 7 types de blocs, 5098 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 2): 7 types de blocs, 5179 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (0, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (0, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (0, 3): 8 types de blocs, 5203 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -12): 7 types de blocs, 4008 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -11): 8 types de blocs, 3874 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -10): 9 types de blocs, 3841 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -9): 8 types de blocs, 3748 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -8): 8 types de blocs, 3764 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -7): 8 types de blocs, 3780 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -6): 9 types de blocs, 3924 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -5): 7 types de blocs, 4151 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -4): 7 types de blocs, 4299 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -3): 7 types de blocs, 4531 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -2): 7 types de blocs, 4700 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, -1): 7 types de blocs, 4753 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 0): 7 types de blocs, 4897 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 1): 7 types de blocs, 4956 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 2): 7 types de blocs, 5156 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (1, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (1, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (1, 3): 7 types de blocs, 5144 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -12): 8 types de blocs, 4144 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -11): 8 types de blocs, 3921 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -10): 8 types de blocs, 3859 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -9): 8 types de blocs, 3716 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -8): 7 types de blocs, 3650 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -7): 9 types de blocs, 3838 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -6): 7 types de blocs, 3900 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -5): 7 types de blocs, 4032 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -4): 7 types de blocs, 4139 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -3): 7 types de blocs, 4416 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -2): 7 types de blocs, 4532 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, -1): 7 types de blocs, 4611 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 0): 7 types de blocs, 4810 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 1): 7 types de blocs, 4943 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 2): 8 types de blocs, 5003 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (2, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (2, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (2, 3): 7 types de blocs, 5031 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -12) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -12) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -12): 9 types de blocs, 4028 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -11): 6 types de blocs, 3881 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -10): 8 types de blocs, 3783 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -9): 8 types de blocs, 3709 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -8): 9 types de blocs, 3850 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -7): 7 types de blocs, 3828 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -6): 7 types de blocs, 3893 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -5): 7 types de blocs, 3906 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -4): 7 types de blocs, 4190 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -3): 7 types de blocs, 4237 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -2): 7 types de blocs, 4389 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, -1): 7 types de blocs, 4622 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 0): 7 types de blocs, 4723 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 1): 7 types de blocs, 4920 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 2): 7 types de blocs, 4922 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 3): 7 types de blocs, 4912 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (3, 4) ClientWorld.js:90:17
📦 ClientChunk créé: (3, 4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (3, 4): 7 types de blocs, 5131 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -11): 8 types de blocs, 3879 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -10): 8 types de blocs, 3769 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -9): 9 types de blocs, 3710 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -8): 7 types de blocs, 3768 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -7): 7 types de blocs, 3692 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -6): 8 types de blocs, 3780 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -5): 8 types de blocs, 3842 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -4): 7 types de blocs, 4087 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -3): 7 types de blocs, 4129 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -2): 7 types de blocs, 4315 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, -1): 7 types de blocs, 4518 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 0): 10 types de blocs, 4535 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 1): 10 types de blocs, 4799 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 2): 8 types de blocs, 4894 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (4, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (4, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (4, 3): 7 types de blocs, 5100 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -11): 8 types de blocs, 3922 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -10): 9 types de blocs, 3791 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -9): 8 types de blocs, 3664 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -8): 7 types de blocs, 3728 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -7): 7 types de blocs, 3633 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -6): 8 types de blocs, 3668 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -5): 9 types de blocs, 3833 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -4): 10 types de blocs, 3981 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -3): 7 types de blocs, 4046 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -2): 7 types de blocs, 4204 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, -1): 7 types de blocs, 4426 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 0): 10 types de blocs, 4559 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 1): 10 types de blocs, 4721 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 2): 7 types de blocs, 4798 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (5, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (5, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (5, 3): 4 types de blocs, 4796 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -11) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -11) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -11): 8 types de blocs, 3866 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -10): 8 types de blocs, 3832 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -9): 7 types de blocs, 3783 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -8): 7 types de blocs, 3658 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -7): 7 types de blocs, 3631 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -6): 8 types de blocs, 3657 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -5): 7 types de blocs, 3836 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -4): 7 types de blocs, 3831 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -3): 9 types de blocs, 4057 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -2): 7 types de blocs, 4228 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, -1): 7 types de blocs, 4397 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 0): 8 types de blocs, 4432 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 1): 7 types de blocs, 4558 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 2): 4 types de blocs, 4650 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (6, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (6, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (6, 3): 4 types de blocs, 4774 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -10): 7 types de blocs, 3771 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -9): 7 types de blocs, 3620 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -8): 7 types de blocs, 3640 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -7): 7 types de blocs, 3645 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -6): 8 types de blocs, 3810 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -5): 8 types de blocs, 3723 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -4): 7 types de blocs, 3860 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -3): 8 types de blocs, 4047 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -2): 7 types de blocs, 4237 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, -1): 8 types de blocs, 4229 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 0): 5 types de blocs, 4245 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 1): 4 types de blocs, 4451 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 2): 4 types de blocs, 4628 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (7, 3) ClientWorld.js:90:17
📦 ClientChunk créé: (7, 3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (7, 3): 4 types de blocs, 4774 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -10) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -10) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -10): 7 types de blocs, 3810 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -9): 7 types de blocs, 3675 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -8): 7 types de blocs, 3576 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -7): 7 types de blocs, 3537 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -6): 7 types de blocs, 3612 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -5): 7 types de blocs, 3664 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -4): 7 types de blocs, 3917 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -3): 7 types de blocs, 4103 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -2): 7 types de blocs, 3987 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (8, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, -1): 5 types de blocs, 4082 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 0): 5 types de blocs, 4268 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 1): 5 types de blocs, 4532 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (8, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (8, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (8, 2): 4 types de blocs, 4657 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -9) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -9) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -9): 7 types de blocs, 3671 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -8) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -8) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -8): 7 types de blocs, 3740 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -7): 8 types de blocs, 3577 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -6): 8 types de blocs, 3752 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -5): 7 types de blocs, 3634 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -4): 7 types de blocs, 3931 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -3): 7 types de blocs, 3842 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -2): 4 types de blocs, 3930 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (9, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, -1): 5 types de blocs, 4132 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 0): 5 types de blocs, 4329 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 1): 5 types de blocs, 4525 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (9, 2) ClientWorld.js:90:17
📦 ClientChunk créé: (9, 2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (9, 2): 4 types de blocs, 4722 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, -7) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -7) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -7): 7 types de blocs, 3760 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, -6) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -6) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -6): 7 types de blocs, 3776 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, -5) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -5) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -5): 7 types de blocs, 3819 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, -4) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -4) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -4): 7 types de blocs, 3789 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -3): 4 types de blocs, 3805 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -2): 5 types de blocs, 4019 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, -1) ClientWorld.js:90:17
📦 ClientChunk créé: (10, -1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, -1): 5 types de blocs, 4224 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, 0) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 0) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 0): 6 types de blocs, 4416 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (10, 1) ClientWorld.js:90:17
📦 ClientChunk créé: (10, 1) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (10, 1): 5 types de blocs, 4616 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (11, -3) ClientWorld.js:90:17
📦 ClientChunk créé: (11, -3) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, -3): 4 types de blocs, 3920 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
📦 Réception chunk: (11, -2) ClientWorld.js:90:17
📦 ClientChunk créé: (11, -2) ClientChunk.js:22:17
🧱 Mesh construit pour chunk (11, -2): 5 types de blocs, 4135 blocs visibles ClientChunk.js:173:17
❌ Erreur parsing message serveur: ReferenceError: distance is not defined
    handleChunkData http://localhost:3000/js/world/ClientWorld.js:111
    handleChunkData http://localhost:3000/js/main.js:300
    onServerMessage http://localhost:3000/js/main.js:255
    handleMessage http://localhost:3000/js/network/SocketClient.js:116
    onmessage http://localhost:3000/js/network/SocketClient.js:70
    setupEventHandlers http://localhost:3000/js/network/SocketClient.js:67
    connect http://localhost:3000/js/network/SocketClient.js:41
    SocketClient http://localhost:3000/js/network/SocketClient.js:33
    connectToServer http://localhost:3000/js/main.js:86
    reconnect http://localhost:3000/js/main.js:163
    setupEventListeners http://localhost:3000/js/main.js:138
SocketClient.js:76:25
👤 ID joueur reçu: yhvwahhg1mdhioukt main.js:285:17
👤 ID joueur défini: yhvwahhg1mdhioukt PlayerController.js:363:17
🌱 Seed du monde défini: 518552 ClientWorld.js:38:17
PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> npm start

> jscraft-server@1.0.0 start
> node server/server.js

🌱 WorldGenerator initialisé avec seed: 518552
🌍 WorldManager initialisé avec seed: 518552
🎮 GameManager initialisé
🌐 Serveur WebSocket initialisé
🚀 Serveur JScraft démarré sur http://localhost:3000
📁 Fichiers client servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\client
🔗 Fichiers partagés servis depuis: C:\Users\<USER>\Desktop\interface\JScraft - Copie\shared
🎮 Boucle de jeu serveur démarrée (20 ticks/s)
👤 Nouveau joueur connecté: 06opizp40mdhio6bj
👤 Player 06opizp40mdhio6bj a rejoint le jeu
👤 Joueur 06opizp40mdhio6bj créé à la position (0, 100, 0)
🧱 Chunk généré: (4, 4)
🧱 Chunk généré: (-6, 4)
🧱 Chunk généré: (-5, 0)
🧱 Chunk généré: (-5, 1)
🧱 Chunk généré: (-5, 2)
🧱 Chunk généré: (-5, 3)
🧱 Chunk généré: (-5, 4)
🧱 Chunk généré: (-5, 5)
🧱 Chunk généré: (-5, 6)
🧱 Chunk généré: (-5, 7)
🧱 Chunk généré: (-5, 8)
🧱 Chunk généré: (-4, -2)
🧱 Chunk généré: (-4, -1)
🧱 Chunk généré: (-4, 0)
🧱 Chunk généré: (-4, 1)
🧱 Chunk généré: (-4, 2)
🧱 Chunk généré: (-4, 3)
🧱 Chunk généré: (-4, 4)
🧱 Chunk généré: (-4, 5)
🧱 Chunk généré: (-4, 6)
🧱 Chunk généré: (-4, 7)
🧱 Chunk généré: (-4, 8)
🧱 Chunk généré: (-4, 9)
🧱 Chunk généré: (-4, 10)
🧱 Chunk généré: (-3, -3)
🧱 Chunk généré: (-3, -2)
🧱 Chunk généré: (-3, -1)
🧱 Chunk généré: (-3, 0)
🧱 Chunk généré: (-3, 1)
🧱 Chunk généré: (-3, 2)
🧱 Chunk généré: (-3, 3)
🧱 Chunk généré: (-3, 4)
🧱 Chunk généré: (-3, 5)
🧱 Chunk généré: (-3, 6)
🧱 Chunk généré: (-3, 7)
🧱 Chunk généré: (-3, 8)
🧱 Chunk généré: (-3, 9)
🧱 Chunk généré: (-3, 10)
🧱 Chunk généré: (-3, 11)
🧱 Chunk généré: (-2, -4)
🧱 Chunk généré: (-2, -3)
🧱 Chunk généré: (-2, -2)
🧱 Chunk généré: (-2, -1)
🧱 Chunk généré: (-2, 0)
🧱 Chunk généré: (-2, 1)
🧱 Chunk généré: (-2, 2)
🧱 Chunk généré: (-2, 3)
🧱 Chunk généré: (-2, 4)
🧱 Chunk généré: (-2, 5)
🧱 Chunk généré: (-2, 6)
🧱 Chunk généré: (-2, 7)
🧱 Chunk généré: (-2, 8)
🧱 Chunk généré: (-2, 9)
🧱 Chunk généré: (-2, 10)
🧱 Chunk généré: (-2, 11)
🧱 Chunk généré: (-2, 12)
🧱 Chunk généré: (-1, -4)
🧱 Chunk généré: (-1, -3)
🧱 Chunk généré: (-1, -2)
🧱 Chunk généré: (-1, -1)
🧱 Chunk généré: (-1, 0)
🧱 Chunk généré: (-1, 1)
🧱 Chunk généré: (-1, 2)
🧱 Chunk généré: (-1, 3)
🧱 Chunk généré: (-1, 4)
🧱 Chunk généré: (-1, 5)
🧱 Chunk généré: (-1, 6)
🧱 Chunk généré: (-1, 7)
🧱 Chunk généré: (-1, 8)
🧱 Chunk généré: (-1, 9)
🧱 Chunk généré: (-1, 10)
🧱 Chunk généré: (-1, 11)
🧱 Chunk généré: (-1, 12)
🧱 Chunk généré: (0, -5)
🧱 Chunk généré: (0, -4)
🧱 Chunk généré: (0, -3)
🧱 Chunk généré: (0, -2)
🧱 Chunk généré: (0, -1)
🧱 Chunk généré: (0, 0)
🧱 Chunk généré: (0, 1)
🧱 Chunk généré: (0, 2)
🧱 Chunk généré: (0, 3)
🧱 Chunk généré: (0, 4)
🧱 Chunk généré: (0, 5)
🧱 Chunk généré: (0, 6)
🧱 Chunk généré: (0, 7)
🧱 Chunk généré: (0, 8)
🧱 Chunk généré: (0, 9)
🧱 Chunk généré: (0, 10)
🧱 Chunk généré: (0, 11)
🧱 Chunk généré: (0, 12)
🧱 Chunk généré: (0, 13)
🧱 Chunk généré: (1, -5)
🧱 Chunk généré: (1, -4)
🧱 Chunk généré: (1, -3)
🧱 Chunk généré: (1, -2)
🧱 Chunk généré: (1, -1)
🧱 Chunk généré: (1, 0)
🧱 Chunk généré: (1, 1)
🧱 Chunk généré: (1, 2)
🧱 Chunk généré: (1, 3)
🧱 Chunk généré: (1, 4)
🧱 Chunk généré: (1, 5)
🧱 Chunk généré: (1, 6)
🧱 Chunk généré: (1, 7)
🧱 Chunk généré: (1, 8)
🧱 Chunk généré: (1, 9)
🧱 Chunk généré: (1, 10)
🧱 Chunk généré: (1, 11)
🧱 Chunk généré: (1, 12)
🧱 Chunk généré: (1, 13)
🧱 Chunk généré: (2, -5)
🧱 Chunk généré: (2, -4)
🧱 Chunk généré: (2, -3)
🧱 Chunk généré: (2, -2)
🧱 Chunk généré: (2, -1)
🧱 Chunk généré: (2, 0)
🧱 Chunk généré: (2, 1)
🧱 Chunk généré: (2, 2)
🧱 Chunk généré: (2, 3)
🧱 Chunk généré: (2, 4)
🧱 Chunk généré: (2, 5)
🧱 Chunk généré: (2, 6)
🧱 Chunk généré: (2, 7)
🧱 Chunk généré: (2, 8)
🧱 Chunk généré: (2, 9)
🧱 Chunk généré: (2, 10)
🧱 Chunk généré: (2, 11)
🧱 Chunk généré: (2, 12)
🧱 Chunk généré: (2, 13)
🧱 Chunk généré: (3, -5)
🧱 Chunk généré: (3, -4)
🧱 Chunk généré: (3, -3)
🧱 Chunk généré: (3, -2)
🧱 Chunk généré: (3, -1)
🧱 Chunk généré: (3, 0)
🧱 Chunk généré: (3, 1)
🧱 Chunk généré: (3, 2)
🧱 Chunk généré: (3, 3)
🧱 Chunk généré: (3, 4)
🧱 Chunk généré: (3, 5)
🧱 Chunk généré: (3, 6)
🧱 Chunk généré: (3, 7)
🧱 Chunk généré: (3, 8)
🧱 Chunk généré: (3, 9)
🧱 Chunk généré: (3, 10)
🧱 Chunk généré: (3, 11)
🧱 Chunk généré: (3, 12)
🧱 Chunk généré: (3, 13)
🧱 Chunk généré: (4, -6)
🧱 Chunk généré: (4, -5)
🧱 Chunk généré: (4, -4)
🧱 Chunk généré: (4, -3)
🧱 Chunk généré: (4, -2)
🧱 Chunk généré: (4, -1)
🧱 Chunk généré: (4, 0)
🧱 Chunk généré: (4, 1)
🧱 Chunk généré: (4, 2)
🧱 Chunk généré: (4, 3)
🧱 Chunk généré: (4, 5)
🧱 Chunk généré: (4, 6)
🧱 Chunk généré: (4, 7)
🧱 Chunk généré: (4, 8)
🧱 Chunk généré: (4, 9)
🧱 Chunk généré: (4, 10)
🧱 Chunk généré: (4, 11)
🧱 Chunk généré: (4, 12)
🧱 Chunk généré: (4, 13)
🧱 Chunk généré: (4, 14)
🧱 Chunk généré: (5, -5)
🧱 Chunk généré: (5, -4)
🧱 Chunk généré: (5, -3)
🧱 Chunk généré: (5, -2)
🧱 Chunk généré: (5, -1)
🧱 Chunk généré: (5, 0)
🧱 Chunk généré: (5, 1)
🧱 Chunk généré: (5, 2)
🧱 Chunk généré: (5, 3)
🧱 Chunk généré: (5, 4)
🧱 Chunk généré: (5, 5)
🧱 Chunk généré: (5, 6)
🧱 Chunk généré: (5, 7)
🧱 Chunk généré: (5, 8)
🧱 Chunk généré: (5, 9)
🧱 Chunk généré: (5, 10)
🧱 Chunk généré: (5, 11)
🧱 Chunk généré: (5, 12)
🧱 Chunk généré: (5, 13)
🧱 Chunk généré: (6, -5)
🧱 Chunk généré: (6, -4)
🧱 Chunk généré: (6, -3)
🧱 Chunk généré: (6, -2)
🧱 Chunk généré: (6, -1)
🧱 Chunk généré: (6, 0)
🧱 Chunk généré: (6, 1)
🧱 Chunk généré: (6, 2)
🧱 Chunk généré: (6, 3)
🧱 Chunk généré: (6, 4)
🧱 Chunk généré: (6, 5)
🧱 Chunk généré: (6, 6)
🧱 Chunk généré: (6, 7)
🧱 Chunk généré: (6, 8)
🧱 Chunk généré: (6, 9)
🧱 Chunk généré: (6, 10)
🧱 Chunk généré: (6, 11)
🧱 Chunk généré: (6, 12)
🧱 Chunk généré: (6, 13)
🧱 Chunk généré: (7, -5)
🧱 Chunk généré: (7, -4)
🧱 Chunk généré: (7, -3)
🧱 Chunk généré: (7, -2)
🧱 Chunk généré: (7, -1)
🧱 Chunk généré: (7, 0)
🧱 Chunk généré: (7, 1)
🧱 Chunk généré: (7, 2)
🧱 Chunk généré: (7, 3)
🧱 Chunk généré: (7, 4)
🧱 Chunk généré: (7, 5)
🧱 Chunk généré: (7, 6)
🧱 Chunk généré: (7, 7)
🧱 Chunk généré: (7, 8)
🧱 Chunk généré: (7, 9)
🧱 Chunk généré: (7, 10)
🧱 Chunk généré: (7, 11)
🧱 Chunk généré: (7, 12)
🧱 Chunk généré: (7, 13)
🧱 Chunk généré: (8, -5)
🧱 Chunk généré: (8, -4)
🧱 Chunk généré: (8, -3)
🧱 Chunk généré: (8, -2)
🧱 Chunk généré: (8, -1)
🧱 Chunk généré: (8, 0)
🧱 Chunk généré: (8, 1)
🧱 Chunk généré: (8, 2)
🧱 Chunk généré: (8, 3)
🧱 Chunk généré: (8, 4)
🧱 Chunk généré: (8, 5)
🧱 Chunk généré: (8, 6)
🧱 Chunk généré: (8, 7)
🧱 Chunk généré: (8, 8)
🧱 Chunk généré: (8, 9)
🧱 Chunk généré: (8, 10)
🧱 Chunk généré: (8, 11)
🧱 Chunk généré: (8, 12)
🧱 Chunk généré: (8, 13)
🧱 Chunk généré: (9, -4)
🧱 Chunk généré: (9, -3)
🧱 Chunk généré: (9, -2)
🧱 Chunk généré: (9, -1)
🧱 Chunk généré: (9, 0)
🧱 Chunk généré: (9, 1)
🧱 Chunk généré: (9, 2)
🧱 Chunk généré: (9, 3)
🧱 Chunk généré: (9, 4)
🧱 Chunk généré: (9, 5)
🧱 Chunk généré: (9, 6)
🧱 Chunk généré: (9, 7)
🧱 Chunk généré: (9, 8)
🧱 Chunk généré: (9, 9)
🧱 Chunk généré: (9, 10)
🧱 Chunk généré: (9, 11)
🧱 Chunk généré: (9, 12)
🧱 Chunk généré: (10, -4)
🧱 Chunk généré: (10, -3)
🧱 Chunk généré: (10, -2)
🧱 Chunk généré: (10, -1)
🧱 Chunk généré: (10, 0)
🧱 Chunk généré: (10, 1)
🧱 Chunk généré: (10, 2)
🧱 Chunk généré: (10, 3)
🧱 Chunk généré: (10, 4)
🧱 Chunk généré: (10, 5)
🧱 Chunk généré: (10, 6)
🧱 Chunk généré: (10, 7)
🧱 Chunk généré: (10, 8)
🧱 Chunk généré: (10, 9)
🧱 Chunk généré: (10, 10)
🧱 Chunk généré: (10, 11)
🧱 Chunk généré: (10, 12)
🧱 Chunk généré: (11, -3)
🧱 Chunk généré: (11, -2)
🧱 Chunk généré: (11, -1)
🧱 Chunk généré: (11, 0)
🧱 Chunk généré: (11, 1)
🧱 Chunk généré: (11, 2)
🧱 Chunk généré: (11, 3)
🧱 Chunk généré: (11, 4)
🧱 Chunk généré: (11, 5)
🧱 Chunk généré: (11, 6)
🧱 Chunk généré: (11, 7)
🧱 Chunk généré: (11, 8)
🧱 Chunk généré: (11, 9)
🧱 Chunk généré: (11, 10)
🧱 Chunk généré: (11, 11)
🧱 Chunk généré: (12, -2)
🧱 Chunk généré: (12, -1)
🧱 Chunk généré: (12, 0)
🧱 Chunk généré: (12, 1)
🧱 Chunk généré: (12, 2)
🧱 Chunk généré: (12, 3)
🧱 Chunk généré: (12, 4)
🧱 Chunk généré: (12, 5)
🧱 Chunk généré: (12, 6)
🧱 Chunk généré: (12, 7)
🧱 Chunk généré: (12, 8)
🧱 Chunk généré: (12, 9)
🧱 Chunk généré: (12, 10)
🧱 Chunk généré: (13, 0)
🧱 Chunk généré: (13, 1)
🧱 Chunk généré: (13, 2)
🧱 Chunk généré: (13, 3)
🧱 Chunk généré: (13, 4)
🧱 Chunk généré: (13, 5)
🧱 Chunk généré: (13, 6)
🧱 Chunk généré: (13, 7)
🧱 Chunk généré: (13, 8)
🧱 Chunk généré: (14, 4)
👤 Nouveau joueur connecté: qdw8t54fgmdhiobuf
👤 Player qdw8t54fgmdhiobuf a rejoint le jeu
👤 Joueur qdw8t54fgmdhiobuf créé à la position (0, 100, 0)
🧱 Chunk généré: (-11, -2)
🧱 Chunk généré: (-10, -6)
🧱 Chunk généré: (-10, -5)
🧱 Chunk généré: (-10, -4)
🧱 Chunk généré: (-10, -3)
🧱 Chunk généré: (-10, -2)
🧱 Chunk généré: (-10, -1)
🧱 Chunk généré: (-10, 0)
🧱 Chunk généré: (-10, 1)
🧱 Chunk généré: (-10, 2)
🧱 Chunk généré: (-9, -8)
🧱 Chunk généré: (-9, -7)
🧱 Chunk généré: (-9, -6)
🧱 Chunk généré: (-9, -5)
🧱 Chunk généré: (-9, -4)
🧱 Chunk généré: (-9, -3)
🧱 Chunk généré: (-9, -2)
🧱 Chunk généré: (-9, -1)
🧱 Chunk généré: (-9, 0)
🧱 Chunk généré: (-9, 1)
🧱 Chunk généré: (-9, 2)
🧱 Chunk généré: (-9, 3)
🧱 Chunk généré: (-9, 4)
🧱 Chunk généré: (-8, -9)
🧱 Chunk généré: (-8, -8)
🧱 Chunk généré: (-8, -7)
🧱 Chunk généré: (-8, -6)
🧱 Chunk généré: (-8, -5)
🧱 Chunk généré: (-8, -4)
🧱 Chunk généré: (-8, -3)
🧱 Chunk généré: (-8, -2)
🧱 Chunk généré: (-8, -1)
🧱 Chunk généré: (-8, 0)
🧱 Chunk généré: (-8, 1)
🧱 Chunk généré: (-8, 2)
🧱 Chunk généré: (-8, 3)
🧱 Chunk généré: (-8, 4)
🧱 Chunk généré: (-8, 5)
🧱 Chunk généré: (-7, -10)
🧱 Chunk généré: (-7, -9)
🧱 Chunk généré: (-7, -8)
🧱 Chunk généré: (-7, -7)
🧱 Chunk généré: (-7, -6)
🧱 Chunk généré: (-7, -5)
🧱 Chunk généré: (-7, -4)
🧱 Chunk généré: (-7, -3)
🧱 Chunk généré: (-7, -2)
🧱 Chunk généré: (-7, -1)
🧱 Chunk généré: (-7, 0)
🧱 Chunk généré: (-7, 1)
🧱 Chunk généré: (-7, 2)
🧱 Chunk généré: (-7, 3)
🧱 Chunk généré: (-7, 4)
🧱 Chunk généré: (-7, 5)
🧱 Chunk généré: (-7, 6)
🧱 Chunk généré: (-6, -10)
🧱 Chunk généré: (-6, -9)
🧱 Chunk généré: (-6, -8)
🧱 Chunk généré: (-6, -7)
🧱 Chunk généré: (-6, -6)
🧱 Chunk généré: (-6, -5)
🧱 Chunk généré: (-6, -4)
🧱 Chunk généré: (-6, -3)
🧱 Chunk généré: (-6, -2)
🧱 Chunk généré: (-6, -1)
🧱 Chunk généré: (-6, 0)
🧱 Chunk généré: (-6, 1)
🧱 Chunk généré: (-6, 2)
🧱 Chunk généré: (-6, 3)
🧱 Chunk généré: (-6, 5)
🧱 Chunk généré: (-6, 6)
🧱 Chunk généré: (-5, -11)
🧱 Chunk généré: (-5, -10)
🧱 Chunk généré: (-5, -9)
🧱 Chunk généré: (-5, -8)
🧱 Chunk généré: (-5, -7)
🧱 Chunk généré: (-5, -6)
🧱 Chunk généré: (-5, -5)
🧱 Chunk généré: (-5, -4)
🧱 Chunk généré: (-5, -3)
🧱 Chunk généré: (-5, -2)
🧱 Chunk généré: (-5, -1)
🧱 Chunk généré: (-4, -11)
🧱 Chunk généré: (-4, -10)
🧱 Chunk généré: (-4, -9)
🧱 Chunk généré: (-4, -8)
🧱 Chunk généré: (-4, -7)
🧱 Chunk généré: (-4, -6)
🧱 Chunk généré: (-4, -5)
🧱 Chunk généré: (-4, -4)
🧱 Chunk généré: (-4, -3)
🧱 Chunk généré: (-3, -11)
🧱 Chunk généré: (-3, -10)
🧱 Chunk généré: (-3, -9)
🧱 Chunk généré: (-3, -8)
🧱 Chunk généré: (-3, -7)
🧱 Chunk généré: (-3, -6)
🧱 Chunk généré: (-3, -5)
🧱 Chunk généré: (-3, -4)
🧱 Chunk généré: (-2, -11)
🧱 Chunk généré: (-2, -10)
🧱 Chunk généré: (-2, -9)
🧱 Chunk généré: (-2, -8)
🧱 Chunk généré: (-2, -7)
🧱 Chunk généré: (-2, -6)
🧱 Chunk généré: (-2, -5)
🧱 Chunk généré: (-1, -12)
🧱 Chunk généré: (-1, -11)
🧱 Chunk généré: (-1, -10)
🧱 Chunk généré: (-1, -9)
🧱 Chunk généré: (-1, -8)
🧱 Chunk généré: (-1, -7)
🧱 Chunk généré: (-1, -6)
🧱 Chunk généré: (-1, -5)
🧱 Chunk généré: (0, -11)
🧱 Chunk généré: (0, -10)
🧱 Chunk généré: (0, -9)
🧱 Chunk généré: (0, -8)
🧱 Chunk généré: (0, -7)
🧱 Chunk généré: (0, -6)
🧱 Chunk généré: (1, -11)
🧱 Chunk généré: (1, -10)
🧱 Chunk généré: (1, -9)
🧱 Chunk généré: (1, -8)
🧱 Chunk généré: (1, -7)
🧱 Chunk généré: (1, -6)
🧱 Chunk généré: (2, -11)
🧱 Chunk généré: (2, -10)
🧱 Chunk généré: (2, -9)
🧱 Chunk généré: (2, -8)
🧱 Chunk généré: (2, -7)
🧱 Chunk généré: (2, -6)
🧱 Chunk généré: (3, -11)
🧱 Chunk généré: (3, -10)
🧱 Chunk généré: (3, -9)
🧱 Chunk généré: (3, -8)
🧱 Chunk généré: (3, -7)
🧱 Chunk généré: (3, -6)
🧱 Chunk généré: (4, -10)
🧱 Chunk généré: (4, -9)
🧱 Chunk généré: (4, -8)
🧱 Chunk généré: (4, -7)
🧱 Chunk généré: (5, -10)
🧱 Chunk généré: (5, -9)
🧱 Chunk généré: (5, -8)
🧱 Chunk généré: (5, -7)
🧱 Chunk généré: (5, -6)
🧱 Chunk généré: (6, -9)
🧱 Chunk généré: (6, -8)
🧱 Chunk généré: (6, -7)
🧱 Chunk généré: (6, -6)
🧱 Chunk généré: (7, -8)
🧱 Chunk généré: (7, -7)
🧱 Chunk généré: (7, -6)
🧱 Chunk généré: (8, -6)
👋 Joueur déconnecté: 06opizp40mdhio6bj
👋 Player 06opizp40mdhio6bj a quitté le jeu
🗑️ 111 chunks déchargés
📊 Stats: 1 joueurs, 1 TPS, 3693.00ms/tick
👤 Nouveau joueur connecté: iwx5m0t0gmdhioezg
👤 Player iwx5m0t0gmdhioezg a rejoint le jeu
👤 Joueur iwx5m0t0gmdhioezg créé à la position (0, 100, 0)
🧱 Chunk généré: (6, -11)
🧱 Chunk généré: (6, -10)
🧱 Chunk généré: (6, 8)
🧱 Chunk généré: (6, 9)
🧱 Chunk généré: (7, -10)
🧱 Chunk généré: (7, -9)
🧱 Chunk généré: (7, 7)
🧱 Chunk généré: (7, 8)
🧱 Chunk généré: (8, -10)
🧱 Chunk généré: (8, -9)
🧱 Chunk généré: (8, -8)
🧱 Chunk généré: (8, -7)
🧱 Chunk généré: (8, 6)
🧱 Chunk généré: (8, 7)
🧱 Chunk généré: (8, 8)
🧱 Chunk généré: (9, -10)
🧱 Chunk généré: (9, -9)
🧱 Chunk généré: (9, -8)
🧱 Chunk généré: (9, -7)
🧱 Chunk généré: (9, -6)
🧱 Chunk généré: (9, -5)
🧱 Chunk généré: (9, 5)
🧱 Chunk généré: (9, 6)
🧱 Chunk généré: (9, 7)
🧱 Chunk généré: (9, 8)
🧱 Chunk généré: (10, -10)
🧱 Chunk généré: (10, -9)
🧱 Chunk généré: (10, -8)
🧱 Chunk généré: (10, -7)
🧱 Chunk généré: (10, -6)
🧱 Chunk généré: (10, -5)
🧱 Chunk généré: (10, 3)
🧱 Chunk généré: (10, 4)
🧱 Chunk généré: (10, 5)
🧱 Chunk généré: (10, 6)
🧱 Chunk généré: (10, 7)
🧱 Chunk généré: (10, 8)
🧱 Chunk généré: (11, -9)
🧱 Chunk généré: (11, -8)
🧱 Chunk généré: (11, -7)
🧱 Chunk généré: (11, -6)
🧱 Chunk généré: (11, -5)
🧱 Chunk généré: (11, -4)
🧱 Chunk généré: (11, -3)
🧱 Chunk généré: (11, -1)
🧱 Chunk généré: (11, 0)
🧱 Chunk généré: (11, 1)
🧱 Chunk généré: (11, 2)
🧱 Chunk généré: (11, 3)
🧱 Chunk généré: (11, 4)
🧱 Chunk généré: (11, 5)
🧱 Chunk généré: (11, 6)
🧱 Chunk généré: (11, 7)
🧱 Chunk généré: (12, -9)
🧱 Chunk généré: (12, -8)
🧱 Chunk généré: (12, -7)
🧱 Chunk généré: (12, -6)
🧱 Chunk généré: (12, -5)
🧱 Chunk généré: (12, -4)
🧱 Chunk généré: (12, -3)
🧱 Chunk généré: (12, -2)
🧱 Chunk généré: (12, -1)
🧱 Chunk généré: (12, 0)
🧱 Chunk généré: (12, 1)
🧱 Chunk généré: (12, 2)
🧱 Chunk généré: (12, 3)
🧱 Chunk généré: (12, 4)
🧱 Chunk généré: (12, 5)
🧱 Chunk généré: (12, 6)
🧱 Chunk généré: (12, 7)
🧱 Chunk généré: (13, -8)
🧱 Chunk généré: (13, -7)
🧱 Chunk généré: (13, -6)
🧱 Chunk généré: (13, -5)
🧱 Chunk généré: (13, -4)
🧱 Chunk généré: (13, -3)
🧱 Chunk généré: (13, -2)
🧱 Chunk généré: (13, -1)
🧱 Chunk généré: (13, 0)
🧱 Chunk généré: (13, 1)
🧱 Chunk généré: (13, 2)
🧱 Chunk généré: (13, 3)
🧱 Chunk généré: (13, 4)
🧱 Chunk généré: (13, 5)
🧱 Chunk généré: (13, 6)
🧱 Chunk généré: (14, -7)
🧱 Chunk généré: (14, -6)
🧱 Chunk généré: (14, -5)
🧱 Chunk généré: (14, -4)
🧱 Chunk généré: (14, -3)
🧱 Chunk généré: (14, -2)
🧱 Chunk généré: (14, -1)
🧱 Chunk généré: (14, 0)
🧱 Chunk généré: (14, 1)
🧱 Chunk généré: (14, 2)
🧱 Chunk généré: (14, 3)
🧱 Chunk généré: (14, 4)
🧱 Chunk généré: (14, 5)
🧱 Chunk généré: (15, -5)
🧱 Chunk généré: (15, -4)
🧱 Chunk généré: (15, -3)
🧱 Chunk généré: (15, -2)
🧱 Chunk généré: (15, -1)
🧱 Chunk généré: (15, 0)
🧱 Chunk généré: (15, 1)
🧱 Chunk généré: (15, 2)
🧱 Chunk généré: (15, 3)
🧱 Chunk généré: (16, -1)
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 63.00ms/tick
👋 Joueur déconnecté: iwx5m0t0gmdhioezg
👋 Player iwx5m0t0gmdhioezg a quitté le jeu
👤 Nouveau joueur connecté: 08ybpjkfymdhioolm
👤 Player 08ybpjkfymdhioolm a rejoint le jeu
👤 Joueur 08ybpjkfymdhioolm créé à la position (0, 100, 0)
🧱 Chunk généré: (-11, -3)
🧱 Chunk généré: (-10, -7)
🧱 Chunk généré: (-9, -9)
🧱 Chunk généré: (-8, -10)
🧱 Chunk généré: (-7, -11)
🧱 Chunk généré: (-6, -11)
🧱 Chunk généré: (-5, -12)
🧱 Chunk généré: (-4, -12)
🧱 Chunk généré: (-3, -12)
🧱 Chunk généré: (-2, -12)
🧱 Chunk généré: (-1, -13)
🧱 Chunk généré: (0, -12)
🧱 Chunk généré: (1, -12)
🧱 Chunk généré: (2, -12)
🧱 Chunk généré: (3, -12)
🧱 Chunk généré: (4, -11)
🧱 Chunk généré: (5, -11)
🗑️ 91 chunks déchargés
📊 Stats: 2 joueurs, 16 TPS, 62.00ms/tick
👋 Joueur déconnecté: 08ybpjkfymdhioolm
👋 Player 08ybpjkfymdhioolm a quitté le jeu
👤 Nouveau joueur connecté: yhvwahhg1mdhioukt
👤 Player yhvwahhg1mdhioukt a rejoint le jeu
👤 Joueur yhvwahhg1mdhioukt créé à la position (0, 100, 0)
🧱 Chunk généré: (-4, -13)
🧱 Chunk généré: (-3, -14)
🧱 Chunk généré: (-3, -13)
🧱 Chunk généré: (-2, -14)
🧱 Chunk généré: (-2, -13)
🧱 Chunk généré: (-1, -15)
🧱 Chunk généré: (-1, -14)
🧱 Chunk généré: (0, -15)
🧱 Chunk généré: (0, -14)
🧱 Chunk généré: (0, -13)
🧱 Chunk généré: (1, -15)
🧱 Chunk généré: (1, -14)
🧱 Chunk généré: (1, -13)
🧱 Chunk généré: (2, -15)
🧱 Chunk généré: (2, -14)
🧱 Chunk généré: (2, -13)
🧱 Chunk généré: (3, -16)
🧱 Chunk généré: (3, -15)
🧱 Chunk généré: (3, -14)
🧱 Chunk généré: (3, -13)
🧱 Chunk généré: (4, -15)
🧱 Chunk généré: (4, -14)
🧱 Chunk généré: (4, -13)
🧱 Chunk généré: (4, -12)
🧱 Chunk généré: (5, -15)
🧱 Chunk généré: (5, -14)
🧱 Chunk généré: (5, -13)
🧱 Chunk généré: (5, -12)
🧱 Chunk généré: (6, -15)
🧱 Chunk généré: (6, -14)
🧱 Chunk généré: (6, -13)
🧱 Chunk généré: (6, -12)
🧱 Chunk généré: (7, -15)
🧱 Chunk généré: (7, -14)
🧱 Chunk généré: (7, -13)
🧱 Chunk généré: (7, -12)
🧱 Chunk généré: (7, -11)
🧱 Chunk généré: (8, -14)
🧱 Chunk généré: (8, -13)
🧱 Chunk généré: (8, -12)
🧱 Chunk généré: (8, -11)
🧱 Chunk généré: (9, -14)
🧱 Chunk généré: (9, -13)
🧱 Chunk généré: (9, -12)
🧱 Chunk généré: (9, -11)
🧱 Chunk généré: (9, -10)
🧱 Chunk généré: (10, -13)
🧱 Chunk généré: (10, -12)
🧱 Chunk généré: (10, -11)
🧱 Chunk généré: (10, -10)
🧱 Chunk généré: (10, -9)
🧱 Chunk généré: (10, -8)
🧱 Chunk généré: (11, -12)
🧱 Chunk généré: (11, -11)
🧱 Chunk généré: (11, -10)
🧱 Chunk généré: (11, -9)
🧱 Chunk généré: (11, -8)
🧱 Chunk généré: (11, -7)
🧱 Chunk généré: (11, -6)
🧱 Chunk généré: (11, -5)
🧱 Chunk généré: (11, -4)
🧱 Chunk généré: (11, -1)
🧱 Chunk généré: (11, 0)
🧱 Chunk généré: (12, -10)
🧱 Chunk généré: (12, -9)
🧱 Chunk généré: (12, -8)
🧱 Chunk généré: (12, -7)
🧱 Chunk généré: (12, -6)
🧱 Chunk généré: (12, -5)
🧱 Chunk généré: (12, -4)
🧱 Chunk généré: (12, -3)
🧱 Chunk généré: (12, -2)
🧱 Chunk généré: (13, -6)
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 17 TPS, 62.00ms/tick
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 2 joueurs, 16 TPS, 61.00ms/tick
⚠️ Type de message inconnu de yhvwahhg1mdhioukt: client:ping
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 17 TPS, 64.00ms/tick
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 2 joueurs, 16 TPS, 62.00ms/tick
⚠️ Type de message inconnu de yhvwahhg1mdhioukt: client:ping
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 63.00ms/tick
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 62.00ms/tick
📊 Stats: 2 joueurs, 16 TPS, 63.00ms/tick
⚠️ Type de message inconnu de yhvwahhg1mdhioukt: client:ping
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 62.00ms/tick
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 63.00ms/tick
📊 Stats: 2 joueurs, 16 TPS, 64.00ms/tick
⚠️ Type de message inconnu de yhvwahhg1mdhioukt: client:ping
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 64.00ms/tick
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 62.00ms/tick
📊 Stats: 2 joueurs, 16 TPS, 63.00ms/tick
⚠️ Type de message inconnu de yhvwahhg1mdhioukt: client:ping
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 63.00ms/tick
⚠️ Type de message inconnu de qdw8t54fgmdhiobuf: client:ping
📊 Stats: 2 joueurs, 16 TPS, 62.00ms/tick
