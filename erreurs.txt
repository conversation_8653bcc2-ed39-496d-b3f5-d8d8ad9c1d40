[2915] [2025-07-24T00:33:23.076Z] [CHUNK] Chunk ajou<PERSON> à la scène
{
  "position": {
    "x": -5,
    "z": -6
  },
  "distance": "6.40",
  "totalRendered": 149,
  "visible": true,
  "timestamp": 5468
}
---
[2916] [2025-07-24T00:33:23.076Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.042,
  "position": {
    "x": -1.38,
    "y": 84.1,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -6.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2917] [2025-07-24T00:33:23.077Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -6.33,
    "z": 0
  },
  "delta": 0.042,
  "calculatedMovement": {
    "x": 0,
    "y": -0.264,
    "z": 0
  }
}
---
[2918] [2025-07-24T00:33:23.077Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[2919] [2025-07-24T00:33:23.077Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.264,
  "yBefore": 84.1,
  "yAfter": 83.83,
  "actualYMovement": -0.264
}
---
[2920] [2025-07-24T00:33:23.077Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 56
}
---
[2921] [2025-07-24T00:33:23.077Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 83.83,
    "z": -3.35
  },
  "feetY": 82.13,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[2922] [2025-07-24T00:33:23.077Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 82.13,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 14.133,
  "targetGroundY": 69.7,
  "velocityY": -6.33,
  "currentOnGround": false
}
---
[2923] [2025-07-24T00:33:23.077Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -6.33,
  "distanceToGround": 14.133,
  "absDistanceToGround": 14.133,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[2924] [2025-07-24T00:33:23.077Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 14.133,
  "velocityY": -6.83,
  "fallTime": 569,
  "onGround": false
}
---
[2925] [2025-07-24T00:33:23.078Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 83.83,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -6.83,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2926] [2025-07-24T00:33:23.088Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 83.83,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -6.83,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2927] [2025-07-24T00:33:23.089Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -6.83,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.095,
    "z": 0
  }
}
---
[2928] [2025-07-24T00:33:23.089Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[2929] [2025-07-24T00:33:23.089Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.095,
  "yBefore": 83.83,
  "yAfter": 83.74,
  "actualYMovement": -0.095
}
---
[2930] [2025-07-24T00:33:23.089Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 68
}
---
[2931] [2025-07-24T00:33:23.089Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 83.74,
    "z": -3.35
  },
  "feetY": 82.04,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[2932] [2025-07-24T00:33:23.089Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 82.04,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 14.038,
  "targetGroundY": 69.7,
  "velocityY": -6.83,
  "currentOnGround": false
}
---
[2933] [2025-07-24T00:33:23.089Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -6.83,
  "distanceToGround": 14.038,
  "absDistanceToGround": 14.038,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[2934] [2025-07-24T00:33:23.089Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 14.038,
  "velocityY": -7,
  "fallTime": 583,
  "onGround": false
}
---
[2935] [2025-07-24T00:33:23.089Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 83.74,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -7,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2936] [2025-07-24T00:33:23.112Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.028,
  "position": {
    "x": -1.38,
    "y": 83.74,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -7,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2937] [2025-07-24T00:33:23.113Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -7,
    "z": 0
  },
  "delta": 0.028,
  "calculatedMovement": {
    "x": 0,
    "y": -0.194,
    "z": 0
  }
}
---
[2938] [2025-07-24T00:33:23.113Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[2939] [2025-07-24T00:33:23.113Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.194,
  "yBefore": 83.74,
  "yAfter": 83.54,
  "actualYMovement": -0.194
}
---
[2940] [2025-07-24T00:33:23.113Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 92
}
---
[2941] [2025-07-24T00:33:23.113Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 83.54,
    "z": -3.35
  },
  "feetY": 81.84,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[2942] [2025-07-24T00:33:23.113Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 81.84,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 13.843,
  "targetGroundY": 69.7,
  "velocityY": -7,
  "currentOnGround": false
}
---
[2943] [2025-07-24T00:33:23.113Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -7,
  "distanceToGround": 13.843,
  "absDistanceToGround": 13.843,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[2944] [2025-07-24T00:33:23.113Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 13.843,
  "velocityY": -7.33,
  "fallTime": 611,
  "onGround": false
}
---
[2945] [2025-07-24T00:33:23.113Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 83.54,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -7.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2946] [2025-07-24T00:33:23.114Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 57,
  "timestamp": 5506
}
---
[2947] [2025-07-24T00:33:23.114Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 56,
  "timestamp": 5506
}
---
[2948] [2025-07-24T00:33:23.123Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 83.54,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -7.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2949] [2025-07-24T00:33:23.123Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -7.33,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.051,
    "z": 0
  }
}
---
[2950] [2025-07-24T00:33:23.123Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[2951] [2025-07-24T00:33:23.124Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.051,
  "yBefore": 83.54,
  "yAfter": 83.49,
  "actualYMovement": -0.051
}
---
[2952] [2025-07-24T00:33:23.124Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[2953] [2025-07-24T00:33:23.124Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[2954] [2025-07-24T00:33:23.124Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[2955] [2025-07-24T00:33:23.124Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 83.49,
    "z": -3.35
  },
  "feetY": 81.79,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[2956] [2025-07-24T00:33:23.124Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 81.79,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 13.793,
  "targetGroundY": 69.7,
  "velocityY": -7.33,
  "currentOnGround": false
}
---
[2957] [2025-07-24T00:33:23.124Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -7.33,
  "distanceToGround": 13.793,
  "absDistanceToGround": 13.793,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[2958] [2025-07-24T00:33:23.124Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 13.793,
  "velocityY": -7.42,
  "fallTime": 618,
  "onGround": false
}
---
[2959] [2025-07-24T00:33:23.125Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 83.49,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -7.42,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2960] [2025-07-24T00:33:23.137Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 5529
}
---
[2961] [2025-07-24T00:33:23.150Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -1,
    "z": -8
  },
  "distance": "7.00",
  "totalRendered": 150,
  "visible": true,
  "timestamp": 5541
}
---
[2962] [2025-07-24T00:33:23.151Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 5543
}
---
[2963] [2025-07-24T00:33:23.164Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -1,
    "z": 6
  },
  "distance": "7.00",
  "totalRendered": 151,
  "visible": true,
  "timestamp": 5555
}
---
[2964] [2025-07-24T00:33:23.165Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.042,
  "position": {
    "x": -1.38,
    "y": 83.49,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -7.42,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2965] [2025-07-24T00:33:23.166Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -7.42,
    "z": 0
  },
  "delta": 0.042,
  "calculatedMovement": {
    "x": 0,
    "y": -0.309,
    "z": 0
  }
}
---
[2966] [2025-07-24T00:33:23.166Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[2967] [2025-07-24T00:33:23.166Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.309,
  "yBefore": 83.49,
  "yAfter": 83.18,
  "actualYMovement": -0.309
}
---
[2968] [2025-07-24T00:33:23.166Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 42
}
---
[2969] [2025-07-24T00:33:23.166Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 83.18,
    "z": -3.35
  },
  "feetY": 81.48,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[2970] [2025-07-24T00:33:23.166Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 81.48,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 13.484,
  "targetGroundY": 69.7,
  "velocityY": -7.42,
  "currentOnGround": false
}
---
[2971] [2025-07-24T00:33:23.166Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -7.42,
  "distanceToGround": 13.484,
  "absDistanceToGround": 13.484,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[2972] [2025-07-24T00:33:23.166Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 13.484,
  "velocityY": -7.92,
  "fallTime": 660,
  "onGround": false
}
---
[2973] [2025-07-24T00:33:23.166Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 83.18,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -7.92,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2974] [2025-07-24T00:33:23.180Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 83.18,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -7.92,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2975] [2025-07-24T00:33:23.180Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -7.92,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.11,
    "z": 0
  }
}
---
[2976] [2025-07-24T00:33:23.180Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[2977] [2025-07-24T00:33:23.180Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.11,
  "yBefore": 83.18,
  "yAfter": 83.07,
  "actualYMovement": -0.11
}
---
[2978] [2025-07-24T00:33:23.181Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 57
}
---
[2979] [2025-07-24T00:33:23.181Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 83.07,
    "z": -3.35
  },
  "feetY": 81.37,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[2980] [2025-07-24T00:33:23.181Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 81.37,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 13.374,
  "targetGroundY": 69.7,
  "velocityY": -7.92,
  "currentOnGround": false
}
---
[2981] [2025-07-24T00:33:23.181Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -7.92,
  "distanceToGround": 13.374,
  "absDistanceToGround": 13.374,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[2982] [2025-07-24T00:33:23.181Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 13.374,
  "velocityY": -8.08,
  "fallTime": 674,
  "onGround": false
}
---
[2983] [2025-07-24T00:33:23.181Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 83.07,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -8.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2984] [2025-07-24T00:33:23.181Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 55,
  "timestamp": 5573
}
---
[2985] [2025-07-24T00:33:23.181Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 54,
  "timestamp": 5573
}
---
[2986] [2025-07-24T00:33:23.196Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 83.07,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -8.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2987] [2025-07-24T00:33:23.196Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -8.08,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.168,
    "z": 0
  }
}
---
[2988] [2025-07-24T00:33:23.196Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[2989] [2025-07-24T00:33:23.197Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.168,
  "yBefore": 83.07,
  "yAfter": 82.91,
  "actualYMovement": -0.168
}
---
[2990] [2025-07-24T00:33:23.197Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 73
}
---
[2991] [2025-07-24T00:33:23.197Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 82.91,
    "z": -3.35
  },
  "feetY": 81.21,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[2992] [2025-07-24T00:33:23.197Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 81.21,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 13.205,
  "targetGroundY": 69.7,
  "velocityY": -8.08,
  "currentOnGround": false
}
---
[2993] [2025-07-24T00:33:23.197Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -8.08,
  "distanceToGround": 13.205,
  "absDistanceToGround": 13.205,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[2994] [2025-07-24T00:33:23.197Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 13.205,
  "velocityY": -8.33,
  "fallTime": 694,
  "onGround": false
}
---
[2995] [2025-07-24T00:33:23.197Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 82.91,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -8.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[2996] [2025-07-24T00:33:23.206Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 5598
}
---
[2997] [2025-07-24T00:33:23.206Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": 6
  },
  "distance": "7.28",
  "reason": "Hors distance de rendu",
  "timestamp": 5598
}
---
[2998] [2025-07-24T00:33:23.208Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 5600
}
---
[2999] [2025-07-24T00:33:23.208Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": -8
  },
  "distance": "7.28",
  "reason": "Hors distance de rendu",
  "timestamp": 5600
}
---
[3000] [2025-07-24T00:33:23.210Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 82.91,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -8.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3001] [2025-07-24T00:33:23.210Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -8.33,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.116,
    "z": 0
  }
}
---
[3002] [2025-07-24T00:33:23.210Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3003] [2025-07-24T00:33:23.211Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.116,
  "yBefore": 82.91,
  "yAfter": 82.79,
  "actualYMovement": -0.116
}
---
[3004] [2025-07-24T00:33:23.211Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 87
}
---
[3005] [2025-07-24T00:33:23.211Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 82.79,
    "z": -3.35
  },
  "feetY": 81.09,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3006] [2025-07-24T00:33:23.211Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 81.09,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 13.089,
  "targetGroundY": 69.7,
  "velocityY": -8.33,
  "currentOnGround": false
}
---
[3007] [2025-07-24T00:33:23.211Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -8.33,
  "distanceToGround": 13.089,
  "absDistanceToGround": 13.089,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3008] [2025-07-24T00:33:23.211Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 13.089,
  "velocityY": -8.5,
  "fallTime": 708,
  "onGround": false
}
---
[3009] [2025-07-24T00:33:23.211Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 82.79,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -8.5,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3010] [2025-07-24T00:33:23.220Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 82.79,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -8.5,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3011] [2025-07-24T00:33:23.221Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -8.5,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.059,
    "z": 0
  }
}
---
[3012] [2025-07-24T00:33:23.221Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3013] [2025-07-24T00:33:23.221Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.059,
  "yBefore": 82.79,
  "yAfter": 82.73,
  "actualYMovement": -0.059
}
---
[3014] [2025-07-24T00:33:23.221Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 97
}
---
[3015] [2025-07-24T00:33:23.221Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 82.73,
    "z": -3.35
  },
  "feetY": 81.03,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3016] [2025-07-24T00:33:23.221Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 81.03,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 13.03,
  "targetGroundY": 69.7,
  "velocityY": -8.5,
  "currentOnGround": false
}
---
[3017] [2025-07-24T00:33:23.221Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -8.5,
  "distanceToGround": 13.03,
  "absDistanceToGround": 13.03,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3018] [2025-07-24T00:33:23.221Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 13.03,
  "velocityY": -8.58,
  "fallTime": 715,
  "onGround": false
}
---
[3019] [2025-07-24T00:33:23.221Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 82.73,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -8.58,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3020] [2025-07-24T00:33:23.222Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 53,
  "timestamp": 5613
}
---
[3021] [2025-07-24T00:33:23.222Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 52,
  "timestamp": 5613
}
---
[3022] [2025-07-24T00:33:23.230Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 82.73,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -8.58,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3023] [2025-07-24T00:33:23.231Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -8.58,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.06,
    "z": 0
  }
}
---
[3024] [2025-07-24T00:33:23.231Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3025] [2025-07-24T00:33:23.231Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.06,
  "yBefore": 82.73,
  "yAfter": 82.67,
  "actualYMovement": -0.06
}
---
[3026] [2025-07-24T00:33:23.231Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3027] [2025-07-24T00:33:23.231Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3028] [2025-07-24T00:33:23.232Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3029] [2025-07-24T00:33:23.232Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 82.67,
    "z": -3.35
  },
  "feetY": 80.97,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3030] [2025-07-24T00:33:23.232Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 80.97,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 12.971,
  "targetGroundY": 69.7,
  "velocityY": -8.58,
  "currentOnGround": false
}
---
[3031] [2025-07-24T00:33:23.232Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -8.58,
  "distanceToGround": 12.971,
  "absDistanceToGround": 12.971,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3032] [2025-07-24T00:33:23.233Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 12.971,
  "velocityY": -8.67,
  "fallTime": 722,
  "onGround": false
}
---
[3033] [2025-07-24T00:33:23.233Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 82.67,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -8.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3034] [2025-07-24T00:33:23.248Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 5639
}
---
[3035] [2025-07-24T00:33:23.249Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -6
  },
  "distance": "7.81",
  "reason": "Hors distance de rendu",
  "timestamp": 5640
}
---
[3036] [2025-07-24T00:33:23.251Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 5642
}
---
[3037] [2025-07-24T00:33:23.251Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": 4
  },
  "distance": "7.81",
  "reason": "Hors distance de rendu",
  "timestamp": 5643
}
---
[3038] [2025-07-24T00:33:23.251Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.028,
  "position": {
    "x": -1.38,
    "y": 82.67,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -8.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3039] [2025-07-24T00:33:23.251Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -8.67,
    "z": 0
  },
  "delta": 0.028,
  "calculatedMovement": {
    "x": 0,
    "y": -0.241,
    "z": 0
  }
}
---
[3040] [2025-07-24T00:33:23.252Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3041] [2025-07-24T00:33:23.252Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.241,
  "yBefore": 82.67,
  "yAfter": 82.43,
  "actualYMovement": -0.241
}
---
[3042] [2025-07-24T00:33:23.252Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 21
}
---
[3043] [2025-07-24T00:33:23.252Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 82.43,
    "z": -3.35
  },
  "feetY": 80.73,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3044] [2025-07-24T00:33:23.252Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 80.73,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 12.73,
  "targetGroundY": 69.7,
  "velocityY": -8.67,
  "currentOnGround": false
}
---
[3045] [2025-07-24T00:33:23.252Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -8.67,
  "distanceToGround": 12.73,
  "absDistanceToGround": 12.73,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3046] [2025-07-24T00:33:23.252Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 12.73,
  "velocityY": -9,
  "fallTime": 750,
  "onGround": false
}
---
[3047] [2025-07-24T00:33:23.252Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 82.43,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -9,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3048] [2025-07-24T00:33:23.265Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 82.43,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -9,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3049] [2025-07-24T00:33:23.266Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -9,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.125,
    "z": 0
  }
}
---
[3050] [2025-07-24T00:33:23.266Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3051] [2025-07-24T00:33:23.267Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.125,
  "yBefore": 82.43,
  "yAfter": 82.31,
  "actualYMovement": -0.125
}
---
[3052] [2025-07-24T00:33:23.267Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 36
}
---
[3053] [2025-07-24T00:33:23.267Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 82.31,
    "z": -3.35
  },
  "feetY": 80.61,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3054] [2025-07-24T00:33:23.267Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 80.61,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 12.605,
  "targetGroundY": 69.7,
  "velocityY": -9,
  "currentOnGround": false
}
---
[3055] [2025-07-24T00:33:23.267Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -9,
  "distanceToGround": 12.605,
  "absDistanceToGround": 12.605,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3056] [2025-07-24T00:33:23.267Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 12.605,
  "velocityY": -9.17,
  "fallTime": 764,
  "onGround": false
}
---
[3057] [2025-07-24T00:33:23.267Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 82.31,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -9.17,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3058] [2025-07-24T00:33:23.267Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -2
  },
  "priority": 2,
  "queueRemaining": 51,
  "timestamp": 5659
}
---
[3059] [2025-07-24T00:33:23.268Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": 0
  },
  "priority": 2,
  "queueRemaining": 50,
  "timestamp": 5659
}
---
[3060] [2025-07-24T00:33:23.268Z] [DEBUG] État de la scène
{
  "sceneObjects": 153,
  "cameraPosition": {
    "x": -1.38,
    "y": 82.31,
    "z": -3.35
  },
  "fps": 72,
  "frameCount": 240
}
---
[3061] [2025-07-24T00:33:23.282Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": 0
  },
  "blocksReceived": 32768,
  "timestamp": 5674
}
---
[3062] [2025-07-24T00:33:23.283Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": 0
  },
  "distance": "8.06",
  "reason": "Hors distance de rendu",
  "timestamp": 5674
}
---
[3063] [2025-07-24T00:33:23.284Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -2
  },
  "blocksReceived": 32768,
  "timestamp": 5675
}
---
[3064] [2025-07-24T00:33:23.285Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -2
  },
  "distance": "8.06",
  "reason": "Hors distance de rendu",
  "timestamp": 5676
}
---
[3065] [2025-07-24T00:33:23.287Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 82.31,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -9.17,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3066] [2025-07-24T00:33:23.287Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -9.17,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.191,
    "z": 0
  }
}
---
[3067] [2025-07-24T00:33:23.287Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3068] [2025-07-24T00:33:23.287Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.191,
  "yBefore": 82.31,
  "yAfter": 82.11,
  "actualYMovement": -0.191
}
---
[3069] [2025-07-24T00:33:23.287Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 56
}
---
[3070] [2025-07-24T00:33:23.287Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 82.11,
    "z": -3.35
  },
  "feetY": 80.41,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3071] [2025-07-24T00:33:23.287Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 80.41,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 12.414,
  "targetGroundY": 69.7,
  "velocityY": -9.17,
  "currentOnGround": false
}
---
[3072] [2025-07-24T00:33:23.288Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -9.17,
  "distanceToGround": 12.414,
  "absDistanceToGround": 12.414,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3073] [2025-07-24T00:33:23.288Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 12.414,
  "velocityY": -9.42,
  "fallTime": 785,
  "onGround": false
}
---
[3074] [2025-07-24T00:33:23.288Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 82.11,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -9.42,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3075] [2025-07-24T00:33:23.295Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 82.11,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -9.42,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3076] [2025-07-24T00:33:23.295Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -9.42,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.065,
    "z": 0
  }
}
---
[3077] [2025-07-24T00:33:23.295Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3078] [2025-07-24T00:33:23.295Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.065,
  "yBefore": 82.11,
  "yAfter": 82.05,
  "actualYMovement": -0.065
}
---
[3079] [2025-07-24T00:33:23.295Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 64
}
---
[3080] [2025-07-24T00:33:23.295Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 82.05,
    "z": -3.35
  },
  "feetY": 80.35,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3081] [2025-07-24T00:33:23.295Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 80.35,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 12.349,
  "targetGroundY": 69.7,
  "velocityY": -9.42,
  "currentOnGround": false
}
---
[3082] [2025-07-24T00:33:23.296Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -9.42,
  "distanceToGround": 12.349,
  "absDistanceToGround": 12.349,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3083] [2025-07-24T00:33:23.296Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 12.349,
  "velocityY": -9.5,
  "fallTime": 792,
  "onGround": false
}
---
[3084] [2025-07-24T00:33:23.296Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 82.05,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -9.5,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3085] [2025-07-24T00:33:23.308Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 82.05,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -9.5,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3086] [2025-07-24T00:33:23.308Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -9.5,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.132,
    "z": 0
  }
}
---
[3087] [2025-07-24T00:33:23.308Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3088] [2025-07-24T00:33:23.308Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.132,
  "yBefore": 82.05,
  "yAfter": 81.92,
  "actualYMovement": -0.132
}
---
[3089] [2025-07-24T00:33:23.308Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 77
}
---
[3090] [2025-07-24T00:33:23.308Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 81.92,
    "z": -3.35
  },
  "feetY": 80.22,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3091] [2025-07-24T00:33:23.308Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 80.22,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 12.217,
  "targetGroundY": 69.7,
  "velocityY": -9.5,
  "currentOnGround": false
}
---
[3092] [2025-07-24T00:33:23.308Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -9.5,
  "distanceToGround": 12.217,
  "absDistanceToGround": 12.217,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3093] [2025-07-24T00:33:23.309Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 12.217,
  "velocityY": -9.67,
  "fallTime": 806,
  "onGround": false
}
---
[3094] [2025-07-24T00:33:23.309Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 81.92,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -9.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3095] [2025-07-24T00:33:23.309Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 49,
  "timestamp": 5701
}
---
[3096] [2025-07-24T00:33:23.309Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 48,
  "timestamp": 5701
}
---
[3097] [2025-07-24T00:33:23.321Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 81.92,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -9.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3098] [2025-07-24T00:33:23.321Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -9.67,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.134,
    "z": 0
  }
}
---
[3099] [2025-07-24T00:33:23.321Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3100] [2025-07-24T00:33:23.322Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.134,
  "yBefore": 81.92,
  "yAfter": 81.78,
  "actualYMovement": -0.134
}
---
[3101] [2025-07-24T00:33:23.322Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 91
}
---
[3102] [2025-07-24T00:33:23.322Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 81.78,
    "z": -3.35
  },
  "feetY": 80.08,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3103] [2025-07-24T00:33:23.322Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 80.08,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 12.083,
  "targetGroundY": 69.7,
  "velocityY": -9.67,
  "currentOnGround": false
}
---
[3104] [2025-07-24T00:33:23.322Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -9.67,
  "distanceToGround": 12.083,
  "absDistanceToGround": 12.083,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3105] [2025-07-24T00:33:23.322Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 12.083,
  "velocityY": -9.83,
  "fallTime": 819,
  "onGround": false
}
---
[3106] [2025-07-24T00:33:23.322Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 81.78,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -9.83,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3107] [2025-07-24T00:33:23.330Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 5722
}
---
[3108] [2025-07-24T00:33:23.343Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -6,
    "z": 3
  },
  "distance": "6.40",
  "totalRendered": 152,
  "visible": true,
  "timestamp": 5735
}
---
[3109] [2025-07-24T00:33:23.344Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 81.78,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -9.83,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3110] [2025-07-24T00:33:23.344Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -9.83,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.205,
    "z": 0
  }
}
---
[3111] [2025-07-24T00:33:23.344Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3112] [2025-07-24T00:33:23.344Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.205,
  "yBefore": 81.78,
  "yAfter": 81.58,
  "actualYMovement": -0.205
}
---
[3113] [2025-07-24T00:33:23.344Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3114] [2025-07-24T00:33:23.344Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3115] [2025-07-24T00:33:23.344Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3116] [2025-07-24T00:33:23.345Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 81.58,
    "z": -3.35
  },
  "feetY": 79.88,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3117] [2025-07-24T00:33:23.345Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 79.88,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 11.878,
  "targetGroundY": 69.7,
  "velocityY": -9.83,
  "currentOnGround": false
}
---
[3118] [2025-07-24T00:33:23.345Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -9.83,
  "distanceToGround": 11.878,
  "absDistanceToGround": 11.878,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3119] [2025-07-24T00:33:23.345Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 11.878,
  "velocityY": -10.08,
  "fallTime": 840,
  "onGround": false
}
---
[3120] [2025-07-24T00:33:23.345Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 81.58,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -10.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3121] [2025-07-24T00:33:23.356Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 5746
}
---
[3122] [2025-07-24T00:33:23.367Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -6,
    "z": -5
  },
  "distance": "6.40",
  "totalRendered": 153,
  "visible": true,
  "timestamp": 5758
}
---
[3123] [2025-07-24T00:33:23.368Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 81.58,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -10.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3124] [2025-07-24T00:33:23.368Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -10.08,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.21,
    "z": 0
  }
}
---
[3125] [2025-07-24T00:33:23.368Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3126] [2025-07-24T00:33:23.368Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.21,
  "yBefore": 81.58,
  "yAfter": 81.37,
  "actualYMovement": -0.21
}
---
[3127] [2025-07-24T00:33:23.368Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 24
}
---
[3128] [2025-07-24T00:33:23.368Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 81.37,
    "z": -3.35
  },
  "feetY": 79.67,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3129] [2025-07-24T00:33:23.369Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 79.67,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 11.668,
  "targetGroundY": 69.7,
  "velocityY": -10.08,
  "currentOnGround": false
}
---
[3130] [2025-07-24T00:33:23.369Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -10.08,
  "distanceToGround": 11.668,
  "absDistanceToGround": 11.668,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3131] [2025-07-24T00:33:23.369Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 11.668,
  "velocityY": -10.33,
  "fallTime": 861,
  "onGround": false
}
---
[3132] [2025-07-24T00:33:23.369Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 81.37,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -10.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3133] [2025-07-24T00:33:23.369Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 47,
  "timestamp": 5760
}
---
[3134] [2025-07-24T00:33:23.370Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 46,
  "timestamp": 5760
}
---
[3135] [2025-07-24T00:33:23.392Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 5783
}
---
[3136] [2025-07-24T00:33:23.405Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -4,
    "z": -7
  },
  "distance": "6.71",
  "totalRendered": 154,
  "visible": true,
  "timestamp": 5797
}
---
[3137] [2025-07-24T00:33:23.406Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.042,
  "position": {
    "x": -1.38,
    "y": 81.37,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -10.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3138] [2025-07-24T00:33:23.406Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -10.33,
    "z": 0
  },
  "delta": 0.042,
  "calculatedMovement": {
    "x": 0,
    "y": -0.431,
    "z": 0
  }
}
---
[3139] [2025-07-24T00:33:23.406Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3140] [2025-07-24T00:33:23.406Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.431,
  "yBefore": 81.37,
  "yAfter": 80.94,
  "actualYMovement": -0.431
}
---
[3141] [2025-07-24T00:33:23.406Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 62
}
---
[3142] [2025-07-24T00:33:23.406Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 80.94,
    "z": -3.35
  },
  "feetY": 79.24,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3143] [2025-07-24T00:33:23.406Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 79.24,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 11.237,
  "targetGroundY": 69.7,
  "velocityY": -10.33,
  "currentOnGround": false
}
---
[3144] [2025-07-24T00:33:23.406Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -10.33,
  "distanceToGround": 11.237,
  "absDistanceToGround": 11.237,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3145] [2025-07-24T00:33:23.407Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 11.237,
  "velocityY": -10.83,
  "fallTime": 903,
  "onGround": false
}
---
[3146] [2025-07-24T00:33:23.407Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 80.94,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -10.83,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3147] [2025-07-24T00:33:23.418Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 5810
}
---
[3148] [2025-07-24T00:33:23.431Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -4,
    "z": 5
  },
  "distance": "6.71",
  "totalRendered": 155,
  "visible": true,
  "timestamp": 5822
}
---
[3149] [2025-07-24T00:33:23.432Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 80.94,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -10.83,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3150] [2025-07-24T00:33:23.432Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -10.83,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.226,
    "z": 0
  }
}
---
[3151] [2025-07-24T00:33:23.432Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3152] [2025-07-24T00:33:23.432Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.226,
  "yBefore": 80.94,
  "yAfter": 80.71,
  "actualYMovement": -0.226
}
---
[3153] [2025-07-24T00:33:23.432Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 88
}
---
[3154] [2025-07-24T00:33:23.432Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 80.71,
    "z": -3.35
  },
  "feetY": 79.01,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3155] [2025-07-24T00:33:23.432Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 79.01,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 11.012,
  "targetGroundY": 69.7,
  "velocityY": -10.83,
  "currentOnGround": false
}
---
[3156] [2025-07-24T00:33:23.432Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -10.83,
  "distanceToGround": 11.012,
  "absDistanceToGround": 11.012,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3157] [2025-07-24T00:33:23.432Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 11.012,
  "velocityY": -11.08,
  "fallTime": 924,
  "onGround": false
}
---
[3158] [2025-07-24T00:33:23.433Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 80.71,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -11.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3159] [2025-07-24T00:33:23.442Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 80.71,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -11.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3160] [2025-07-24T00:33:23.442Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -11.08,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.154,
    "z": 0
  }
}
---
[3161] [2025-07-24T00:33:23.442Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3162] [2025-07-24T00:33:23.442Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.154,
  "yBefore": 80.71,
  "yAfter": 80.56,
  "actualYMovement": -0.154
}
---
[3163] [2025-07-24T00:33:23.443Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 99
}
---
[3164] [2025-07-24T00:33:23.443Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 80.56,
    "z": -3.35
  },
  "feetY": 78.86,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3165] [2025-07-24T00:33:23.443Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 78.86,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 10.858,
  "targetGroundY": 69.7,
  "velocityY": -11.08,
  "currentOnGround": false
}
---
[3166] [2025-07-24T00:33:23.443Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -11.08,
  "distanceToGround": 10.858,
  "absDistanceToGround": 10.858,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3167] [2025-07-24T00:33:23.443Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 10.858,
  "velocityY": -11.25,
  "fallTime": 937,
  "onGround": false
}
---
[3168] [2025-07-24T00:33:23.443Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 80.56,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -11.25,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3169] [2025-07-24T00:33:23.444Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 45,
  "timestamp": 5835
}
---
[3170] [2025-07-24T00:33:23.444Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 44,
  "timestamp": 5835
}
---
[3171] [2025-07-24T00:33:23.452Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 80.56,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -11.25,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3172] [2025-07-24T00:33:23.452Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -11.25,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.078,
    "z": 0
  }
}
---
[3173] [2025-07-24T00:33:23.452Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3174] [2025-07-24T00:33:23.452Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.078,
  "yBefore": 80.56,
  "yAfter": 80.48,
  "actualYMovement": -0.078
}
---
[3175] [2025-07-24T00:33:23.452Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3176] [2025-07-24T00:33:23.453Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3177] [2025-07-24T00:33:23.453Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3178] [2025-07-24T00:33:23.453Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 80.48,
    "z": -3.35
  },
  "feetY": 78.78,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3179] [2025-07-24T00:33:23.453Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 78.78,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 10.779,
  "targetGroundY": 69.7,
  "velocityY": -11.25,
  "currentOnGround": false
}
---
[3180] [2025-07-24T00:33:23.453Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -11.25,
  "distanceToGround": 10.779,
  "absDistanceToGround": 10.779,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3181] [2025-07-24T00:33:23.453Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 10.779,
  "velocityY": -11.33,
  "fallTime": 944,
  "onGround": false
}
---
[3182] [2025-07-24T00:33:23.453Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 80.48,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -11.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3183] [2025-07-24T00:33:23.463Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 5854
}
---
[3184] [2025-07-24T00:33:23.475Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -7,
    "z": -3
  },
  "distance": "6.32",
  "totalRendered": 156,
  "visible": true,
  "timestamp": 5866
}
---
[3185] [2025-07-24T00:33:23.475Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.028,
  "position": {
    "x": -1.38,
    "y": 80.48,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -11.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3186] [2025-07-24T00:33:23.476Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -11.33,
    "z": 0
  },
  "delta": 0.028,
  "calculatedMovement": {
    "x": 0,
    "y": -0.315,
    "z": 0
  }
}
---
[3187] [2025-07-24T00:33:23.476Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3188] [2025-07-24T00:33:23.476Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.315,
  "yBefore": 80.48,
  "yAfter": 80.16,
  "actualYMovement": -0.315
}
---
[3189] [2025-07-24T00:33:23.476Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 24
}
---
[3190] [2025-07-24T00:33:23.476Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 80.16,
    "z": -3.35
  },
  "feetY": 78.46,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3191] [2025-07-24T00:33:23.476Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 78.46,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 10.465,
  "targetGroundY": 69.7,
  "velocityY": -11.33,
  "currentOnGround": false
}
---
[3192] [2025-07-24T00:33:23.476Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -11.33,
  "distanceToGround": 10.465,
  "absDistanceToGround": 10.465,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3193] [2025-07-24T00:33:23.477Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 10.465,
  "velocityY": -11.67,
  "fallTime": 972,
  "onGround": false
}
---
[3194] [2025-07-24T00:33:23.477Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 80.16,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -11.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3195] [2025-07-24T00:33:23.490Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 5882
}
---
[3196] [2025-07-24T00:33:23.502Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -7,
    "z": 1
  },
  "distance": "6.32",
  "totalRendered": 157,
  "visible": true,
  "timestamp": 5893
}
---
[3197] [2025-07-24T00:33:23.503Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.028,
  "position": {
    "x": -1.38,
    "y": 80.16,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -11.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3198] [2025-07-24T00:33:23.503Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -11.67,
    "z": 0
  },
  "delta": 0.028,
  "calculatedMovement": {
    "x": 0,
    "y": -0.324,
    "z": 0
  }
}
---
[3199] [2025-07-24T00:33:23.503Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3200] [2025-07-24T00:33:23.503Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.324,
  "yBefore": 80.16,
  "yAfter": 79.84,
  "actualYMovement": -0.324
}
---
[3201] [2025-07-24T00:33:23.503Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 51
}
---
[3202] [2025-07-24T00:33:23.503Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 79.84,
    "z": -3.35
  },
  "feetY": 78.14,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3203] [2025-07-24T00:33:23.503Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 78.14,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 10.141,
  "targetGroundY": 69.7,
  "velocityY": -11.67,
  "currentOnGround": false
}
---
[3204] [2025-07-24T00:33:23.504Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -11.67,
  "distanceToGround": 10.141,
  "absDistanceToGround": 10.141,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3205] [2025-07-24T00:33:23.504Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 10.141,
  "velocityY": -12,
  "fallTime": 1000,
  "onGround": false
}
---
[3206] [2025-07-24T00:33:23.504Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 79.84,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -12,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3207] [2025-07-24T00:33:23.504Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 43,
  "timestamp": 5895
}
---
[3208] [2025-07-24T00:33:23.504Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 42,
  "timestamp": 5895
}
---
[3209] [2025-07-24T00:33:23.513Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 79.84,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -12,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3210] [2025-07-24T00:33:23.513Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -12,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.084,
    "z": 0
  }
}
---
[3211] [2025-07-24T00:33:23.514Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3212] [2025-07-24T00:33:23.514Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.084,
  "yBefore": 79.84,
  "yAfter": 79.76,
  "actualYMovement": -0.084
}
---
[3213] [2025-07-24T00:33:23.514Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 62
}
---
[3214] [2025-07-24T00:33:23.514Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 79.76,
    "z": -3.35
  },
  "feetY": 78.06,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3215] [2025-07-24T00:33:23.514Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 78.06,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 10.057,
  "targetGroundY": 69.7,
  "velocityY": -12,
  "currentOnGround": false
}
---
[3216] [2025-07-24T00:33:23.514Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -12,
  "distanceToGround": 10.057,
  "absDistanceToGround": 10.057,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3217] [2025-07-24T00:33:23.514Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 10.057,
  "velocityY": -12.08,
  "fallTime": 1007,
  "onGround": false
}
---
[3218] [2025-07-24T00:33:23.514Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 79.76,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -12.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3219] [2025-07-24T00:33:23.523Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 5914
}
---
[3220] [2025-07-24T00:33:23.523Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": 5
  },
  "distance": "7.81",
  "reason": "Hors distance de rendu",
  "timestamp": 5915
}
---
[3221] [2025-07-24T00:33:23.523Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 79.76,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -12.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3222] [2025-07-24T00:33:23.524Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -12.08,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.168,
    "z": 0
  }
}
---
[3223] [2025-07-24T00:33:23.524Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3224] [2025-07-24T00:33:23.524Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.168,
  "yBefore": 79.76,
  "yAfter": 79.59,
  "actualYMovement": -0.168
}
---
[3225] [2025-07-24T00:33:23.524Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 72
}
---
[3226] [2025-07-24T00:33:23.524Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 79.59,
    "z": -3.35
  },
  "feetY": 77.89,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3227] [2025-07-24T00:33:23.524Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 77.89,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 9.89,
  "targetGroundY": 69.7,
  "velocityY": -12.08,
  "currentOnGround": false
}
---
[3228] [2025-07-24T00:33:23.524Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -12.08,
  "distanceToGround": 9.89,
  "absDistanceToGround": 9.89,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3229] [2025-07-24T00:33:23.524Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 9.89,
  "velocityY": -12.25,
  "fallTime": 1021,
  "onGround": false
}
---
[3230] [2025-07-24T00:33:23.524Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 79.59,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -12.25,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3231] [2025-07-24T00:33:23.532Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 5923
}
---
[3232] [2025-07-24T00:33:23.532Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": -7
  },
  "distance": "7.81",
  "reason": "Hors distance de rendu",
  "timestamp": 5923
}
---
[3233] [2025-07-24T00:33:23.537Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 79.59,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -12.25,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3234] [2025-07-24T00:33:23.537Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -12.25,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.17,
    "z": 0
  }
}
---
[3235] [2025-07-24T00:33:23.537Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3236] [2025-07-24T00:33:23.537Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.17,
  "yBefore": 79.59,
  "yAfter": 79.42,
  "actualYMovement": -0.17
}
---
[3237] [2025-07-24T00:33:23.537Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 85
}
---
[3238] [2025-07-24T00:33:23.537Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 79.42,
    "z": -3.35
  },
  "feetY": 77.72,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3239] [2025-07-24T00:33:23.537Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 77.72,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 9.719,
  "targetGroundY": 69.7,
  "velocityY": -12.25,
  "currentOnGround": false
}
---
[3240] [2025-07-24T00:33:23.539Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -12.25,
  "distanceToGround": 9.719,
  "absDistanceToGround": 9.719,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3241] [2025-07-24T00:33:23.539Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 9.719,
  "velocityY": -12.42,
  "fallTime": 1035,
  "onGround": false
}
---
[3242] [2025-07-24T00:33:23.539Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 79.42,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -12.42,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3243] [2025-07-24T00:33:23.539Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 41,
  "timestamp": 5930
}
---
[3244] [2025-07-24T00:33:23.539Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 40,
  "timestamp": 5930
}
---
[3245] [2025-07-24T00:33:23.555Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 5947
}
---
[3246] [2025-07-24T00:33:23.555Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": 3
  },
  "distance": "8.06",
  "reason": "Hors distance de rendu",
  "timestamp": 5947
}
---
[3247] [2025-07-24T00:33:23.557Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 5949
}
---
[3248] [2025-07-24T00:33:23.557Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -5
  },
  "distance": "8.06",
  "reason": "Hors distance de rendu",
  "timestamp": 5949
}
---
[3249] [2025-07-24T00:33:23.557Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 79.42,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -12.42,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3250] [2025-07-24T00:33:23.558Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -12.42,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.259,
    "z": 0
  }
}
---
[3251] [2025-07-24T00:33:23.558Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3252] [2025-07-24T00:33:23.558Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.259,
  "yBefore": 79.42,
  "yAfter": 79.16,
  "actualYMovement": -0.259
}
---
[3253] [2025-07-24T00:33:23.558Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3254] [2025-07-24T00:33:23.558Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3255] [2025-07-24T00:33:23.558Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3256] [2025-07-24T00:33:23.558Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 79.16,
    "z": -3.35
  },
  "feetY": 77.46,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3257] [2025-07-24T00:33:23.558Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 77.46,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 9.461,
  "targetGroundY": 69.7,
  "velocityY": -12.42,
  "currentOnGround": false
}
---
[3258] [2025-07-24T00:33:23.558Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -12.42,
  "distanceToGround": 9.461,
  "absDistanceToGround": 9.461,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3259] [2025-07-24T00:33:23.558Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 9.461,
  "velocityY": -12.67,
  "fallTime": 1056,
  "onGround": false
}
---
[3260] [2025-07-24T00:33:23.559Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 79.16,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -12.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3261] [2025-07-24T00:33:23.592Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.035,
  "position": {
    "x": -1.38,
    "y": 79.16,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -12.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3262] [2025-07-24T00:33:23.594Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -12.67,
    "z": 0
  },
  "delta": 0.035,
  "calculatedMovement": {
    "x": 0,
    "y": -0.44,
    "z": 0
  }
}
---
[3263] [2025-07-24T00:33:23.595Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3264] [2025-07-24T00:33:23.596Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.44,
  "yBefore": 79.16,
  "yAfter": 78.72,
  "actualYMovement": -0.44
}
---
[3265] [2025-07-24T00:33:23.596Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 38
}
---
[3266] [2025-07-24T00:33:23.596Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 78.72,
    "z": -3.35
  },
  "feetY": 77.02,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3267] [2025-07-24T00:33:23.597Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 77.02,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 9.021,
  "targetGroundY": 69.7,
  "velocityY": -12.67,
  "currentOnGround": false
}
---
[3268] [2025-07-24T00:33:23.597Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -12.67,
  "distanceToGround": 9.021,
  "absDistanceToGround": 9.021,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3269] [2025-07-24T00:33:23.597Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 9.021,
  "velocityY": -13.08,
  "fallTime": 1090,
  "onGround": false
}
---
[3270] [2025-07-24T00:33:23.598Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 78.72,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -13.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3271] [2025-07-24T00:33:23.613Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 78.72,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -13.08,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3272] [2025-07-24T00:33:23.614Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -13.08,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.273,
    "z": 0
  }
}
---
[3273] [2025-07-24T00:33:23.614Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3274] [2025-07-24T00:33:23.615Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.273,
  "yBefore": 78.72,
  "yAfter": 78.45,
  "actualYMovement": -0.273
}
---
[3275] [2025-07-24T00:33:23.615Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 57
}
---
[3276] [2025-07-24T00:33:23.616Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 78.45,
    "z": -3.35
  },
  "feetY": 76.75,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3277] [2025-07-24T00:33:23.616Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 76.75,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 8.748,
  "targetGroundY": 69.7,
  "velocityY": -13.08,
  "currentOnGround": false
}
---
[3278] [2025-07-24T00:33:23.616Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -13.08,
  "distanceToGround": 8.748,
  "absDistanceToGround": 8.748,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3279] [2025-07-24T00:33:23.616Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 8.748,
  "velocityY": -13.33,
  "fallTime": 1111,
  "onGround": false
}
---
[3280] [2025-07-24T00:33:23.617Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 78.45,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -13.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3281] [2025-07-24T00:33:23.617Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 39,
  "timestamp": 6008
}
---
[3282] [2025-07-24T00:33:23.617Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 38,
  "timestamp": 6008
}
---
[3283] [2025-07-24T00:33:23.626Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 78.45,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -13.33,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3284] [2025-07-24T00:33:23.627Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -13.33,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.185,
    "z": 0
  }
}
---
[3285] [2025-07-24T00:33:23.627Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3286] [2025-07-24T00:33:23.627Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.185,
  "yBefore": 78.45,
  "yAfter": 78.26,
  "actualYMovement": -0.185
}
---
[3287] [2025-07-24T00:33:23.627Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 69
}
---
[3288] [2025-07-24T00:33:23.627Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 78.26,
    "z": -3.35
  },
  "feetY": 76.56,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3289] [2025-07-24T00:33:23.627Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 76.56,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 8.563,
  "targetGroundY": 69.7,
  "velocityY": -13.33,
  "currentOnGround": false
}
---
[3290] [2025-07-24T00:33:23.627Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -13.33,
  "distanceToGround": 8.563,
  "absDistanceToGround": 8.563,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3291] [2025-07-24T00:33:23.627Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 8.563,
  "velocityY": -13.5,
  "fallTime": 1125,
  "onGround": false
}
---
[3292] [2025-07-24T00:33:23.627Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 78.26,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -13.5,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3293] [2025-07-24T00:33:23.639Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 78.26,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -13.5,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3294] [2025-07-24T00:33:23.640Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -13.5,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.188,
    "z": 0
  }
}
---
[3295] [2025-07-24T00:33:23.640Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3296] [2025-07-24T00:33:23.640Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.188,
  "yBefore": 78.26,
  "yAfter": 78.08,
  "actualYMovement": -0.188
}
---
[3297] [2025-07-24T00:33:23.641Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 83
}
---
[3298] [2025-07-24T00:33:23.642Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 78.08,
    "z": -3.35
  },
  "feetY": 76.38,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3299] [2025-07-24T00:33:23.642Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 76.38,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 8.376,
  "targetGroundY": 69.7,
  "velocityY": -13.5,
  "currentOnGround": false
}
---
[3300] [2025-07-24T00:33:23.642Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -13.5,
  "distanceToGround": 8.376,
  "absDistanceToGround": 8.376,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3301] [2025-07-24T00:33:23.642Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 8.376,
  "velocityY": -13.67,
  "fallTime": 1139,
  "onGround": false
}
---
[3302] [2025-07-24T00:33:23.642Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 78.08,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -13.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3303] [2025-07-24T00:33:23.653Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 6044
}
---
[3304] [2025-07-24T00:33:23.654Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": -8
  },
  "distance": "7.07",
  "reason": "Hors distance de rendu",
  "timestamp": 6045
}
---
[3305] [2025-07-24T00:33:23.655Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 6046
}
---
[3306] [2025-07-24T00:33:23.656Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": 6
  },
  "distance": "7.07",
  "reason": "Hors distance de rendu",
  "timestamp": 6046
}
---
[3307] [2025-07-24T00:33:23.656Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 78.08,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -13.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3308] [2025-07-24T00:33:23.656Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -13.67,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.19,
    "z": 0
  }
}
---
[3309] [2025-07-24T00:33:23.656Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3310] [2025-07-24T00:33:23.657Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.19,
  "yBefore": 78.08,
  "yAfter": 77.89,
  "actualYMovement": -0.19
}
---
[3311] [2025-07-24T00:33:23.657Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 99
}
---
[3312] [2025-07-24T00:33:23.657Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 77.89,
    "z": -3.35
  },
  "feetY": 76.19,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3313] [2025-07-24T00:33:23.657Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 76.19,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 8.186,
  "targetGroundY": 69.7,
  "velocityY": -13.67,
  "currentOnGround": false
}
---
[3314] [2025-07-24T00:33:23.657Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -13.67,
  "distanceToGround": 8.186,
  "absDistanceToGround": 8.186,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3315] [2025-07-24T00:33:23.657Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 8.186,
  "velocityY": -13.83,
  "fallTime": 1153,
  "onGround": false
}
---
[3316] [2025-07-24T00:33:23.657Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 77.89,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -13.83,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3317] [2025-07-24T00:33:23.657Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 37,
  "timestamp": 6049
}
---
[3318] [2025-07-24T00:33:23.657Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 36,
  "timestamp": 6049
}
---
[3319] [2025-07-24T00:33:23.668Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 77.89,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -13.83,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3320] [2025-07-24T00:33:23.668Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -13.83,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.192,
    "z": 0
  }
}
---
[3321] [2025-07-24T00:33:23.668Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3322] [2025-07-24T00:33:23.669Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.192,
  "yBefore": 77.89,
  "yAfter": 77.69,
  "actualYMovement": -0.192
}
---
[3323] [2025-07-24T00:33:23.669Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3324] [2025-07-24T00:33:23.669Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3325] [2025-07-24T00:33:23.669Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3326] [2025-07-24T00:33:23.669Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 77.69,
    "z": -3.35
  },
  "feetY": 75.99,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3327] [2025-07-24T00:33:23.669Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 75.99,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 7.994,
  "targetGroundY": 69.7,
  "velocityY": -13.83,
  "currentOnGround": false
}
---
[3328] [2025-07-24T00:33:23.669Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -13.83,
  "distanceToGround": 7.994,
  "absDistanceToGround": 7.994,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3329] [2025-07-24T00:33:23.670Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 7.994,
  "velocityY": -14,
  "fallTime": 1167,
  "onGround": false
}
---
[3330] [2025-07-24T00:33:23.670Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 77.69,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -14,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3331] [2025-07-24T00:33:23.680Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 6071
}
---
[3332] [2025-07-24T00:33:23.680Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": 6
  },
  "distance": "7.62",
  "reason": "Hors distance de rendu",
  "timestamp": 6072
}
---
[3333] [2025-07-24T00:33:23.682Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 6073
}
---
[3334] [2025-07-24T00:33:23.682Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": -8
  },
  "distance": "7.62",
  "reason": "Hors distance de rendu",
  "timestamp": 6074
}
---
[3335] [2025-07-24T00:33:23.703Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.035,
  "position": {
    "x": -1.38,
    "y": 77.69,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -14,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3336] [2025-07-24T00:33:23.703Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -14,
    "z": 0
  },
  "delta": 0.035,
  "calculatedMovement": {
    "x": 0,
    "y": -0.486,
    "z": 0
  }
}
---
[3337] [2025-07-24T00:33:23.703Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3338] [2025-07-24T00:33:23.704Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.486,
  "yBefore": 77.69,
  "yAfter": 77.21,
  "actualYMovement": -0.486
}
---
[3339] [2025-07-24T00:33:23.705Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 36
}
---
[3340] [2025-07-24T00:33:23.705Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 77.21,
    "z": -3.35
  },
  "feetY": 75.51,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3341] [2025-07-24T00:33:23.705Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 75.51,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 7.508,
  "targetGroundY": 69.7,
  "velocityY": -14,
  "currentOnGround": false
}
---
[3342] [2025-07-24T00:33:23.705Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -14,
  "distanceToGround": 7.508,
  "absDistanceToGround": 7.508,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3343] [2025-07-24T00:33:23.705Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 7.508,
  "velocityY": -14.42,
  "fallTime": 1201,
  "onGround": false
}
---
[3344] [2025-07-24T00:33:23.705Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 77.21,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -14.42,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3345] [2025-07-24T00:33:23.724Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 77.21,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -14.42,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3346] [2025-07-24T00:33:23.724Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -14.42,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.3,
    "z": 0
  }
}
---
[3347] [2025-07-24T00:33:23.724Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3348] [2025-07-24T00:33:23.724Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.3,
  "yBefore": 77.21,
  "yAfter": 76.91,
  "actualYMovement": -0.3
}
---
[3349] [2025-07-24T00:33:23.724Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 55
}
---
[3350] [2025-07-24T00:33:23.725Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 76.91,
    "z": -3.35
  },
  "feetY": 75.21,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3351] [2025-07-24T00:33:23.725Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 75.21,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 7.207,
  "targetGroundY": 69.7,
  "velocityY": -14.42,
  "currentOnGround": false
}
---
[3352] [2025-07-24T00:33:23.725Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -14.42,
  "distanceToGround": 7.207,
  "absDistanceToGround": 7.207,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3353] [2025-07-24T00:33:23.725Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 7.207,
  "velocityY": -14.67,
  "fallTime": 1222,
  "onGround": false
}
---
[3354] [2025-07-24T00:33:23.725Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 76.91,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -14.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3355] [2025-07-24T00:33:23.725Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 35,
  "timestamp": 6117
}
---
[3356] [2025-07-24T00:33:23.725Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 34,
  "timestamp": 6117
}
---
[3357] [2025-07-24T00:33:23.739Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 6130
}
---
[3358] [2025-07-24T00:33:23.739Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": 1
  },
  "distance": "8.25",
  "reason": "Hors distance de rendu",
  "timestamp": 6131
}
---
[3359] [2025-07-24T00:33:23.741Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 6133
}
---
[3360] [2025-07-24T00:33:23.741Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -3
  },
  "distance": "8.25",
  "reason": "Hors distance de rendu",
  "timestamp": 6133
}
---
[3361] [2025-07-24T00:33:23.745Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 76.91,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -14.67,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3362] [2025-07-24T00:33:23.746Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -14.67,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.306,
    "z": 0
  }
}
---
[3363] [2025-07-24T00:33:23.746Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3364] [2025-07-24T00:33:23.746Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.306,
  "yBefore": 76.91,
  "yAfter": 76.6,
  "actualYMovement": -0.306
}
---
[3365] [2025-07-24T00:33:23.746Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 77
}
---
[3366] [2025-07-24T00:33:23.746Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 76.6,
    "z": -3.35
  },
  "feetY": 74.9,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3367] [2025-07-24T00:33:23.747Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 74.9,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 6.902,
  "targetGroundY": 69.7,
  "velocityY": -14.67,
  "currentOnGround": false
}
---
[3368] [2025-07-24T00:33:23.747Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -14.67,
  "distanceToGround": 6.902,
  "absDistanceToGround": 6.902,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3369] [2025-07-24T00:33:23.747Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 6.902,
  "velocityY": -14.92,
  "fallTime": 1243,
  "onGround": false
}
---
[3370] [2025-07-24T00:33:23.747Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 76.6,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -14.92,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3371] [2025-07-24T00:33:23.766Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 76.6,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -14.92,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3372] [2025-07-24T00:33:23.766Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -14.92,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.311,
    "z": 0
  }
}
---
[3373] [2025-07-24T00:33:23.766Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3374] [2025-07-24T00:33:23.766Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.311,
  "yBefore": 76.6,
  "yAfter": 76.29,
  "actualYMovement": -0.311
}
---
[3375] [2025-07-24T00:33:23.766Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 97
}
---
[3376] [2025-07-24T00:33:23.766Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 76.29,
    "z": -3.35
  },
  "feetY": 74.59,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3377] [2025-07-24T00:33:23.766Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 74.59,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 6.591,
  "targetGroundY": 69.7,
  "velocityY": -14.92,
  "currentOnGround": false
}
---
[3378] [2025-07-24T00:33:23.766Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -14.92,
  "distanceToGround": 6.591,
  "absDistanceToGround": 6.591,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3379] [2025-07-24T00:33:23.766Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 6.591,
  "velocityY": -15,
  "fallTime": 1264,
  "onGround": false
}
---
[3380] [2025-07-24T00:33:23.766Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 76.29,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3381] [2025-07-24T00:33:23.786Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 76.29,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3382] [2025-07-24T00:33:23.787Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.313,
    "z": 0
  }
}
---
[3383] [2025-07-24T00:33:23.787Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3384] [2025-07-24T00:33:23.787Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.313,
  "yBefore": 76.29,
  "yAfter": 75.98,
  "actualYMovement": -0.313
}
---
[3385] [2025-07-24T00:33:23.787Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3386] [2025-07-24T00:33:23.787Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3387] [2025-07-24T00:33:23.788Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3388] [2025-07-24T00:33:23.788Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 75.98,
    "z": -3.35
  },
  "feetY": 74.28,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3389] [2025-07-24T00:33:23.788Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 74.28,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 6.279,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3390] [2025-07-24T00:33:23.788Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 6.279,
  "absDistanceToGround": 6.279,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3391] [2025-07-24T00:33:23.788Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 6.279,
  "velocityY": -15,
  "fallTime": 1285,
  "onGround": false
}
---
[3392] [2025-07-24T00:33:23.788Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 75.98,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3393] [2025-07-24T00:33:23.789Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 33,
  "timestamp": 6180
}
---
[3394] [2025-07-24T00:33:23.789Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 32,
  "timestamp": 6180
}
---
[3395] [2025-07-24T00:33:23.815Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 6207
}
---
[3396] [2025-07-24T00:33:23.827Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -7,
    "z": 2
  },
  "distance": "6.71",
  "totalRendered": 158,
  "visible": true,
  "timestamp": 6219
}
---
[3397] [2025-07-24T00:33:23.827Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.042,
  "position": {
    "x": -1.38,
    "y": 75.98,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3398] [2025-07-24T00:33:23.827Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.042,
  "calculatedMovement": {
    "x": 0,
    "y": -0.625,
    "z": 0
  }
}
---
[3399] [2025-07-24T00:33:23.828Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3400] [2025-07-24T00:33:23.828Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.625,
  "yBefore": 75.98,
  "yAfter": 75.35,
  "actualYMovement": -0.625
}
---
[3401] [2025-07-24T00:33:23.828Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 41
}
---
[3402] [2025-07-24T00:33:23.828Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 75.35,
    "z": -3.35
  },
  "feetY": 73.65,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3403] [2025-07-24T00:33:23.828Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 73.65,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 5.654,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3404] [2025-07-24T00:33:23.828Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 5.654,
  "absDistanceToGround": 5.654,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3405] [2025-07-24T00:33:23.828Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 5.654,
  "velocityY": -15,
  "fallTime": 1326,
  "onGround": false
}
---
[3406] [2025-07-24T00:33:23.828Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 75.35,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3407] [2025-07-24T00:33:23.839Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 6230
}
---
[3408] [2025-07-24T00:33:23.851Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -7,
    "z": -4
  },
  "distance": "6.71",
  "totalRendered": 159,
  "visible": true,
  "timestamp": 6242
}
---
[3409] [2025-07-24T00:33:23.851Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 75.35,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3410] [2025-07-24T00:33:23.851Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.313,
    "z": 0
  }
}
---
[3411] [2025-07-24T00:33:23.851Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3412] [2025-07-24T00:33:23.852Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.313,
  "yBefore": 75.35,
  "yAfter": 75.04,
  "actualYMovement": -0.313
}
---
[3413] [2025-07-24T00:33:23.852Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 65
}
---
[3414] [2025-07-24T00:33:23.852Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 75.04,
    "z": -3.35
  },
  "feetY": 73.34,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3415] [2025-07-24T00:33:23.852Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 73.34,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 5.341,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3416] [2025-07-24T00:33:23.852Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 5.341,
  "absDistanceToGround": 5.341,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3417] [2025-07-24T00:33:23.852Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 5.341,
  "velocityY": -15,
  "fallTime": 1347,
  "onGround": false
}
---
[3418] [2025-07-24T00:33:23.852Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 75.04,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3419] [2025-07-24T00:33:23.877Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.028,
  "position": {
    "x": -1.38,
    "y": 75.04,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3420] [2025-07-24T00:33:23.878Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.028,
  "calculatedMovement": {
    "x": 0,
    "y": -0.417,
    "z": 0
  }
}
---
[3421] [2025-07-24T00:33:23.878Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3422] [2025-07-24T00:33:23.878Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.417,
  "yBefore": 75.04,
  "yAfter": 74.62,
  "actualYMovement": -0.417
}
---
[3423] [2025-07-24T00:33:23.878Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 91
}
---
[3424] [2025-07-24T00:33:23.878Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 74.62,
    "z": -3.35
  },
  "feetY": 72.92,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3425] [2025-07-24T00:33:23.878Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 72.92,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 4.925,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3426] [2025-07-24T00:33:23.878Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 4.925,
  "absDistanceToGround": 4.925,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3427] [2025-07-24T00:33:23.878Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 4.925,
  "velocityY": -15,
  "fallTime": 1375,
  "onGround": false
}
---
[3428] [2025-07-24T00:33:23.878Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 74.62,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3429] [2025-07-24T00:33:23.878Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 31,
  "timestamp": 6269
}
---
[3430] [2025-07-24T00:33:23.878Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 30,
  "timestamp": 6270
}
---
[3431] [2025-07-24T00:33:23.887Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 74.62,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3432] [2025-07-24T00:33:23.888Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.104,
    "z": 0
  }
}
---
[3433] [2025-07-24T00:33:23.888Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3434] [2025-07-24T00:33:23.888Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.104,
  "yBefore": 74.62,
  "yAfter": 74.52,
  "actualYMovement": -0.104
}
---
[3435] [2025-07-24T00:33:23.888Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3436] [2025-07-24T00:33:23.889Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3437] [2025-07-24T00:33:23.889Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3438] [2025-07-24T00:33:23.889Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 74.52,
    "z": -3.35
  },
  "feetY": 72.82,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3439] [2025-07-24T00:33:23.889Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 72.82,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 4.82,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3440] [2025-07-24T00:33:23.889Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 4.82,
  "absDistanceToGround": 4.82,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3441] [2025-07-24T00:33:23.889Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 4.82,
  "velocityY": -15,
  "fallTime": 1382,
  "onGround": false
}
---
[3442] [2025-07-24T00:33:23.889Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 74.52,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3443] [2025-07-24T00:33:23.898Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 6290
}
---
[3444] [2025-07-24T00:33:23.899Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": 6
  },
  "distance": "7.28",
  "reason": "Hors distance de rendu",
  "timestamp": 6290
}
---
[3445] [2025-07-24T00:33:23.900Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 6292
}
---
[3446] [2025-07-24T00:33:23.900Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": -8
  },
  "distance": "7.28",
  "reason": "Hors distance de rendu",
  "timestamp": 6292
}
---
[3447] [2025-07-24T00:33:23.926Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.042,
  "position": {
    "x": -1.38,
    "y": 74.52,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3448] [2025-07-24T00:33:23.926Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.042,
  "calculatedMovement": {
    "x": 0,
    "y": -0.625,
    "z": 0
  }
}
---
[3449] [2025-07-24T00:33:23.926Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3450] [2025-07-24T00:33:23.926Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.625,
  "yBefore": 74.52,
  "yAfter": 73.9,
  "actualYMovement": -0.625
}
---
[3451] [2025-07-24T00:33:23.926Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 38
}
---
[3452] [2025-07-24T00:33:23.926Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 73.9,
    "z": -3.35
  },
  "feetY": 72.2,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3453] [2025-07-24T00:33:23.926Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 72.2,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 4.196,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3454] [2025-07-24T00:33:23.926Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 4.196,
  "absDistanceToGround": 4.196,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3455] [2025-07-24T00:33:23.926Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 4.196,
  "velocityY": -15,
  "fallTime": 1424,
  "onGround": false
}
---
[3456] [2025-07-24T00:33:23.926Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 73.9,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3457] [2025-07-24T00:33:23.932Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 73.9,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3458] [2025-07-24T00:33:23.933Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.104,
    "z": 0
  }
}
---
[3459] [2025-07-24T00:33:23.933Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3460] [2025-07-24T00:33:23.933Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.104,
  "yBefore": 73.9,
  "yAfter": 73.79,
  "actualYMovement": -0.104
}
---
[3461] [2025-07-24T00:33:23.933Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 45
}
---
[3462] [2025-07-24T00:33:23.933Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 73.79,
    "z": -3.35
  },
  "feetY": 72.09,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3463] [2025-07-24T00:33:23.933Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 72.09,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 4.091,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3464] [2025-07-24T00:33:23.933Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 4.091,
  "absDistanceToGround": 4.091,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3465] [2025-07-24T00:33:23.934Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 4.091,
  "velocityY": -15,
  "fallTime": 1431,
  "onGround": false
}
---
[3466] [2025-07-24T00:33:23.934Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 73.79,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3467] [2025-07-24T00:33:23.934Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 29,
  "timestamp": 6325
}
---
[3468] [2025-07-24T00:33:23.934Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 28,
  "timestamp": 6325
}
---
[3469] [2025-07-24T00:33:23.949Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 6341
}
---
[3470] [2025-07-24T00:33:23.950Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": -8
  },
  "distance": "8.06",
  "reason": "Hors distance de rendu",
  "timestamp": 6341
}
---
[3471] [2025-07-24T00:33:23.951Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 6342
}
---
[3472] [2025-07-24T00:33:23.952Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": 6
  },
  "distance": "8.06",
  "reason": "Hors distance de rendu",
  "timestamp": 6342
}
---
[3473] [2025-07-24T00:33:23.953Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 73.79,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3474] [2025-07-24T00:33:23.953Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.312,
    "z": 0
  }
}
---
[3475] [2025-07-24T00:33:23.953Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3476] [2025-07-24T00:33:23.953Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.312,
  "yBefore": 73.79,
  "yAfter": 73.48,
  "actualYMovement": -0.312
}
---
[3477] [2025-07-24T00:33:23.954Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 66
}
---
[3478] [2025-07-24T00:33:23.954Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 73.48,
    "z": -3.35
  },
  "feetY": 71.78,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3479] [2025-07-24T00:33:23.954Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 71.78,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 3.779,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3480] [2025-07-24T00:33:23.954Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 3.779,
  "absDistanceToGround": 3.779,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3481] [2025-07-24T00:33:23.954Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 3.779,
  "velocityY": -15,
  "fallTime": 1451,
  "onGround": false
}
---
[3482] [2025-07-24T00:33:23.954Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 73.48,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3483] [2025-07-24T00:33:23.974Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 73.48,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3484] [2025-07-24T00:33:23.975Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.313,
    "z": 0
  }
}
---
[3485] [2025-07-24T00:33:23.975Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3486] [2025-07-24T00:33:23.975Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.313,
  "yBefore": 73.48,
  "yAfter": 73.17,
  "actualYMovement": -0.313
}
---
[3487] [2025-07-24T00:33:23.975Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 87
}
---
[3488] [2025-07-24T00:33:23.975Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 73.17,
    "z": -3.35
  },
  "feetY": 71.47,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3489] [2025-07-24T00:33:23.975Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 71.47,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 3.466,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3490] [2025-07-24T00:33:23.975Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 3.466,
  "absDistanceToGround": 3.466,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3491] [2025-07-24T00:33:23.975Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 3.466,
  "velocityY": -15,
  "fallTime": 1472,
  "onGround": false
}
---
[3492] [2025-07-24T00:33:23.976Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 73.17,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3493] [2025-07-24T00:33:23.995Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 73.17,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3494] [2025-07-24T00:33:23.995Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.312,
    "z": 0
  }
}
---
[3495] [2025-07-24T00:33:23.996Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3496] [2025-07-24T00:33:23.996Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.312,
  "yBefore": 73.17,
  "yAfter": 72.85,
  "actualYMovement": -0.312
}
---
[3497] [2025-07-24T00:33:23.996Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3498] [2025-07-24T00:33:23.996Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3499] [2025-07-24T00:33:23.996Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3500] [2025-07-24T00:33:23.996Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 72.85,
    "z": -3.35
  },
  "feetY": 71.15,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3501] [2025-07-24T00:33:23.996Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 71.15,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 3.154,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3502] [2025-07-24T00:33:23.996Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 3.154,
  "absDistanceToGround": 3.154,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3503] [2025-07-24T00:33:23.996Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 3.154,
  "velocityY": -15,
  "fallTime": 1493,
  "onGround": false
}
---
[3504] [2025-07-24T00:33:23.997Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 72.85,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3505] [2025-07-24T00:33:23.997Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 27,
  "timestamp": 6388
}
---
[3506] [2025-07-24T00:33:23.997Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 26,
  "timestamp": 6388
}
---
[3507] [2025-07-24T00:33:24.009Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 6401
}
---
[3508] [2025-07-24T00:33:24.010Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": 2
  },
  "distance": "8.54",
  "reason": "Hors distance de rendu",
  "timestamp": 6401
}
---
[3509] [2025-07-24T00:33:24.012Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 6403
}
---
[3510] [2025-07-24T00:33:24.012Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -4
  },
  "distance": "8.54",
  "reason": "Hors distance de rendu",
  "timestamp": 6403
}
---
[3511] [2025-07-24T00:33:24.023Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.028,
  "position": {
    "x": -1.38,
    "y": 72.85,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3512] [2025-07-24T00:33:24.023Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.028,
  "calculatedMovement": {
    "x": 0,
    "y": -0.417,
    "z": 0
  }
}
---
[3513] [2025-07-24T00:33:24.023Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3514] [2025-07-24T00:33:24.023Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.417,
  "yBefore": 72.85,
  "yAfter": 72.44,
  "actualYMovement": -0.417
}
---
[3515] [2025-07-24T00:33:24.023Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 27
}
---
[3516] [2025-07-24T00:33:24.023Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 72.44,
    "z": -3.35
  },
  "feetY": 70.74,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3517] [2025-07-24T00:33:24.023Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 70.74,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 2.737,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3518] [2025-07-24T00:33:24.024Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 2.737,
  "absDistanceToGround": 2.737,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3519] [2025-07-24T00:33:24.024Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 2.737,
  "velocityY": -15,
  "fallTime": 1521,
  "onGround": false
}
---
[3520] [2025-07-24T00:33:24.024Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 72.44,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3521] [2025-07-24T00:33:24.037Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 72.44,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3522] [2025-07-24T00:33:24.037Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.208,
    "z": 0
  }
}
---
[3523] [2025-07-24T00:33:24.037Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3524] [2025-07-24T00:33:24.037Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.208,
  "yBefore": 72.44,
  "yAfter": 72.23,
  "actualYMovement": -0.209
}
---
[3525] [2025-07-24T00:33:24.037Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 41
}
---
[3526] [2025-07-24T00:33:24.037Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 72.23,
    "z": -3.35
  },
  "feetY": 70.53,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3527] [2025-07-24T00:33:24.037Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 70.53,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 2.529,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3528] [2025-07-24T00:33:24.037Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 2.529,
  "absDistanceToGround": 2.529,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3529] [2025-07-24T00:33:24.038Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 2.529,
  "velocityY": -15,
  "fallTime": 1535,
  "onGround": false
}
---
[3530] [2025-07-24T00:33:24.038Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 72.23,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3531] [2025-07-24T00:33:24.057Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 72.23,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3532] [2025-07-24T00:33:24.057Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.312,
    "z": 0
  }
}
---
[3533] [2025-07-24T00:33:24.057Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3534] [2025-07-24T00:33:24.057Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.312,
  "yBefore": 72.23,
  "yAfter": 71.92,
  "actualYMovement": -0.312
}
---
[3535] [2025-07-24T00:33:24.057Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 61
}
---
[3536] [2025-07-24T00:33:24.058Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 71.92,
    "z": -3.35
  },
  "feetY": 70.22,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3537] [2025-07-24T00:33:24.058Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 70.22,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 2.216,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3538] [2025-07-24T00:33:24.058Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 2.216,
  "absDistanceToGround": 2.216,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3539] [2025-07-24T00:33:24.058Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 2.216,
  "velocityY": -15,
  "fallTime": 1555,
  "onGround": false
}
---
[3540] [2025-07-24T00:33:24.058Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 71.92,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3541] [2025-07-24T00:33:24.058Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 25,
  "timestamp": 6450
}
---
[3542] [2025-07-24T00:33:24.058Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 24,
  "timestamp": 6450
}
---
[3543] [2025-07-24T00:33:24.073Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 6465
}
---
[3544] [2025-07-24T00:33:24.074Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -6
  },
  "distance": "7.07",
  "reason": "Hors distance de rendu",
  "timestamp": 6466
}
---
[3545] [2025-07-24T00:33:24.076Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 6467
}
---
[3546] [2025-07-24T00:33:24.076Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": 4
  },
  "distance": "7.07",
  "reason": "Hors distance de rendu",
  "timestamp": 6468
}
---
[3547] [2025-07-24T00:33:24.079Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 71.92,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3548] [2025-07-24T00:33:24.079Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.313,
    "z": 0
  }
}
---
[3549] [2025-07-24T00:33:24.079Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3550] [2025-07-24T00:33:24.079Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.313,
  "yBefore": 71.92,
  "yAfter": 71.6,
  "actualYMovement": -0.313
}
---
[3551] [2025-07-24T00:33:24.079Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 83
}
---
[3552] [2025-07-24T00:33:24.079Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 71.6,
    "z": -3.35
  },
  "feetY": 69.9,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3553] [2025-07-24T00:33:24.079Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 69.9,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 1.904,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3554] [2025-07-24T00:33:24.079Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 1.904,
  "absDistanceToGround": 1.904,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3555] [2025-07-24T00:33:24.080Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 1.904,
  "velocityY": -15,
  "fallTime": 1576,
  "onGround": false
}
---
[3556] [2025-07-24T00:33:24.080Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 71.6,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3557] [2025-07-24T00:33:24.099Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 71.6,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3558] [2025-07-24T00:33:24.100Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.313,
    "z": 0
  }
}
---
[3559] [2025-07-24T00:33:24.100Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3560] [2025-07-24T00:33:24.100Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.313,
  "yBefore": 71.6,
  "yAfter": 71.29,
  "actualYMovement": -0.313
}
---
[3561] [2025-07-24T00:33:24.100Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3562] [2025-07-24T00:33:24.100Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3563] [2025-07-24T00:33:24.100Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3564] [2025-07-24T00:33:24.100Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 71.29,
    "z": -3.35
  },
  "feetY": 69.59,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3565] [2025-07-24T00:33:24.100Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 69.59,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 1.591,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3566] [2025-07-24T00:33:24.100Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 1.591,
  "absDistanceToGround": 1.591,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3567] [2025-07-24T00:33:24.100Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 1.591,
  "velocityY": -15,
  "fallTime": 1597,
  "onGround": false
}
---
[3568] [2025-07-24T00:33:24.100Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 71.29,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3569] [2025-07-24T00:33:24.113Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 71.29,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3570] [2025-07-24T00:33:24.113Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.208,
    "z": 0
  }
}
---
[3571] [2025-07-24T00:33:24.113Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3572] [2025-07-24T00:33:24.114Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.208,
  "yBefore": 71.29,
  "yAfter": 71.08,
  "actualYMovement": -0.208
}
---
[3573] [2025-07-24T00:33:24.114Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 14
}
---
[3574] [2025-07-24T00:33:24.114Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 71.08,
    "z": -3.35
  },
  "feetY": 69.38,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3575] [2025-07-24T00:33:24.114Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 69.38,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 1.383,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3576] [2025-07-24T00:33:24.114Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 1.383,
  "absDistanceToGround": 1.383,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3577] [2025-07-24T00:33:24.114Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 1.383,
  "velocityY": -15,
  "fallTime": 1611,
  "onGround": false
}
---
[3578] [2025-07-24T00:33:24.114Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 71.08,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3579] [2025-07-24T00:33:24.114Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 23,
  "timestamp": 6505
}
---
[3580] [2025-07-24T00:33:24.114Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 22,
  "timestamp": 6505
}
---
[3581] [2025-07-24T00:33:24.123Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 71.08,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3582] [2025-07-24T00:33:24.124Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": -0.104,
    "z": 0
  }
}
---
[3583] [2025-07-24T00:33:24.124Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3584] [2025-07-24T00:33:24.124Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.104,
  "yBefore": 71.08,
  "yAfter": 70.98,
  "actualYMovement": -0.104
}
---
[3585] [2025-07-24T00:33:24.124Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 24
}
---
[3586] [2025-07-24T00:33:24.124Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 70.98,
    "z": -3.35
  },
  "feetY": 69.28,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3587] [2025-07-24T00:33:24.125Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 69.28,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 1.279,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3588] [2025-07-24T00:33:24.125Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 1.279,
  "absDistanceToGround": 1.279,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3589] [2025-07-24T00:33:24.125Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 1.279,
  "velocityY": -15,
  "fallTime": 1618,
  "onGround": false
}
---
[3590] [2025-07-24T00:33:24.125Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 70.98,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3591] [2025-07-24T00:33:24.132Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 6524
}
---
[3592] [2025-07-24T00:33:24.133Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": 5
  },
  "distance": "7.21",
  "reason": "Hors distance de rendu",
  "timestamp": 6525
}
---
[3593] [2025-07-24T00:33:24.133Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 70.98,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3594] [2025-07-24T00:33:24.133Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.208,
    "z": 0
  }
}
---
[3595] [2025-07-24T00:33:24.133Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3596] [2025-07-24T00:33:24.133Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.208,
  "yBefore": 70.98,
  "yAfter": 70.77,
  "actualYMovement": -0.209
}
---
[3597] [2025-07-24T00:33:24.134Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 34
}
---
[3598] [2025-07-24T00:33:24.134Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 70.77,
    "z": -3.35
  },
  "feetY": 69.07,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3599] [2025-07-24T00:33:24.134Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 69.07,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 1.07,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3600] [2025-07-24T00:33:24.134Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 1.07,
  "absDistanceToGround": 1.07,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3601] [2025-07-24T00:33:24.134Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 1.07,
  "velocityY": -15,
  "fallTime": 1632,
  "onGround": false
}
---
[3602] [2025-07-24T00:33:24.134Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 70.77,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3603] [2025-07-24T00:33:24.142Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 6534
}
---
[3604] [2025-07-24T00:33:24.142Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": -7
  },
  "distance": "7.21",
  "reason": "Hors distance de rendu",
  "timestamp": 6534
}
---
[3605] [2025-07-24T00:33:24.155Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 70.77,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3606] [2025-07-24T00:33:24.155Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.312,
    "z": 0
  }
}
---
[3607] [2025-07-24T00:33:24.155Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3608] [2025-07-24T00:33:24.156Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.312,
  "yBefore": 70.77,
  "yAfter": 70.46,
  "actualYMovement": -0.312
}
---
[3609] [2025-07-24T00:33:24.156Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 56
}
---
[3610] [2025-07-24T00:33:24.156Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 70.46,
    "z": -3.35
  },
  "feetY": 68.76,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3611] [2025-07-24T00:33:24.156Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68.76,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0.758,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3612] [2025-07-24T00:33:24.156Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 0.758,
  "absDistanceToGround": 0.758,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3613] [2025-07-24T00:33:24.156Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 0.758,
  "velocityY": -15,
  "fallTime": 1653,
  "onGround": false
}
---
[3614] [2025-07-24T00:33:24.156Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 70.46,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3615] [2025-07-24T00:33:24.156Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 21,
  "timestamp": 6547
}
---
[3616] [2025-07-24T00:33:24.156Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 20,
  "timestamp": 6547
}
---
[3617] [2025-07-24T00:33:24.171Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 6564
}
---
[3618] [2025-07-24T00:33:24.172Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -7
  },
  "distance": "8.49",
  "reason": "Hors distance de rendu",
  "timestamp": 6564
}
---
[3619] [2025-07-24T00:33:24.172Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 70.46,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3620] [2025-07-24T00:33:24.172Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": -0.209,
    "z": 0
  }
}
---
[3621] [2025-07-24T00:33:24.172Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3622] [2025-07-24T00:33:24.173Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.209,
  "yBefore": 70.46,
  "yAfter": 70.25,
  "actualYMovement": -0.209
}
---
[3623] [2025-07-24T00:33:24.173Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 73
}
---
[3624] [2025-07-24T00:33:24.173Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 70.25,
    "z": -3.35
  },
  "feetY": 68.55,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3625] [2025-07-24T00:33:24.173Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68.55,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0.55,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3626] [2025-07-24T00:33:24.173Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 0.55,
  "absDistanceToGround": 0.55,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3627] [2025-07-24T00:33:24.173Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 0.55,
  "velocityY": -15,
  "fallTime": 1667,
  "onGround": false
}
---
[3628] [2025-07-24T00:33:24.174Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 70.25,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3629] [2025-07-24T00:33:24.182Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 6573
}
---
[3630] [2025-07-24T00:33:24.182Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": 5
  },
  "distance": "8.49",
  "reason": "Hors distance de rendu",
  "timestamp": 6574
}
---
[3631] [2025-07-24T00:33:24.189Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 70.25,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3632] [2025-07-24T00:33:24.190Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": -0.312,
    "z": 0
  }
}
---
[3633] [2025-07-24T00:33:24.190Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3634] [2025-07-24T00:33:24.190Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.312,
  "yBefore": 70.25,
  "yAfter": 69.94,
  "actualYMovement": -0.312
}
---
[3635] [2025-07-24T00:33:24.190Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 90
}
---
[3636] [2025-07-24T00:33:24.190Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.94,
    "z": -3.35
  },
  "feetY": 68.24,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3637] [2025-07-24T00:33:24.190Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68.24,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0.237,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3638] [2025-07-24T00:33:24.190Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": 0.237,
  "absDistanceToGround": 0.237,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": true
}
---
[3639] [2025-07-24T00:33:24.191Z] [DEBUG] PHYSIQUE - CHUTE LIBRE
{
  "distanceToGround": 0.237,
  "velocityY": -15,
  "fallTime": 1687,
  "onGround": false
}
---
[3640] [2025-07-24T00:33:24.191Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.94,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3641] [2025-07-24T00:33:24.231Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.042,
  "position": {
    "x": -1.38,
    "y": 69.94,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3642] [2025-07-24T00:33:24.231Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": -15,
    "z": 0
  },
  "delta": 0.042,
  "calculatedMovement": {
    "x": 0,
    "y": -0.625,
    "z": 0
  }
}
---
[3643] [2025-07-24T00:33:24.231Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3644] [2025-07-24T00:33:24.231Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": -0.625,
  "yBefore": 69.94,
  "yAfter": 69.31,
  "actualYMovement": -0.625
}
---
[3645] [2025-07-24T00:33:24.231Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3646] [2025-07-24T00:33:24.232Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3647] [2025-07-24T00:33:24.232Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3648] [2025-07-24T00:33:24.232Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.31,
    "z": -3.35
  },
  "feetY": 67.61,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3649] [2025-07-24T00:33:24.232Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 67.61,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": -0.388,
  "targetGroundY": 69.7,
  "velocityY": -15,
  "currentOnGround": false
}
---
[3650] [2025-07-24T00:33:24.232Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": -15,
  "distanceToGround": -0.388,
  "absDistanceToGround": 0.388,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": false
}
---
[3651] [2025-07-24T00:33:24.232Z] [WARN] PHYSICS_UNDERGROUND_CORRECTION - Correction de la position du joueur sous le sol
{
  "oldY": 69.51,
  "newY": 69.51,
  "distanceToGround": -0.388
}
---
[3652] [2025-07-24T00:33:24.232Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.51,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3653] [2025-07-24T00:33:24.232Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 19,
  "timestamp": 6624
}
---
[3654] [2025-07-24T00:33:24.232Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 18,
  "timestamp": 6624
}
---
[3655] [2025-07-24T00:33:24.245Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 6637
}
---
[3656] [2025-07-24T00:33:24.246Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": 4
  },
  "distance": "8.60",
  "reason": "Hors distance de rendu",
  "timestamp": 6638
}
---
[3657] [2025-07-24T00:33:24.246Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 69.51,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3658] [2025-07-24T00:33:24.246Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3659] [2025-07-24T00:33:24.246Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3660] [2025-07-24T00:33:24.246Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.51,
  "yAfter": 69.51,
  "actualYMovement": 0
}
---
[3661] [2025-07-24T00:33:24.246Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 15
}
---
[3662] [2025-07-24T00:33:24.247Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.51,
    "z": -3.35
  },
  "feetY": 67.81,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3663] [2025-07-24T00:33:24.247Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 67.81,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": -0.194,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": false
}
---
[3664] [2025-07-24T00:33:24.247Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": -0.194,
  "absDistanceToGround": 0.194,
  "groundSnapTolerance": 0.1,
  "shouldSnap": false,
  "shouldFall": false
}
---
[3665] [2025-07-24T00:33:24.247Z] [WARN] PHYSICS_UNDERGROUND_CORRECTION - Correction de la position du joueur sous le sol
{
  "oldY": 69.6,
  "newY": 69.6,
  "distanceToGround": -0.194
}
---
[3666] [2025-07-24T00:33:24.248Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.6,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3667] [2025-07-24T00:33:24.256Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 6647
}
---
[3668] [2025-07-24T00:33:24.256Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -6
  },
  "distance": "8.60",
  "reason": "Hors distance de rendu",
  "timestamp": 6648
}
---
[3669] [2025-07-24T00:33:24.257Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 69.6,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": false,
  "flyMode": false
}
---
[3670] [2025-07-24T00:33:24.257Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3671] [2025-07-24T00:33:24.257Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3672] [2025-07-24T00:33:24.257Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.6,
  "yAfter": 69.6,
  "actualYMovement": 0
}
---
[3673] [2025-07-24T00:33:24.257Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 26
}
---
[3674] [2025-07-24T00:33:24.257Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.6,
    "z": -3.35
  },
  "feetY": 67.9,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3675] [2025-07-24T00:33:24.257Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 67.9,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": -0.097,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": false
}
---
[3676] [2025-07-24T00:33:24.257Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": -0.097,
  "absDistanceToGround": 0.097,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3677] [2025-07-24T00:33:24.257Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": -0.097,
  "onGround": true
}
---
[3678] [2025-07-24T00:33:24.257Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3679] [2025-07-24T00:33:24.265Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3680] [2025-07-24T00:33:24.266Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3681] [2025-07-24T00:33:24.266Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3682] [2025-07-24T00:33:24.266Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3683] [2025-07-24T00:33:24.266Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 35
}
---
[3684] [2025-07-24T00:33:24.266Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3685] [2025-07-24T00:33:24.266Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3686] [2025-07-24T00:33:24.266Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3687] [2025-07-24T00:33:24.266Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3688] [2025-07-24T00:33:24.267Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3689] [2025-07-24T00:33:24.267Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -1
  },
  "priority": 2,
  "queueRemaining": 17,
  "timestamp": 6659
}
---
[3690] [2025-07-24T00:33:24.267Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 16,
  "timestamp": 6659
}
---
[3691] [2025-07-24T00:33:24.286Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -1
  },
  "blocksReceived": 32768,
  "timestamp": 6677
}
---
[3692] [2025-07-24T00:33:24.301Z] [CHUNK] Chunk ajouté à la scène
{
  "position": {
    "x": -8,
    "z": -1
  },
  "distance": "7.00",
  "totalRendered": 160,
  "visible": true,
  "timestamp": 6692
}
---
[3693] [2025-07-24T00:33:24.301Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.035,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3694] [2025-07-24T00:33:24.301Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.035,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3695] [2025-07-24T00:33:24.301Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3696] [2025-07-24T00:33:24.301Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3697] [2025-07-24T00:33:24.302Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 71
}
---
[3698] [2025-07-24T00:33:24.303Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3699] [2025-07-24T00:33:24.303Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3700] [2025-07-24T00:33:24.303Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3701] [2025-07-24T00:33:24.303Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3702] [2025-07-24T00:33:24.303Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3703] [2025-07-24T00:33:24.313Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 6704
}
---
[3704] [2025-07-24T00:33:24.313Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": -9
  },
  "distance": "8.06",
  "reason": "Hors distance de rendu",
  "timestamp": 6705
}
---
[3705] [2025-07-24T00:33:24.314Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3706] [2025-07-24T00:33:24.314Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3707] [2025-07-24T00:33:24.314Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3708] [2025-07-24T00:33:24.314Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3709] [2025-07-24T00:33:24.314Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 83
}
---
[3710] [2025-07-24T00:33:24.314Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3711] [2025-07-24T00:33:24.314Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3712] [2025-07-24T00:33:24.314Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3713] [2025-07-24T00:33:24.315Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3714] [2025-07-24T00:33:24.315Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3715] [2025-07-24T00:33:24.342Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.028,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3716] [2025-07-24T00:33:24.342Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.028,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3717] [2025-07-24T00:33:24.342Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3718] [2025-07-24T00:33:24.342Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3719] [2025-07-24T00:33:24.343Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3720] [2025-07-24T00:33:24.343Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3721] [2025-07-24T00:33:24.343Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3722] [2025-07-24T00:33:24.343Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3723] [2025-07-24T00:33:24.343Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3724] [2025-07-24T00:33:24.343Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3725] [2025-07-24T00:33:24.343Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3726] [2025-07-24T00:33:24.343Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3727] [2025-07-24T00:33:24.343Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 15,
  "timestamp": 6735
}
---
[3728] [2025-07-24T00:33:24.343Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -1
  },
  "priority": 2,
  "queueRemaining": 14,
  "timestamp": 6735
}
---
[3729] [2025-07-24T00:33:24.357Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 6748
}
---
[3730] [2025-07-24T00:33:24.357Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": 7
  },
  "distance": "8.06",
  "reason": "Hors distance de rendu",
  "timestamp": 6749
}
---
[3731] [2025-07-24T00:33:24.359Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -1
  },
  "blocksReceived": 32768,
  "timestamp": 6750
}
---
[3732] [2025-07-24T00:33:24.359Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -1
  },
  "distance": "9.00",
  "reason": "Hors distance de rendu",
  "timestamp": 6750
}
---
[3733] [2025-07-24T00:33:24.363Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3734] [2025-07-24T00:33:24.363Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3735] [2025-07-24T00:33:24.363Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3736] [2025-07-24T00:33:24.363Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3737] [2025-07-24T00:33:24.363Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 21
}
---
[3738] [2025-07-24T00:33:24.363Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3739] [2025-07-24T00:33:24.364Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3740] [2025-07-24T00:33:24.364Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3741] [2025-07-24T00:33:24.364Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3742] [2025-07-24T00:33:24.364Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3743] [2025-07-24T00:33:24.372Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3744] [2025-07-24T00:33:24.372Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3745] [2025-07-24T00:33:24.372Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3746] [2025-07-24T00:33:24.373Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3747] [2025-07-24T00:33:24.373Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 31
}
---
[3748] [2025-07-24T00:33:24.373Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3749] [2025-07-24T00:33:24.373Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3750] [2025-07-24T00:33:24.373Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3751] [2025-07-24T00:33:24.373Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3752] [2025-07-24T00:33:24.373Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3753] [2025-07-24T00:33:24.380Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3754] [2025-07-24T00:33:24.381Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3755] [2025-07-24T00:33:24.381Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3756] [2025-07-24T00:33:24.381Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3757] [2025-07-24T00:33:24.381Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 39
}
---
[3758] [2025-07-24T00:33:24.381Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3759] [2025-07-24T00:33:24.381Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3760] [2025-07-24T00:33:24.381Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3761] [2025-07-24T00:33:24.382Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3762] [2025-07-24T00:33:24.382Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3763] [2025-07-24T00:33:24.382Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -2
  },
  "priority": 2,
  "queueRemaining": 13,
  "timestamp": 6774
}
---
[3764] [2025-07-24T00:33:24.382Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": 0
  },
  "priority": 2,
  "queueRemaining": 12,
  "timestamp": 6774
}
---
[3765] [2025-07-24T00:33:24.398Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3766] [2025-07-24T00:33:24.398Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3767] [2025-07-24T00:33:24.398Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3768] [2025-07-24T00:33:24.399Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3769] [2025-07-24T00:33:24.399Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 57
}
---
[3770] [2025-07-24T00:33:24.399Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3771] [2025-07-24T00:33:24.399Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3772] [2025-07-24T00:33:24.400Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3773] [2025-07-24T00:33:24.400Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3774] [2025-07-24T00:33:24.400Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3775] [2025-07-24T00:33:24.409Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": 0
  },
  "blocksReceived": 32768,
  "timestamp": 6801
}
---
[3776] [2025-07-24T00:33:24.410Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": 0
  },
  "distance": "7.07",
  "reason": "Hors distance de rendu",
  "timestamp": 6801
}
---
[3777] [2025-07-24T00:33:24.412Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -2
  },
  "blocksReceived": 32768,
  "timestamp": 6803
}
---
[3778] [2025-07-24T00:33:24.412Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -2
  },
  "distance": "7.07",
  "reason": "Hors distance de rendu",
  "timestamp": 6803
}
---
[3779] [2025-07-24T00:33:24.419Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3780] [2025-07-24T00:33:24.420Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3781] [2025-07-24T00:33:24.420Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3782] [2025-07-24T00:33:24.420Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3783] [2025-07-24T00:33:24.421Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 79
}
---
[3784] [2025-07-24T00:33:24.421Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3785] [2025-07-24T00:33:24.421Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3786] [2025-07-24T00:33:24.421Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3787] [2025-07-24T00:33:24.421Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3788] [2025-07-24T00:33:24.421Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3789] [2025-07-24T00:33:24.453Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.035,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3790] [2025-07-24T00:33:24.453Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.035,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3791] [2025-07-24T00:33:24.453Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3792] [2025-07-24T00:33:24.453Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3793] [2025-07-24T00:33:24.454Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3794] [2025-07-24T00:33:24.454Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3795] [2025-07-24T00:33:24.454Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3796] [2025-07-24T00:33:24.454Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3797] [2025-07-24T00:33:24.454Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3798] [2025-07-24T00:33:24.454Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3799] [2025-07-24T00:33:24.454Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3800] [2025-07-24T00:33:24.454Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3801] [2025-07-24T00:33:24.454Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 11,
  "timestamp": 6846
}
---
[3802] [2025-07-24T00:33:24.454Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 10,
  "timestamp": 6846
}
---
[3803] [2025-07-24T00:33:24.470Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 6861
}
---
[3804] [2025-07-24T00:33:24.470Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": 3
  },
  "distance": "7.21",
  "reason": "Hors distance de rendu",
  "timestamp": 6861
}
---
[3805] [2025-07-24T00:33:24.471Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 6863
}
---
[3806] [2025-07-24T00:33:24.471Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -5
  },
  "distance": "7.21",
  "reason": "Hors distance de rendu",
  "timestamp": 6863
}
---
[3807] [2025-07-24T00:33:24.474Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3808] [2025-07-24T00:33:24.474Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3809] [2025-07-24T00:33:24.474Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3810] [2025-07-24T00:33:24.474Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3811] [2025-07-24T00:33:24.474Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 20
}
---
[3812] [2025-07-24T00:33:24.475Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3813] [2025-07-24T00:33:24.475Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3814] [2025-07-24T00:33:24.475Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3815] [2025-07-24T00:33:24.475Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3816] [2025-07-24T00:33:24.475Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3817] [2025-07-24T00:33:24.481Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3818] [2025-07-24T00:33:24.481Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3819] [2025-07-24T00:33:24.481Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3820] [2025-07-24T00:33:24.481Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3821] [2025-07-24T00:33:24.482Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 28
}
---
[3822] [2025-07-24T00:33:24.482Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3823] [2025-07-24T00:33:24.482Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3824] [2025-07-24T00:33:24.482Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3825] [2025-07-24T00:33:24.482Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3826] [2025-07-24T00:33:24.482Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3827] [2025-07-24T00:33:24.494Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3828] [2025-07-24T00:33:24.494Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3829] [2025-07-24T00:33:24.495Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3830] [2025-07-24T00:33:24.495Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3831] [2025-07-24T00:33:24.495Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 41
}
---
[3832] [2025-07-24T00:33:24.495Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3833] [2025-07-24T00:33:24.495Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3834] [2025-07-24T00:33:24.495Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3835] [2025-07-24T00:33:24.495Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3836] [2025-07-24T00:33:24.495Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3837] [2025-07-24T00:33:24.496Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 9,
  "timestamp": 6888
}
---
[3838] [2025-07-24T00:33:24.496Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 8,
  "timestamp": 6888
}
---
[3839] [2025-07-24T00:33:24.508Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3840] [2025-07-24T00:33:24.508Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3841] [2025-07-24T00:33:24.508Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3842] [2025-07-24T00:33:24.509Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3843] [2025-07-24T00:33:24.509Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 55
}
---
[3844] [2025-07-24T00:33:24.509Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3845] [2025-07-24T00:33:24.509Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3846] [2025-07-24T00:33:24.509Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3847] [2025-07-24T00:33:24.510Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3848] [2025-07-24T00:33:24.510Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3849] [2025-07-24T00:33:24.518Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 6910
}
---
[3850] [2025-07-24T00:33:24.519Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -3
  },
  "distance": "7.28",
  "reason": "Hors distance de rendu",
  "timestamp": 6910
}
---
[3851] [2025-07-24T00:33:24.520Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 6912
}
---
[3852] [2025-07-24T00:33:24.521Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": 1
  },
  "distance": "7.28",
  "reason": "Hors distance de rendu",
  "timestamp": 6912
}
---
[3853] [2025-07-24T00:33:24.551Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.042,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3854] [2025-07-24T00:33:24.551Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.042,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3855] [2025-07-24T00:33:24.551Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3856] [2025-07-24T00:33:24.551Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3857] [2025-07-24T00:33:24.551Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 97
}
---
[3858] [2025-07-24T00:33:24.551Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3859] [2025-07-24T00:33:24.551Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3860] [2025-07-24T00:33:24.552Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3861] [2025-07-24T00:33:24.552Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3862] [2025-07-24T00:33:24.552Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3863] [2025-07-24T00:33:24.563Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3864] [2025-07-24T00:33:24.565Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3865] [2025-07-24T00:33:24.565Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3866] [2025-07-24T00:33:24.565Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3867] [2025-07-24T00:33:24.565Z] [DEBUG] DEBUT DETECTION SOL - Nouvelle recherche
{
  "x": -1.38,
  "z": -3.35,
  "cacheKey": "-2,-4",
  "worldAvailable": true,
  "getGroundHeightAtAvailable": true
}
---
[3868] [2025-07-24T00:33:24.565Z] [DEBUG] METHODE 1 - getGroundHeightAt
{
  "x": -1.38,
  "z": -3.35,
  "result": 67,
  "success": true
}
---
[3869] [2025-07-24T00:33:24.565Z] [DEBUG] RESULTAT FINAL DETECTION SOL
{
  "x": -1.38,
  "z": -3.35,
  "groundHeight": 67,
  "success": true,
  "cacheUpdated": true
}
---
[3870] [2025-07-24T00:33:24.565Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3871] [2025-07-24T00:33:24.565Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3872] [2025-07-24T00:33:24.565Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3873] [2025-07-24T00:33:24.565Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3874] [2025-07-24T00:33:24.565Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3875] [2025-07-24T00:33:24.565Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 7,
  "timestamp": 6957
}
---
[3876] [2025-07-24T00:33:24.565Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 6,
  "timestamp": 6957
}
---
[3877] [2025-07-24T00:33:24.573Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.007,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3878] [2025-07-24T00:33:24.573Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.007,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3879] [2025-07-24T00:33:24.573Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3880] [2025-07-24T00:33:24.573Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3881] [2025-07-24T00:33:24.573Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 8
}
---
[3882] [2025-07-24T00:33:24.573Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3883] [2025-07-24T00:33:24.574Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3884] [2025-07-24T00:33:24.574Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3885] [2025-07-24T00:33:24.574Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3886] [2025-07-24T00:33:24.574Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3887] [2025-07-24T00:33:24.582Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 6973
}
---
[3888] [2025-07-24T00:33:24.582Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": 2
  },
  "distance": "7.62",
  "reason": "Hors distance de rendu",
  "timestamp": 6974
}
---
[3889] [2025-07-24T00:33:24.583Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 6975
}
---
[3890] [2025-07-24T00:33:24.584Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -4
  },
  "distance": "7.62",
  "reason": "Hors distance de rendu",
  "timestamp": 6976
}
---
[3891] [2025-07-24T00:33:24.587Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.014,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3892] [2025-07-24T00:33:24.588Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.014,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3893] [2025-07-24T00:33:24.588Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3894] [2025-07-24T00:33:24.588Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3895] [2025-07-24T00:33:24.588Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 23
}
---
[3896] [2025-07-24T00:33:24.588Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3897] [2025-07-24T00:33:24.588Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3898] [2025-07-24T00:33:24.589Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3899] [2025-07-24T00:33:24.589Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3900] [2025-07-24T00:33:24.589Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3901] [2025-07-24T00:33:24.606Z] [DEBUG] === DEBUT UPDATE PLAYER ===
{
  "delta": 0.021,
  "position": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "velocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3902] [2025-07-24T00:33:24.606Z] [DEBUG] Calcul du mouvement
{
  "velocityBeforeScale": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "delta": 0.021,
  "calculatedMovement": {
    "x": 0,
    "y": 0,
    "z": 0
  }
}
---
[3903] [2025-07-24T00:33:24.606Z] [DEBUG] Apres mouvement horizontal
{
  "horizontalMovement": {
    "x": 0,
    "z": 0
  },
  "positionBefore": {
    "x": -1.38,
    "z": -3.35
  },
  "positionAfter": {
    "x": -1.38,
    "z": -3.35
  },
  "actualMovement": {
    "x": 0,
    "z": 0
  }
}
---
[3904] [2025-07-24T00:33:24.607Z] [DEBUG] Apres mouvement vertical
{
  "verticalMovement": 0,
  "yBefore": 69.7,
  "yAfter": 69.7,
  "actualYMovement": 0
}
---
[3905] [2025-07-24T00:33:24.607Z] [DEBUG] CACHE HIT - Utilisation du cache pour la detection du sol
{
  "cacheKey": "-2,-4",
  "cachedResult": 67,
  "timeSinceLastCheck": 42
}
---
[3906] [2025-07-24T00:33:24.607Z] [DEBUG] DETECTION DU SOL
{
  "playerPos": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "feetY": 68,
  "groundHeight": 67,
  "groundHeightFound": true
}
---
[3907] [2025-07-24T00:33:24.607Z] [DEBUG] CALCULS DE DISTANCE AU SOL
{
  "feetY": 68,
  "groundHeight": 67,
  "groundPlusOne": 68,
  "distanceToGround": 0,
  "targetGroundY": 69.7,
  "velocityY": 0,
  "currentOnGround": true
}
---
[3908] [2025-07-24T00:33:24.607Z] [DEBUG] VERIFICATION STABILISATION
{
  "velocityY": 0,
  "distanceToGround": 0,
  "absDistanceToGround": 0,
  "groundSnapTolerance": 0.1,
  "shouldSnap": true,
  "shouldFall": false
}
---
[3909] [2025-07-24T00:33:24.607Z] [INFO] PHYSICS_LANDING_SNAP - Joueur stabilise sur le sol
{
  "oldY": 68,
  "newY": 69.7,
  "targetGroundY": 69.7,
  "distanceToGround": 0,
  "onGround": true
}
---
[3910] [2025-07-24T00:33:24.607Z] [DEBUG] === FIN UPDATE PLAYER ===
{
  "finalPosition": {
    "x": -1.38,
    "y": 69.7,
    "z": -3.35
  },
  "finalVelocity": {
    "x": 0,
    "y": 0,
    "z": 0
  },
  "onGround": true,
  "flyMode": false
}
---
[3911] [2025-07-24T00:33:24.608Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 5,
  "timestamp": 6999
}
---
[3912] [2025-07-24T00:33:24.608Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 4,
  "timestamp": 6999
}
---
[3913] [2025-07-24T00:33:24.614Z] [INFO] Raccourci F11 - Téléchargement logs
{
  "trigger": "keyboard_shortcut"
}
---
[3914] [2025-07-24T00:33:24.614Z] [INFO] Téléchargement manuel des logs demandé
{
  "trigger": "manual_download",
  "totalLogs": 1000
}