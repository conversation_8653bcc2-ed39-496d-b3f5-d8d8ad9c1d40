INDEX : Logs du terminal console du serveur ci bas a partir de la ligne : 4
INDEX : Logs du navigateur a partir de la ligne : 

🧱 Chunk généré: (-5, 9)
🧱 Chunk généré: (-5, 10)
🧱 Chunk généré: (-5, 11)
🧱 Chunk généré: (-4, 9)
🧱 Chunk généré: (-4, 10)
🧱 Chunk généré: (-4, 11)
🧱 Chunk généré: (-3, 9)
🧱 Chunk généré: (-3, 10)
🧱 Chunk généré: (-3, 11)
🧱 Chunk généré: (-2, 9)
🧱 Chunk généré: (-2, 10)
🧱 Chunk généré: (-2, 11)
🧱 Chunk généré: (-1, 9)
🧱 Chunk généré: (-1, 10)
🧱 Chunk généré: (-1, 11)
🧱 Chunk généré: (-1, 12)
🧱 Chunk généré: (0, 10)
🧱 Chunk généré: (0, 11)
🧱 Chunk généré: (1, 9)
🧱 Chunk généré: (1, 10)
🧱 Chunk généré: (1, 11)
🧱 Chunk généré: (2, 9)
🧱 Chunk généré: (2, 10)
🧱 Chunk généré: (2, 11)
🧱 Chunk généré: (3, 9)
🧱 Chunk géné<PERSON>: (3, 10)
🧱 Chunk généré: (3, 11)
🧱 Chunk géné<PERSON>: (4, 9)
🧱 Chunk généré: (4, 10)
🧱 Chunk généré: (5, 8)
🧱 Chunk généré: (5, 9)
🧱 Chunk géné<PERSON>: (5, 10)
🧱 Chunk géné<PERSON>: (6, 8)
🧱 Chunk généré: (6, 9)
🧱 Chunk généré: (7, 7)
🧱 Chunk généré: (7, 8)
🧱 Chunk généré: (8, 6)
🔄 [TICK] Tick 1: broadcastInterval=2, shouldBroadcast=false, players=1
📊 Stats: 1 joueurs, 1 TPS, 7447.00ms/tick
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316352
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316372
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316402
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316419
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316436
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316456
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316488
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316520
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316553
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316586
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316619
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA', 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316636
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA', 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316669
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyA', 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316686
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316703
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316736
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316753
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316786
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316819
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316836
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316869
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316886
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1039999999999994 },
  sprint: false,
  timestamp: 1753373316919
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1059999999999994 },
  sprint: false,
  timestamp: 1753373316936
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.05, y: -1.1059999999999994 },
  sprint: false,
  timestamp: 1753373316969
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373316986
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317019
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317036
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317069
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317086
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317103
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317136
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317169
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317186
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317203
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317236
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317269
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317286
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317319
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317336
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317369
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317403
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317419
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317452
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317469
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317486
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317519
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317552
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317569
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317586
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317603
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317636
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317669
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317687
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317719
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317753
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317786
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317803
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317835
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317852
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317869
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317887
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317919
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🧱 Chunk généré: (-11, 1)
🧱 Chunk généré: (-10, -3)
🔄 [TICK] Tick 1: broadcastInterval=2, shouldBroadcast=false, players=1
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317952
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 2: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 2,
  firstPlayerPos: { x: '-9.14', y: '129.17', z: '24.88' }
}
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373317969
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318003
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318036
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 3: broadcastInterval=2, shouldBroadcast=false, players=1
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318069
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318086
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318103
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 4: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 4,
  firstPlayerPos: { x: '-8.88', y: '129.02', z: '24.37' }
}
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW', 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318136
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 5: broadcastInterval=2, shouldBroadcast=false, players=1
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW', 'KeyD' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318169
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318186
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318218
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 6: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 6,
  firstPlayerPos: { x: '-9.22', y: '128.76', z: '24.00' }
}
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318236
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318253
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 7: broadcastInterval=2, shouldBroadcast=false, players=1
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318286
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318319
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318336
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 8: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 8,
  firstPlayerPos: { x: '-9.75', y: '129.16', z: '23.74' }
}
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318370
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 9: broadcastInterval=2, shouldBroadcast=false, players=1
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318403
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318436
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
🔄 [TICK] Tick 10: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 10,
  firstPlayerPos: { x: '-10.31', y: '128.97', z: '23.46' }
}
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318469
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318486
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318519
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318536
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 12,
  firstPlayerPos: { x: '-10.86', y: '129.20', z: '23.19' }
}
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318569
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318602
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318620
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318653
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📨 [WEBSOCKET] Message reçu de 8vc5ac8ysmdhl73qc: type=client:input
📥 [SERVER] Input reçu de 8vc5ac8ysmdhl73qc: {
  keys: [ 'KeyW' ],
  rotation: { x: -1.048, y: -1.1139999999999994 },
  sprint: false,
  timestamp: 1753373318686
}
✅ [SERVER] Input appliqué au joueur 8vc5ac8ysmdhl73qc
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 14,
  firstPlayerPos: { x: '-11.38', y: '129.10', z: '22.93' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 16,
  firstPlayerPos: { x: '-11.91', y: '128.86', z: '22.68' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 18,
  firstPlayerPos: { x: '-12.48', y: '129.16', z: '22.40' }
}
🔄 [TICK] Tick 1: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 2: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 2,
  firstPlayerPos: { x: '-13.04', y: '128.97', z: '22.12' }
}
🔄 [TICK] Tick 3: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 4: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 4,
  firstPlayerPos: { x: '-13.60', y: '129.20', z: '21.84' }
}
🔄 [TICK] Tick 5: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 6: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 6,
  firstPlayerPos: { x: '-14.16', y: '129.09', z: '21.57' }
}
🔄 [TICK] Tick 7: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 8: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 8,
  firstPlayerPos: { x: '-14.72', y: '128.82', z: '21.30' }
}
🔄 [TICK] Tick 9: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 10: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 10,
  firstPlayerPos: { x: '-15.28', y: '129.16', z: '21.02' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 12,
  firstPlayerPos: { x: '-15.84', y: '128.97', z: '20.74' }
}
🧱 Chunk généré: (-12, 1)
🧱 Chunk généré: (-11, -3)
🧱 Chunk généré: (-11, -2)
🧱 Chunk généré: (-11, -1)
🧱 Chunk généré: (-11, 0)
🧱 Chunk généré: (-11, 3)
🧱 Chunk généré: (-11, 4)
🧱 Chunk généré: (-11, 5)
🧱 Chunk généré: (-10, -5)
🧱 Chunk généré: (-10, -4)
🧱 Chunk généré: (-10, 7)
🧱 Chunk généré: (-9, -6)
🔄 [TICK] Tick 1: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 2: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 2,
  firstPlayerPos: { x: '-17.66', y: '129.17', z: '19.85' }
}
🔄 [TICK] Tick 3: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 4: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 4,
  firstPlayerPos: { x: '-18.21', y: '128.98', z: '19.58' }
}
🔄 [TICK] Tick 5: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 6: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 6,
  firstPlayerPos: { x: '-18.78', y: '129.20', z: '19.30' }
}
🔄 [TICK] Tick 7: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 8: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 8,
  firstPlayerPos: { x: '-19.34', y: '129.08', z: '19.02' }
}
🔄 [TICK] Tick 9: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 10: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 10,
  firstPlayerPos: { x: '-19.90', y: '128.82', z: '18.75' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 12,
  firstPlayerPos: { x: '-20.45', y: '129.16', z: '18.48' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 14,
  firstPlayerPos: { x: '-21.00', y: '128.98', z: '18.21' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 16,
  firstPlayerPos: { x: '-21.57', y: '129.20', z: '17.93' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 18,
  firstPlayerPos: { x: '-22.14', y: '129.08', z: '17.65' }
}
🔄 [TICK] Tick 1: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 2: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 2,
  firstPlayerPos: { x: '-22.69', y: '128.82', z: '17.38' }
}
🔄 [TICK] Tick 3: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 4: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 4,
  firstPlayerPos: { x: '-23.25', y: '129.16', z: '17.10' }
}
🔄 [TICK] Tick 5: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 6: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 6,
  firstPlayerPos: { x: '-23.81', y: '128.97', z: '16.83' }
}
🔄 [TICK] Tick 7: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 8: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 8,
  firstPlayerPos: { x: '-24.37', y: '129.20', z: '16.55' }
}
🔄 [TICK] Tick 9: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 10: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 10,
  firstPlayerPos: { x: '-24.92', y: '129.09', z: '16.28' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 12,
  firstPlayerPos: { x: '-25.49', y: '128.82', z: '16.00' }
}
🧱 Chunk généré: (-12, 0)
🧱 Chunk généré: (-11, -4)
🧱 Chunk généré: (-10, -6)
🧱 Chunk généré: (-9, -7)
🧱 Chunk généré: (-8, -8)
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 14,
  firstPlayerPos: { x: '-26.33', y: '129.04', z: '15.59' }
}
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 16,
  firstPlayerPos: { x: '-26.88', y: '128.78', z: '15.32' }
}
🔄 [TICK] Tick 1: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 2: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 2,
  firstPlayerPos: { x: '-27.45', y: '129.16', z: '15.04' }
}
🔄 [TICK] Tick 3: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 4: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 4,
  firstPlayerPos: { x: '-28.01', y: '128.97', z: '14.77' }
}
🔄 [TICK] Tick 5: broadcastInterval=2, shouldBroadcast=false, players=1
🔄 [TICK] Tick 6: broadcastInterval=2, shouldBroadcast=true, players=1
📡 [BROADCAST] Diffusion état du monde à 1 joueurs: {
  playersCount: 1,
  tick: 6,
  firstPlayerPos: { x: '-28.57', y: '129.20', z: '14.49' }
}
🔄 [TICK] Tick 7: broadcastInterval=2, shouldBroadcast=false, players=1

🛑 Arrêt du serveur...
🛑 GameManager arrêté
🔌 Fermeture du serveur WebSocket...
PS C:\Users\<USER>\Desktop\interface\JScraft - Copie> ✅ Serveur arrêté proprement