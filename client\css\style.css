body {
    margin: 0;
    overflow: hidden;
    font-family: Arial, sans-serif;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

#game {
    display: block;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 0;
}

#ui {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

#crosshair {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
    pointer-events: none;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

#instructions {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
    pointer-events: none;
}

#inventory {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: grid;
    grid-template-columns: repeat(9, 40px);
    gap: 2px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
    pointer-events: auto;
}

.inventory-slot {
    width: 40px;
    height: 40px;
    background: #2a2a2a;
    border: 1px solid #666;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.inventory-slot:hover {
    background: #4a4a4a;
    border-color: #888;
}

.inventory-slot.selected {
    border: 2px solid white;
    background: #4a4a4a;
}

#commands {
    position: fixed;
    top: 20px;
    right: 20px;
    color: white;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border-radius: 8px;
    font-size: 12px;
    pointer-events: none;
    max-width: 250px;
}

#commands h3 {
    margin: 0 0 10px 0;
    color: #ffff00;
    font-size: 14px;
}

#commands p {
    margin: 5px 0;
    line-height: 1.4;
}

#commands strong {
    color: #00ff00;
}

#status {
    position: fixed;
    bottom: 20px;
    left: 20px;
    color: white;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border-radius: 8px;
    font-size: 12px;
    pointer-events: none;
    min-width: 200px;
}

#status div {
    margin: 5px 0;
    line-height: 1.4;
}

#fly-mode span {
    color: #ff0000;
    font-weight: bold;
}

#fly-mode.active span {
    color: #00ff00;
}

#position span, #biome span, #fps span {
    color: #ffff00;
    font-weight: bold;
}

#notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    pointer-events: none;
}

.notification {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    font-size: 14px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    max-width: 300px;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification.info {
    border-left: 4px solid #00ff00;
}

.notification.warning {
    border-left: 4px solid #ffff00;
}

.notification.error {
    border-left: 4px solid #ff0000;
}

#notifications {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 1001;
}

.notification {
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 20px;
    margin: 5px 0;
    border-radius: 6px;
    border-left: 4px solid #00ff00;
    font-size: 14px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    max-width: 300px;
    word-wrap: break-word;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification.info {
    border-left-color: #00bfff;
}

.notification.warning {
    border-left-color: #ffa500;
}

.notification.error {
    border-left-color: #ff4444;
} 

/* BOUTONS DE SECOURS */
#emergency-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: auto;
    z-index: 1001;
}

#spawn-button, #reset-physics-button {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    min-width: 180px;
    text-align: center;
}

#spawn-button:hover, #reset-physics-button:hover {
    background: linear-gradient(135deg, #ff5252, #d63031);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

#spawn-button:active, #reset-physics-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

#reset-physics-button {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
}

#reset-physics-button:hover {
    background: linear-gradient(135deg, #5a9cff, #0770c4);
}

/* Animation de pulsation pour attirer l'attention */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.emergency-pulse {
    animation: pulse 2s infinite;
}

/* MENU D'OPTIONS (F1) */
#options-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.options-content {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-radius: 15px;
    padding: 30px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    color: white;
}

.options-content h2 {
    text-align: center;
    margin: 0 0 25px 0;
    color: #ecf0f1;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.options-section {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.options-section h3 {
    margin: 0 0 15px 0;
    color: #3498db;
    font-size: 18px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.option-item label {
    min-width: 150px;
    font-weight: bold;
    color: #ecf0f1;
}

.option-item input[type="range"] {
    flex: 1;
    height: 6px;
    background: #34495e;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.option-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.option-item input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.option-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #3498db;
    cursor: pointer;
}

.option-item span {
    min-width: 80px;
    text-align: right;
    color: #f39c12;
    font-weight: bold;
}

.options-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.options-buttons button {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

#reset-options {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

#reset-options:hover {
    background: linear-gradient(135deg, #d63031, #a71e1e);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

#close-options {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

#close-options:hover {
    background: linear-gradient(135deg, #2ecc71, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
}

#download-logs-button {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    min-width: 180px;
    text-align: center;
}

#download-logs-button:hover {
    background: linear-gradient(135deg, #a569bd, #7d3c98);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

#download-logs-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Scrollbar personnalisée pour le menu d'options */
.options-content::-webkit-scrollbar {
    width: 8px;
}

.options-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.options-content::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 4px;
}

.options-content::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}

/* Styles pour les nouveaux éléments du menu d'options */
.option-item select {
    flex: 1;
    padding: 8px 12px;
    background: #34495e;
    color: #ecf0f1;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    outline: none;
}

.option-item select:focus {
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.option-item select option {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 8px;
}

.option-item input[type="text"] {
    flex: 1;
    padding: 8px 12px;
    background: #34495e;
    color: #ecf0f1;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    font-weight: bold;
}

.option-item input[type="text"][readonly] {
    background: #2c3e50;
    cursor: not-allowed;
    opacity: 0.8;
}

/* Empêcher la sélection de texte dans l'interface utilisateur */
#ui, #ui * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Empêcher spécifiquement la sélection dans l'inventaire et la hotbar */
.inventory-slot, .hotbar-slot, 
.inventory-slot *, .hotbar-slot *,
#inventory, #inventory *,
.hotbar, .hotbar * {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Empêcher la sélection dans tous les éléments de statut et commandes */
#status, #status *,
#commands, #commands *,
#crosshair, #crosshair *,
#instructions, #instructions *,
#notifications, #notifications * {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Permettre la sélection uniquement dans les champs de texte du menu d'options */
#options-menu input[type="text"], 
#options-menu input[type="range"], 
#options-menu select,
#options-menu textarea {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* Empêcher la sélection dans le canvas */
#game {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Empêcher la sélection sur tout le body par défaut */
body {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Styles pour les raccourcis d'urgence */
.options-section h3[data-section="emergency"] {
    color: #e74c3c;
    border-bottom-color: #e74c3c;
}

/* Animation pour les modes de couleur */
.color-mode-transition {
    transition: filter 0.5s ease-in-out;
}

/* Styles pour les différents modes de couleur */
.color-mode-vibrant {
    filter: saturate(150%) contrast(110%) brightness(105%);
}

.color-mode-moody {
    filter: saturate(80%) contrast(120%) brightness(85%) sepia(15%);
}

.color-mode-classic {
    filter: saturate(90%) contrast(105%) hue-rotate(-5deg);
}

.color-mode-warm {
    filter: saturate(110%) brightness(105%) sepia(20%) hue-rotate(10deg);
}

.color-mode-cool {
    filter: saturate(110%) brightness(95%) hue-rotate(-15deg) contrast(105%);
}/* =
==== RESPONSIVITÉ ET MEDIA QUERIES ===== */

/* Styles pour les écrans très petits (mobiles en portrait) */
@media screen and (max-width: 480px) {
    #instructions {
        font-size: 11px;
        padding: 6px 12px;
        top: 10px;
        left: 10px;
        right: 10px;
        transform: none;
        max-width: calc(100vw - 20px);
    }
    
    #commands {
        font-size: 9px;
        padding: 8px;
        top: 10px;
        right: 10px;
        max-width: 150px;
    }
    
    #commands h3 {
        font-size: 11px;
        margin-bottom: 6px;
    }
    
    #status {
        font-size: 9px;
        padding: 8px;
        bottom: 10px;
        left: 10px;
        min-width: 120px;
    }
    
    #crosshair {
        font-size: 18px;
    }
    
    #inventory {
        grid-template-columns: repeat(5, 32px);
        gap: 1px;
        padding: 6px;
        bottom: 10px;
    }
    
    .inventory-slot {
        width: 32px;
        height: 32px;
        font-size: 10px;
    }
    
    .options-content {
        padding: 20px;
        max-width: 90vw;
        max-height: 90vh;
    }
    
    .option-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .option-item label {
        min-width: auto;
        text-align: left;
    }
}

/* Styles pour les écrans petits (mobiles en paysage et petites tablettes) */
@media screen and (min-width: 481px) and (max-width: 768px) {
    #instructions {
        font-size: 12px;
        padding: 8px 15px;
        top: 15px;
    }
    
    #commands {
        font-size: 10px;
        padding: 10px;
        max-width: 180px;
    }
    
    #commands h3 {
        font-size: 12px;
    }
    
    #status {
        font-size: 10px;
        padding: 10px;
        min-width: 150px;
    }
    
    #crosshair {
        font-size: 20px;
    }
    
    #inventory {
        grid-template-columns: repeat(7, 36px);
        gap: 1px;
        padding: 8px;
    }
    
    .inventory-slot {
        width: 36px;
        height: 36px;
        font-size: 11px;
    }
    
    .options-content {
        padding: 25px;
        max-width: 85vw;
    }
}

/* Styles pour les écrans moyens (tablettes) */
@media screen and (min-width: 769px) and (max-width: 1024px) {
    #instructions {
        font-size: 13px;
        padding: 9px 18px;
    }
    
    #commands {
        font-size: 11px;
        padding: 12px;
        max-width: 220px;
    }
    
    #status {
        font-size: 11px;
        padding: 12px;
        min-width: 180px;
    }
    
    #crosshair {
        font-size: 22px;
    }
    
    #inventory {
        grid-template-columns: repeat(8, 38px);
        gap: 2px;
        padding: 9px;
    }
    
    .inventory-slot {
        width: 38px;
        height: 38px;
        font-size: 11px;
    }
}

/* Styles pour les écrans ultra-larges */
@media screen and (min-width: 1920px) {
    #instructions {
        font-size: 16px;
        padding: 12px 24px;
    }
    
    #commands {
        font-size: 14px;
        padding: 18px;
        max-width: 300px;
    }
    
    #commands h3 {
        font-size: 16px;
    }
    
    #status {
        font-size: 14px;
        padding: 18px;
        min-width: 240px;
    }
    
    #crosshair {
        font-size: 28px;
    }
    
    #inventory {
        grid-template-columns: repeat(9, 45px);
        gap: 3px;
        padding: 12px;
    }
    
    .inventory-slot {
        width: 45px;
        height: 45px;
        font-size: 14px;
    }
}

/* Styles pour les écrans en mode portrait */
@media screen and (orientation: portrait) {
    #commands {
        top: auto;
        bottom: 80px;
        right: 10px;
        max-width: 200px;
    }
    
    #notifications {
        top: 10px;
        right: 10px;
        transform: none;
    }
}

/* Styles pour les écrans avec ratio d'aspect très large */
@media screen and (min-aspect-ratio: 21/9) {
    #instructions {
        max-width: 50vw;
    }
    
    #commands {
        max-width: 300px;
    }
    
    #status {
        min-width: 250px;
    }
}

/* Styles pour les écrans avec ratio d'aspect très étroit */
@media screen and (max-aspect-ratio: 4/3) {
    #inventory {
        grid-template-columns: repeat(6, 35px);
        bottom: 60px;
    }
    
    #status {
        bottom: 60px;
    }
}

/* Animations fluides pour les changements de taille */
#ui > * {
    transition: all 0.3s ease;
}

/* Optimisations pour les performances sur mobile */
@media screen and (max-width: 768px) {
    * {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
    
    #game {
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
}

/* Styles pour le mode sombre forcé */
@media (prefers-color-scheme: dark) {
    .options-content {
        background: linear-gradient(135deg, #1a252f, #2c3e50);
    }
}

/* Styles pour les préférences de mouvement réduit */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .color-mode-transition {
        transition: none !important;
    }
}

/* Styles pour l'impression (au cas où) */
@media print {
    #game, #ui {
        display: none;
    }
    
    body::after {
        content: "Minecraft JS - Jeu en ligne non imprimable";
        display: block;
        text-align: center;
        padding: 50px;
        font-size: 24px;
    }
}