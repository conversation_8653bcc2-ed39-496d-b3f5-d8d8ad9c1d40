/* Interface de minage et inventaire */

#crosshair {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 1000;
}

#crosshair .horizontal {
    position: absolute;
    top: 50%;
    left: 25%;
    width: 50%;
    height: 2px;
    background: white;
    transform: translateY(-50%);
    box-shadow: 0 0 2px black;
}

#crosshair .vertical {
    position: absolute;
    left: 50%;
    top: 25%;
    width: 2px;
    height: 50%;
    background: white;
    transform: translateX(-50%);
    box-shadow: 0 0 2px black;
}

#mining-progress {
    position: fixed;
    top: 60%;
    left: 50%;
    width: 200px;
    height: 20px;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #fff;
    border-radius: 10px;
    display: none;
    z-index: 1000;
}

#mining-progress-bar {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    border-radius: 8px;
    transition: width 0.1s ease;
}

#hotbar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 2px;
    background: rgba(0, 0, 0, 0.7);
    padding: 8px;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    z-index: 1000;
}

.hotbar-slot {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    line-height: 1.2;
}

.hotbar-slot:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.hotbar-slot.selected {
    border-color: #fff;
    background: rgba(255, 255, 255, 0.2);
}

#inventory {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 450px;
    height: 350px;
    background: rgba(0, 0, 0, 0.9);
    border: 3px solid #fff;
    border-radius: 15px;
    padding: 20px;
    display: none;
    z-index: 2000;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.8);
}

#inventory h3 {
    color: white;
    margin: 0 0 20px 0;
    text-align: center;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

#inventory-grid {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    gap: 3px;
    height: 240px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.inventory-slot {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 9px;
    cursor: pointer;
    min-height: 40px;
    transition: all 0.2s ease;
    text-align: center;
    line-height: 1.1;
}

.inventory-slot:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

.inventory-slot.has-item {
    background: rgba(100, 150, 200, 0.3);
    border-color: rgba(100, 150, 200, 0.6);
}

/* Animation de minage */
@keyframes mining-swing {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(-15deg); }
    50% { transform: rotate(0deg); }
    75% { transform: rotate(15deg); }
    100% { transform: rotate(0deg); }
}

.mining-animation {
    animation: mining-swing 0.3s ease-in-out infinite;
}

/* Notification de minage */
.mining-notification {
    position: fixed;
    top: 70%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    border: 2px solid #4CAF50;
    z-index: 1500;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateX(-50%) translateY(20px); }
    20% { opacity: 1; transform: translateX(-50%) translateY(0px); }
    80% { opacity: 1; transform: translateX(-50%) translateY(0px); }
    100% { opacity: 0; transform: translateX(-50%) translateY(-20px); }
}/*
 Protection contre la sélection de texte dans l'inventaire et la hotbar */
.hotbar-slot, .hotbar-slot *,
.inventory-slot, .inventory-slot *,
.hotbar, .hotbar *,
#mining-progress, #mining-progress *,
.mining-ui, .mining-ui * {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Empêcher la sélection sur les éléments de texte dans les slots */
.hotbar-slot span, .hotbar-slot div,
.inventory-slot span, .inventory-slot div,
.item-count, .item-name, .item-tooltip {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Empêcher le drag and drop qui peut causer des sélections */
.hotbar-slot, .inventory-slot {
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-select: none !important;
}

/* Empêcher les interactions de sélection sur mobile */
.hotbar-slot, .inventory-slot {
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
}