# Script PowerShell pour arreter tous les serveurs Node.js et Python
# Utilisation: .\kill-servers.ps1

Write-Host "Arret de tous les serveurs Node.js et Python..." -ForegroundColor Yellow

# Fonction pour arreter les processus en toute securite
function Stop-ProcessesSafely {
    param(
        [string]$ProcessName,
        [string]$DisplayName
    )
    
    try {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "Processus $DisplayName trouves: $($processes.Count)" -ForegroundColor Cyan
            foreach ($process in $processes) {
                Write-Host "  Arret du processus $DisplayName (PID: $($process.Id))" -ForegroundColor Gray
                try {
                    $process.Kill()
                    Write-Host "  Processus $($process.Id) arrete avec succes" -ForegroundColor Green
                } catch {
                    Write-Host "  Erreur lors de l'arret du processus $($process.Id): $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "Aucun processus $DisplayName en cours d'execution" -ForegroundColor Gray
        }
    } catch {
        Write-Host "Erreur lors de la recherche des processus $DisplayName : $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Fonction pour arreter les serveurs HTTP Python specifiquement
function Stop-PythonHTTPServers {
    Write-Host "Recherche des serveurs HTTP Python..." -ForegroundColor Cyan
    
    try {
        # Rechercher les processus Python qui utilisent http.server
        $pythonProcesses = Get-WmiObject Win32_Process | Where-Object { 
            $_.Name -eq "python.exe" -and 
            $_.CommandLine -like "*http.server*" 
        }
        
        if ($pythonProcesses) {
            Write-Host "Serveurs HTTP Python trouves: $($pythonProcesses.Count)" -ForegroundColor Cyan
            foreach ($process in $pythonProcesses) {
                Write-Host "  Arret du serveur HTTP Python (PID: $($process.ProcessId))" -ForegroundColor Gray
                Write-Host "     Commande: $($process.CommandLine)" -ForegroundColor DarkGray
                try {
                    Stop-Process -Id $process.ProcessId -Force
                    Write-Host "  Serveur HTTP Python $($process.ProcessId) arrete avec succes" -ForegroundColor Green
                } catch {
                    Write-Host "  Erreur lors de l'arret du serveur: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "Aucun serveur HTTP Python en cours d'execution" -ForegroundColor Gray
        }
    } catch {
        Write-Host "Erreur lors de la recherche des serveurs HTTP Python: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Fonction pour liberer les ports specifiques
function Stop-ProcessesOnPorts {
    param(
        [int[]]$Ports
    )
    
    Write-Host "Verification des ports specifiques..." -ForegroundColor Cyan
    
    foreach ($port in $Ports) {
        try {
            $connections = netstat -ano | Select-String ":$port " | Select-String "LISTENING"
            if ($connections) {
                foreach ($connection in $connections) {
                    $parts = $connection.ToString().Split() | Where-Object { $_ -ne "" }
                    if ($parts.Length -ge 5) {
                        $pid = $parts[-1]
                        if ($pid -match "^\d+$") {
                            try {
                                $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                                if ($process) {
                                    Write-Host "  Arret du processus sur le port $port (PID: $pid, Nom: $($process.Name))" -ForegroundColor Gray
                                    Stop-Process -Id $pid -Force
                                    Write-Host "  Processus $pid arrete avec succes" -ForegroundColor Green
                                }
                            } catch {
                                Write-Host "  Erreur lors de l'arret du processus $pid : $($_.Exception.Message)" -ForegroundColor Red
                            }
                        }
                    }
                }
            } else {
                Write-Host "Aucun processus trouve sur le port $port" -ForegroundColor Gray
            }
        } catch {
            Write-Host "Erreur lors de la verification du port $port : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Execution principale
Write-Host "Debut de l'arret des serveurs..." -ForegroundColor Yellow
Write-Host "" # Ligne vide

# Arreter les processus Node.js
Stop-ProcessesSafely -ProcessName "node" -DisplayName "Node.js"
Write-Host "" # Ligne vide

# Arreter tous les processus Python
Stop-ProcessesSafely -ProcessName "python" -DisplayName "Python"
Write-Host "" # Ligne vide

# Arreter specifiquement les serveurs HTTP Python
Stop-PythonHTTPServers
Write-Host "" # Ligne vide

# Arreter les processus sur les ports couramment utilises pour le developpement
$commonPorts = @(3000, 8000, 8001, 8002, 8080, 5000, 4200, 9000)
Stop-ProcessesOnPorts -Ports $commonPorts
Write-Host "" # Ligne vide

# Verification finale
Write-Host "Verification finale..." -ForegroundColor Yellow
$nodeProcesses = Get-Process -Name node -ErrorAction SilentlyContinue
$pythonProcesses = Get-Process -Name python -ErrorAction SilentlyContinue

if ($nodeProcesses) {
    Write-Host "$($nodeProcesses.Count) processus Node.js encore actifs" -ForegroundColor Yellow
} else {
    Write-Host "Aucun processus Node.js actif" -ForegroundColor Green
}

if ($pythonProcesses) {
    Write-Host "$($pythonProcesses.Count) processus Python encore actifs" -ForegroundColor Yellow
} else {
    Write-Host "Aucun processus Python actif" -ForegroundColor Green
}

Write-Host "" # Ligne vide
Write-Host "Script termine !" -ForegroundColor Green
Write-Host "Conseil: Utilisez ce script avant de redemarrer vos serveurs de developpement" -ForegroundColor Cyan

# Pause pour permettre a l'utilisateur de voir les resultats
Read-Host "Appuyez sur Entree pour continuer..."