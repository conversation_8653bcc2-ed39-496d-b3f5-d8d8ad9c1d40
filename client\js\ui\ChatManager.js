// client/js/ui/ChatManager.js
// Gestionnaire du chat côté client

export class ChatManager {
    constructor() {
        this.isOpen = false;
        this.messages = [];
        this.maxMessages = 100;
        this.messageHistory = [];
        this.historyIndex = -1;
        
        // Éléments DOM
        this.chatContainer = null;
        this.chatMessages = null;
        this.chatInput = null;
        this.chatInputContainer = null;
        
        // Configuration
        this.fadeTimeout = 5000; // 5 secondes avant que les messages disparaissent
        this.maxMessageLength = 100;
        
        this.initializeElements();
        this.setupEventListeners();
        
        console.log('💬 ChatManager initialisé');
    }
    
    initializeElements() {
        this.chatContainer = document.getElementById('chat-container');
        this.chatMessages = document.getElementById('chat-messages');
        this.chatInput = document.getElementById('chat-input');
        this.chatInputContainer = document.getElementById('chat-input-container');
        
        if (!this.chatContainer || !this.chatMessages || !this.chatInput) {
            console.warn('⚠️ Éléments de chat manquants dans le DOM');
            return;
        }
        
        // Configurer l'input
        this.chatInput.maxLength = this.maxMessageLength;
        this.chatInput.placeholder = 'Tapez votre message...';
    }
    
    setupEventListeners() {
        if (!this.chatInput) return;
        
        // Gestion des touches dans l'input
        this.chatInput.addEventListener('keydown', (e) => {
            e.stopPropagation(); // Empêcher la propagation vers les contrôles du jeu
            
            switch (e.code) {
                case 'Enter':
                    e.preventDefault();
                    this.sendMessage();
                    break;
                    
                case 'Escape':
                    e.preventDefault();
                    this.close();
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    this.navigateHistory(-1);
                    break;
                    
                case 'ArrowDown':
                    e.preventDefault();
                    this.navigateHistory(1);
                    break;
                    
                case 'Tab':
                    e.preventDefault();
                    this.autoComplete();
                    break;
            }
        });
        
        // Empêcher la propagation des autres événements
        this.chatInput.addEventListener('keyup', (e) => e.stopPropagation());
        this.chatInput.addEventListener('keypress', (e) => e.stopPropagation());
        
        // Gestion de la perte de focus
        this.chatInput.addEventListener('blur', () => {
            // Fermer le chat si on perd le focus (sauf si on clique dans le chat)
            setTimeout(() => {
                if (!this.chatContainer.contains(document.activeElement)) {
                    this.close();
                }
            }, 100);
        });
        
        // Clic sur le conteneur de chat pour le garder ouvert
        if (this.chatContainer) {
            this.chatContainer.addEventListener('click', (e) => {
                e.stopPropagation();
                if (this.isOpen) {
                    this.chatInput.focus();
                }
            });
        }
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        if (!this.chatContainer || this.isOpen) return;
        
        this.isOpen = true;
        this.chatContainer.classList.remove('hidden');
        this.chatContainer.classList.add('chat-open');
        
        // Afficher tous les messages
        this.showAllMessages();
        
        // Focus sur l'input
        if (this.chatInput) {
            this.chatInput.focus();
            this.chatInput.value = '';
        }
        
        // Réinitialiser l'index de l'historique
        this.historyIndex = -1;
        
        console.log('💬 Chat ouvert');
    }
    
    close() {
        if (!this.chatContainer || !this.isOpen) return;
        
        this.isOpen = false;
        this.chatContainer.classList.remove('chat-open');
        
        // Cacher le chat après un délai
        setTimeout(() => {
            if (!this.isOpen) {
                this.chatContainer.classList.add('hidden');
                this.fadeMessages();
            }
        }, 100);
        
        // Enlever le focus de l'input
        if (this.chatInput) {
            this.chatInput.blur();
        }
        
        console.log('💬 Chat fermé');
    }
    
    sendMessage() {
        if (!this.chatInput) return;
        
        const message = this.chatInput.value.trim();
        if (!message) return;
        
        // Ajouter à l'historique
        this.messageHistory.unshift(message);
        if (this.messageHistory.length > 20) {
            this.messageHistory.pop();
        }
        
        // Envoyer au serveur via le jeu
        if (window.game && window.game.sendChatMessage) {
            window.game.sendChatMessage(message);
        }
        
        // Vider l'input et fermer le chat
        this.chatInput.value = '';
        this.close();
        
        console.log('💬 Message envoyé:', message);
    }
    
    addMessage(playerId, message, timestamp, isSystem = false) {
        const messageObj = {
            id: Date.now() + Math.random(),
            playerId,
            message,
            timestamp: timestamp || Date.now(),
            isSystem
        };
        
        this.messages.push(messageObj);
        
        // Limiter le nombre de messages
        if (this.messages.length > this.maxMessages) {
            this.messages.shift();
        }
        
        // Afficher le message
        this.displayMessage(messageObj);
        
        // Auto-scroll vers le bas
        this.scrollToBottom();
        
        // Programmer la disparition si le chat n'est pas ouvert
        if (!this.isOpen) {
            this.scheduleMessageFade(messageObj.id);
        }
    }
    
    addSystemMessage(message) {
        this.addMessage('Système', message, Date.now(), true);
    }
    
    displayMessage(messageObj) {
        if (!this.chatMessages) return;
        
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${messageObj.isSystem ? 'system-message' : 'player-message'}`;
        messageElement.dataset.messageId = messageObj.id;
        
        // Formater le timestamp
        const time = new Date(messageObj.timestamp).toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        if (messageObj.isSystem) {
            messageElement.innerHTML = `
                <span class="message-time">[${time}]</span>
                <span class="message-content system">${this.escapeHtml(messageObj.message)}</span>
            `;
        } else {
            messageElement.innerHTML = `
                <span class="message-time">[${time}]</span>
                <span class="message-player">&lt;${this.escapeHtml(messageObj.playerId)}&gt;</span>
                <span class="message-content">${this.escapeHtml(messageObj.message)}</span>
            `;
        }
        
        this.chatMessages.appendChild(messageElement);
        
        // Animation d'apparition
        setTimeout(() => {
            messageElement.classList.add('show');
        }, 10);
    }
    
    scheduleMessageFade(messageId) {
        setTimeout(() => {
            if (!this.isOpen) {
                this.fadeMessage(messageId);
            }
        }, this.fadeTimeout);
    }
    
    fadeMessage(messageId) {
        const messageElement = this.chatMessages?.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            messageElement.classList.add('fading');
            setTimeout(() => {
                if (messageElement.parentNode && !this.isOpen) {
                    messageElement.parentNode.removeChild(messageElement);
                }
            }, 1000);
        }
    }
    
    showAllMessages() {
        if (!this.chatMessages) return;
        
        // Supprimer tous les messages affichés
        this.chatMessages.innerHTML = '';
        
        // Afficher tous les messages récents
        const recentMessages = this.messages.slice(-20); // 20 derniers messages
        recentMessages.forEach(message => {
            this.displayMessage(message);
        });
        
        this.scrollToBottom();
    }
    
    fadeMessages() {
        if (!this.chatMessages) return;
        
        const messages = this.chatMessages.querySelectorAll('.chat-message');
        messages.forEach(message => {
            message.classList.add('fading');
        });
        
        setTimeout(() => {
            if (!this.isOpen) {
                this.chatMessages.innerHTML = '';
            }
        }, 1000);
    }
    
    navigateHistory(direction) {
        if (this.messageHistory.length === 0) return;
        
        if (direction === -1) {
            // Vers le haut (message plus ancien)
            this.historyIndex = Math.min(this.historyIndex + 1, this.messageHistory.length - 1);
        } else {
            // Vers le bas (message plus récent)
            this.historyIndex = Math.max(this.historyIndex - 1, -1);
        }
        
        if (this.historyIndex === -1) {
            this.chatInput.value = '';
        } else {
            this.chatInput.value = this.messageHistory[this.historyIndex];
        }
    }
    
    autoComplete() {
        if (!this.chatInput) return;
        
        const currentText = this.chatInput.value;
        const words = currentText.split(' ');
        const lastWord = words[words.length - 1];
        
        if (lastWord.startsWith('@')) {
            // Auto-complétion des noms de joueurs
            const playerName = lastWord.substring(1);
            const players = this.getConnectedPlayers();
            const matches = players.filter(name => 
                name.toLowerCase().startsWith(playerName.toLowerCase())
            );
            
            if (matches.length === 1) {
                words[words.length - 1] = '@' + matches[0];
                this.chatInput.value = words.join(' ');
            }
        }
    }
    
    getConnectedPlayers() {
        // Récupérer la liste des joueurs connectés depuis le jeu
        if (window.game && window.game.clientWorld) {
            const players = Array.from(window.game.clientWorld.remotePlayers.keys());
            if (window.game.playerId) {
                players.push(window.game.playerId);
            }
            return players;
        }
        return [];
    }
    
    scrollToBottom() {
        if (this.chatMessages) {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }
    }
    
    // Filtrage et modération
    filterMessage(message) {
        // Filtrer les mots inappropriés (liste basique)
        const badWords = ['spam', 'hack', 'cheat']; // À étendre selon les besoins
        let filteredMessage = message;
        
        badWords.forEach(word => {
            const regex = new RegExp(word, 'gi');
            filteredMessage = filteredMessage.replace(regex, '*'.repeat(word.length));
        });
        
        return filteredMessage;
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Commandes de chat
    processCommand(message) {
        if (!message.startsWith('/')) return false;
        
        const parts = message.substring(1).split(' ');
        const command = parts[0].toLowerCase();
        const args = parts.slice(1);
        
        switch (command) {
            case 'help':
                this.addSystemMessage('Commandes disponibles: /help, /players, /time, /clear');
                break;
                
            case 'players':
                const players = this.getConnectedPlayers();
                this.addSystemMessage(`Joueurs connectés (${players.length}): ${players.join(', ')}`);
                break;
                
            case 'time':
                this.addSystemMessage(`Heure actuelle: ${new Date().toLocaleTimeString()}`);
                break;
                
            case 'clear':
                this.clearMessages();
                break;
                
            default:
                this.addSystemMessage(`Commande inconnue: /${command}`);
        }
        
        return true;
    }
    
    clearMessages() {
        this.messages = [];
        if (this.chatMessages) {
            this.chatMessages.innerHTML = '';
        }
        this.addSystemMessage('Chat effacé');
    }
    
    // Statistiques et debug
    getStats() {
        return {
            isOpen: this.isOpen,
            messageCount: this.messages.length,
            historyCount: this.messageHistory.length,
            maxMessages: this.maxMessages
        };
    }
    
    // Nettoyage
    dispose() {
        console.log('🧹 Nettoyage ChatManager...');
        
        // Nettoyer les event listeners
        if (this.chatInput) {
            this.chatInput.removeEventListener('keydown', this.handleKeyDown);
            this.chatInput.removeEventListener('blur', this.handleBlur);
        }
        
        // Nettoyer les données
        this.messages = [];
        this.messageHistory = [];
        
        // Fermer le chat
        this.close();
    }
}
